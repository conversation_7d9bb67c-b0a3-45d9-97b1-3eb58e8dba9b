# 订单退款数据计算功能说明

## 🎯 功能概述

新增了根据订单号计算退款数据的功能，用于分析每个支付流水释放的课时情况，为退款操作提供数据支持。

## 📋 核心功能

### 1. 接口定义

```java
/**
 * 计算订单退款数据
 * @param orderNo 订单号
 * @return 退款数据计算结果
 */
CourseHoursRefundCalculationResult calculateRefundData(String orderNo);
```

### 2. 返回数据结构

#### 主要字段
- **订单信息**：订单号、订单ID、课时包ID
- **金额信息**：订单总金额、已支付金额
- **课时信息**：总课时、已释放课时、剩余课时、已消耗课时、可退款课时
- **支付详情**：每个支付流水的释放情况

#### 支付流水释放详情
```java
public static class PaymentReleaseDetail {
    private String orderTrxId;              // 支付流水ID
    private Integer trxIdx;                 // 支付流水序号
    private Long paymentAmount;             // 支付金额(分)
    private BigDecimal paymentRatio;        // 支付比例
    private BigDecimal releasedPurchasedHours;  // 释放的购买课时数
    private BigDecimal releasedGiftHours;   // 释放的赠送课时数
    private BigDecimal totalReleasedHours;  // 释放的总课时数
    private String releaseType;             // 释放类型
    private String releaseTime;             // 释放时间
    private boolean refundable;             // 是否可退款
    private String remark;                  // 备注
}
```

## 🔧 计算逻辑

### 1. 数据收集
1. **查询订单信息**：根据订单号获取订单详情
2. **查询课时包**：获取订单关联的课时包信息
3. **查询支付流水**：获取所有已支付的交易流水
4. **查询释放记录**：获取所有课时释放历史记录

### 2. 数据计算
1. **基础统计**：
   - 订单总金额 vs 已支付金额
   - 课时包总课时 vs 已释放课时
   - 剩余可用课时 vs 已消耗课时

2. **可退款课时计算**：
   ```
   可退款课时 = 剩余课时 - 已消耗课时
   ```

3. **支付流水分析**：
   - 已释放流水：显示具体释放的课时数量和时间
   - 未释放流水：标记为不可退款

### 3. 退款可行性判断
- **已释放课时**：可以退款（需要回收课时）
- **未释放课时**：无法退款（本身就是冻结状态）
- **已消耗课时**：无法退款（已经使用）

## 📊 使用场景

### 1. 退款申请前评估
```java
// 获取退款数据
CourseHoursRefundCalculationResult result = courseHoursReleaseService.calculateRefundData(orderNo);

if (result.isSuccess()) {
    // 检查可退款课时
    BigDecimal refundableHours = result.getRefundableHours();
    
    // 分析每个支付流水的退款影响
    for (PaymentReleaseDetail detail : result.getPaymentReleaseDetails()) {
        if (detail.isRefundable()) {
            // 这笔支付可以退款，需要回收对应的课时
            System.out.println("支付流水 " + detail.getTrxIdx() + " 可退款，需回收课时：" + detail.getTotalReleasedHours());
        }
    }
}
```

### 2. 退款金额计算
```java
// 根据要退款的金额，计算影响的课时
Long refundAmount = 50000L; // 退款500元
Long totalPaidAmount = result.getTotalPaidAmount();

// 计算退款比例
BigDecimal refundRatio = new BigDecimal(refundAmount).divide(new BigDecimal(totalPaidAmount), 4, RoundingMode.HALF_UP);

// 计算需要回收的课时
BigDecimal hoursToReclaim = result.getTotalReleasedHours().multiply(refundRatio);
```

### 3. 分期退款处理
```java
// 按支付流水倒序退款（后进先出）
List<PaymentReleaseDetail> refundablePayments = result.getPaymentReleaseDetails().stream()
    .filter(PaymentReleaseDetail::isRefundable)
    .sorted((a, b) -> b.getTrxIdx().compareTo(a.getTrxIdx())) // 倒序
    .collect(Collectors.toList());

// 逐笔处理退款
for (PaymentReleaseDetail payment : refundablePayments) {
    if (remainingRefundAmount > 0) {
        // 处理这笔支付的退款
        processPaymentRefund(payment, remainingRefundAmount);
    }
}
```

## 🧪 测试接口

### 测试地址
```
GET /test/course-hours-release/refund-calculation/{orderNo}
```

### 测试示例
```bash
# 计算订单退款数据
curl -X GET "http://localhost:8080/test/course-hours-release/refund-calculation/ORDER123456"
```

### 返回示例
```json
{
  "success": true,
  "message": "退款数据计算成功",
  "orderNo": "ORDER123456",
  "orderId": "1234567890",
  "courseHoursId": "9876543210",
  "totalOrderAmount": 100000,
  "totalPaidAmount": 60000,
  "totalCourseHours": 12.00,
  "totalReleasedHours": 6.00,
  "remainingHours": 6.00,
  "consumedHours": 2.00,
  "refundableHours": 4.00,
  "paymentReleaseDetails": [
    {
      "orderTrxId": "trx001",
      "trxIdx": 1,
      "paymentAmount": 60000,
      "paymentRatio": 0.6000,
      "releasedPurchasedHours": 6.00,
      "releasedGiftHours": 0.00,
      "totalReleasedHours": 6.00,
      "releaseType": "部分释放",
      "releaseTime": "2025-01-08 14:30:00",
      "refundable": true,
      "remark": "第1期支付，已释放课时"
    },
    {
      "orderTrxId": "trx002",
      "trxIdx": 2,
      "paymentAmount": 40000,
      "paymentRatio": 0.4000,
      "releasedPurchasedHours": 0.00,
      "releasedGiftHours": 0.00,
      "totalReleasedHours": 0.00,
      "releaseType": "未释放",
      "releaseTime": null,
      "refundable": false,
      "remark": "第2期支付，未释放课时"
    }
  ]
}
```

## 💡 业务价值

1. **精确退款计算**：准确计算每笔支付对应的课时释放情况
2. **风险评估**：评估退款对课时包的影响
3. **数据透明**：提供完整的支付和课时释放链路
4. **决策支持**：为退款审批提供数据依据
5. **自动化基础**：为自动化退款流程提供数据支持

## 🔄 与退款流程的集成

这个计算功能可以与现有的退款流程集成：

1. **退款申请时**：调用此接口评估退款可行性
2. **退款审批时**：展示详细的课时影响分析
3. **退款执行时**：根据计算结果回收对应的课时
4. **退款完成后**：更新课时包状态和调整记录

---

**退款计算功能为课时包退款提供了完整的数据支持！** 🎉
