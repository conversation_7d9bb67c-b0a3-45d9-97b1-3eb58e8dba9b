# 订单多次付款课时生成功能需求

## 需求背景
用户订单支持多次付款，创建订单时不生成课时数据，只有在有付款时才根据支付比例生成对应的课时数据。

## 核心需求
1. 订单创建时不生成课时包数据
2. 提供一个刷新课时的方法给其他服务调用
3. 接受一个订单号参数
4. 判断订单号不同支付流水是否已经生成课时
5. 如果未生成，则按支付比例生成课时，增加到学生课时包中
6. 如果最后一次支付付款后已经是全付款，则生成所有剩余课时（包含赠送课时）

## 实现方案

### 1. 数据库表结构调整

#### 1.1 StudentCourseHours表增加字段
- `order_id` VARCHAR(64) - 关联的订单ID
- `order_trx_id` VARCHAR(64) - 关联的订单交易流水ID（记录是哪次支付生成的）

#### 1.2 新增课时生成记录表 StudentCourseHoursGeneration
- `id` VARCHAR(64) PRIMARY KEY - 主键ID
- `order_id` VARCHAR(64) - 订单ID
- `order_trx_id` VARCHAR(64) - 订单交易流水ID
- `student_id` VARCHAR(64) - 学生ID
- `course_hours_id` VARCHAR(64) - 生成的课时包ID
- `generated_purchased_hours` DECIMAL(10,2) - 生成的购买课时数
- `generated_gift_hours` DECIMAL(10,2) - 生成的赠送课时数
- `payment_ratio` DECIMAL(5,4) - 支付比例
- `payment_amount` BIGINT - 支付金额(分)
- `total_order_amount` BIGINT - 订单总金额(分)
- `generation_time` TIMESTAMP - 生成时间
- `generation_type` VARCHAR(20) - 生成类型：PARTIAL(部分支付生成)、FINAL(最终支付生成)
- `remark` TEXT - 备注
- 继承DataEntity基础字段

### 2. 核心服务接口设计

#### 2.1 课时生成服务接口
```java
public interface ICourseHoursGenerationService {
    /**
     * 刷新订单课时生成状态
     * @param orderNo 订单号
     * @return 生成结果
     */
    CourseHoursGenerationResult refreshCourseHoursGeneration(String orderNo);

    /**
     * 检查订单是否需要生成课时
     * @param orderNo 订单号
     * @return 是否需要生成
     */
    boolean needsGenerationCheck(String orderNo);
}
```

#### 2.2 生成结果DTO
```java
public class CourseHoursGenerationResult {
    private boolean success;
    private String message;
    private List<GenerationDetail> generationDetails;
    private BigDecimal totalGeneratedPurchasedHours;
    private BigDecimal totalGeneratedGiftHours;
}
```

### 3. 实现逻辑流程

#### 3.1 刷新课时生成主流程
1. 根据订单号查询订单信息和产品信息
2. 查询该订单的所有支付流水记录（状态为已支付）
3. 查询已有的课时生成记录，避免重复生成
4. 计算需要新生成的课时数量
5. 创建新的课时包记录
6. 记录课时生成历史

#### 3.2 课时生成计算逻辑
- **部分支付**：生成课时 = 总购买课时 × (当前支付金额 / 订单总金额)
- **最终支付**：生成所有剩余课时（包括赠送课时）
- **生成规则**：
  - 优先生成购买课时
  - 只有全额支付完成后才生成赠送课时
  - 每次支付生成一个独立的课时包记录

#### 3.3 课时包生成策略
- 每次支付生成独立的课时包记录，便于追溯
- 课时包的`batchNo`使用订单号+支付流水号格式
- 设置`orderTime`为支付时间，保证FIFO消费顺序

### 4. 集成点

#### 4.1 支付回调集成
在`AllinPayNotifyServiceImpl.handleSuccessPayment()`方法中调用课时生成服务

#### 4.2 订单创建逻辑调整
订单创建时不再创建课时包，只创建订单和支付流水

### 5. 异常处理
- 订单不存在
- 产品信息不完整
- 课时包创建失败
- 重复生成检查
- 数据一致性校验
- 支付流水状态异常

## 技术要点
1. 使用事务确保数据一致性
2. 支持幂等性操作，避免重复生成
3. 详细的日志记录便于问题排查
4. 支持回滚机制
5. 按需生成，避免资源浪费

## 实现优势
1. **简化逻辑**：无需处理冻结/解冻状态
2. **按需生成**：只有付款才生成课时，避免无效数据
3. **易于追溯**：每次支付对应独立的课时包记录
4. **数据一致性**：支付状态与课时状态完全对应
5. **性能优化**：减少不必要的数据创建和更新操作
