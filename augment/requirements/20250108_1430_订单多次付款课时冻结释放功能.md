# 订单多次付款课时释放功能需求

## 需求背景
用户订单支持多次付款，采用一个订单对应一个课时包的设计，订单创建时生成课时包但课时处于冻结状态，每次支付成功后按比例释放对应的课时。

## 核心需求
1. 订单创建时生成课时包，但课时处于冻结状态
2. 提供一个刷新课时的方法给其他服务调用
3. 接受一个订单号参数
4. 判断订单号不同支付流水是否已经释放课时
5. 如果未释放，则按支付比例释放冻结课时到可用课时
6. 如果最后一次支付付款后已经是全付款，则释放所有剩余冻结课时（包含赠送课时）

## 实现方案

### 1. 数据库表结构调整

#### 1.1 StudentCourseHours表增加字段
- `order_id` VARCHAR(64) - 关联的订单ID（一个课时包对应一个订单）
- `source_type` VARCHAR(20) - 课时来源类型：MANUAL(手动)、ORDER(订单)、TRIAL(试听课)、IMPORT(导入)、ADJUSTMENT(调整)
- `frozen_purchased_hours` DECIMAL(10,2) - 冻结的购买课时数
- `frozen_gift_hours` DECIMAL(10,2) - 冻结的赠送课时数

#### 1.2 新增课时释放记录表 StudentCourseHoursRelease
- `id` VARCHAR(64) PRIMARY KEY - 主键ID
- `course_hours_id` VARCHAR(64) - 课时包ID
- `order_id` VARCHAR(64) - 订单ID
- `order_trx_id` VARCHAR(64) - 订单交易流水ID
- `student_id` VARCHAR(64) - 学生ID
- `released_purchased_hours` DECIMAL(10,2) - 释放的购买课时数
- `released_gift_hours` DECIMAL(10,2) - 释放的赠送课时数
- `payment_ratio` DECIMAL(5,4) - 支付比例
- `payment_amount` BIGINT - 支付金额(分)
- `total_order_amount` BIGINT - 订单总金额(分)
- `release_time` TIMESTAMP - 释放时间
- `release_type` VARCHAR(20) - 释放类型：PARTIAL(部分释放)、FINAL(最终释放)
- `remark` TEXT - 备注
- 继承DataEntity基础字段

### 2. 核心服务接口设计

#### 2.1 课时释放服务接口
```java
public interface ICourseHoursReleaseService {
    /**
     * 刷新订单课时释放状态
     * @param orderNo 订单号
     * @return 释放结果
     */
    CourseHoursReleaseResult refreshCourseHoursRelease(String orderNo);

    /**
     * 检查订单是否需要释放课时
     * @param orderNo 订单号
     * @return 是否需要释放
     */
    boolean needsReleaseCheck(String orderNo);
}
```

#### 2.2 释放结果DTO
```java
public class CourseHoursReleaseResult {
    private boolean success;
    private String message;
    private List<ReleaseDetail> releaseDetails;
    private BigDecimal totalReleasedPurchasedHours;
    private BigDecimal totalReleasedGiftHours;
}
```

### 3. 实现逻辑流程

#### 3.1 刷新课时释放主流程
1. 根据订单号查询订单信息和产品信息
2. 查询该订单关联的课时包记录
3. 查询该订单的所有支付流水记录（状态为已支付）
4. 查询已有的课时释放记录，避免重复释放
5. 计算需要新释放的课时数量
6. 更新课时包的冻结课时和剩余课时
7. 记录课时释放历史

#### 3.2 课时释放计算逻辑
- **部分支付**：释放购买课时 = 总购买课时 × (当前支付金额 / 订单总金额)
- **最终支付**：释放所有剩余冻结课时（包括赠送课时）
- **释放规则**：
  - 优先释放购买课时
  - 只有全额支付完成后才释放赠送课时
  - 从冻结课时转移到剩余课时

#### 3.3 课时包管理策略
- 一个订单对应一个课时包记录
- 课时包的`batchNo`使用订单号格式
- 课时包创建时所有课时处于冻结状态
- 通过释放记录追溯每次支付的课时释放情况

### 4. 集成点

#### 4.1 支付回调集成
在`AllinPayNotifyServiceImpl.handleSuccessPayment()`方法中调用课时释放服务

#### 4.2 订单创建逻辑调整
订单创建时需要创建课时包，但所有课时设置为冻结状态

### 5. 异常处理
- 订单不存在
- 产品信息不完整
- 课时包不存在
- 重复释放检查
- 数据一致性校验
- 支付流水状态异常
- 冻结课时不足

## 技术要点
1. 使用事务确保数据一致性
2. 支持幂等性操作，避免重复释放
3. 详细的日志记录便于问题排查
4. 支持回滚机制
5. 冻结/释放状态管理

## 实现优势
1. **业务清晰**：一个订单对应一个课时包，关系明确
2. **状态管理**：通过冻结/释放状态精确控制课时可用性
3. **易于追溯**：每次支付的课时释放都有详细记录
4. **数据一致性**：支付状态与课时释放状态完全对应
5. **灵活控制**：支持部分支付部分释放的业务场景
