package org.nonamespace.word.thirdpart.esign.service;

import org.nonamespace.word.thirdpart.esign.exception.ESignException;
import org.nonamespace.word.thirdpart.esign.model.CreateFileByTemplateInput;
import org.nonamespace.word.thirdpart.esign.model.CreateFileByTemplateOutput;

public interface IESignContractsFileService {

    /**
     * 填写模板生成文件
     * 基于合同模板编号和模板中的控件来填充自定义的内容，最终生成一份pdf文件
     *
     * @param input 填写参数
     * @return 生成文件响应
     * @throws ESignException 异常
     */
    CreateFileByTemplateOutput createFileByTemplate(CreateFileByTemplateInput input) throws ESignException;

}
