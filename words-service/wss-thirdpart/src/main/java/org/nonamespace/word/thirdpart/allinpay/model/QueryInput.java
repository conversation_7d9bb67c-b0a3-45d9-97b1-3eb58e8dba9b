package org.nonamespace.word.thirdpart.allinpay.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig;
import org.nonamespace.word.thirdpart.allinpay.model.base.AllinPayInputBase;

/**
 * 通联支付统一查询接口输入参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryInput extends AllinPayInputBase {

    /**
     * 商户订单号
     */
    private String reqsn;

    /**
     * 平台交易流水
     */
    private String trxid;

    /**
     * 版本号
     */
    private String version = "11";

    /**
     * 随机字符串
     */
    private String randomstr;

    public QueryInput() {
        super(null);
    }

    public QueryInput(AllinPayConfig config) {
        super(config);
    }

    /**
     * 验证必填参数
     */
    public boolean isValid() {
        return getCusid() != null && getAppid() != null;
    }

}