package org.nonamespace.word.thirdpart.esign.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * e签宝配置
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "esign")
public class ESignConfig {

    /**
     * e签宝API基础地址
     */
    private String baseUrl;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 60000;

    /**
     * 是否启用
     */
    private boolean enabled = true;

    /**
     * 合同模板ID
     */
    private String docTemplateId;

    private String sdkVersion;
}