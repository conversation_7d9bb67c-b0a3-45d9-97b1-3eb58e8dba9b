package org.nonamespace.word.thirdpart.allinpay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig;
import org.nonamespace.word.thirdpart.allinpay.constants.AllinPayConstants;
import org.nonamespace.word.thirdpart.allinpay.dto.PayDto;
import org.nonamespace.word.thirdpart.allinpay.dto.RefundDto;
import org.nonamespace.word.thirdpart.allinpay.exception.AllinPayException;
import org.nonamespace.word.thirdpart.allinpay.model.*;
import org.nonamespace.word.thirdpart.allinpay.service.IAllinPayService;
import org.nonamespace.word.thirdpart.allinpay.service.IAllinpayApiRecordsService;
import org.nonamespace.word.thirdpart.allinpay.util.AllinPayHttpUtil;
import org.nonamespace.word.thirdpart.allinpay.util.AllinPaySignUtil;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * 通联统一主扫支付服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AllinPayServiceImpl implements IAllinPayService {

    private final AllinPayConfig config;
    private final StringRedisTemplate stringRedisTemplate;
    private final IAllinpayApiRecordsService allinpayApiRecordsService;


    private static final String ORDER_STATUS_REDIS_KEY = "order:locked:%s:%s";

    @Override
    public NativePayOutput nativepay(PayDto.NativePayReq req) throws AllinPayException.ProcessingException {
        String key = String.format(ORDER_STATUS_REDIS_KEY, req.getCusTrxSeq(), AllinPayConstants.AllinPayType.NATIVE_PAY);
        if(stringRedisTemplate.hasKey(key)) {
            throw new AllinPayException.ProcessingException("当前请求正在处理中", req.getCusTrxSeq());
        }
        stringRedisTemplate.opsForValue().set(key, DateUtil.now());

        try {
            // 单位分钟，当前时间累加分钟数
            String expiretime = DateUtil.format(DateUtil.offsetMinute(DateUtil.date(), config.getValidTime()), "yyyyMMddHHmmss");

            NativePayInput input = new NativePayInput(config);
            input.setTrxamt(req.getTrxamt())
                    .setReqsn(req.getCusTrxSeq())
                    .setBody(req.getBody())
                    .setRemark(req.getRemark())
                    .setRandomstr(req.getCusTrxSeq())
                    .setExpiretime(expiretime)
                    .setNotify_url(config.getNotifyUrl())
                    .setFront_url(config.getFrontUrl())
            ;
            return this.executeNativepay(input);
        } finally {
            stringRedisTemplate.delete(key);
        }
    }


    @Override
    public CancelOutput cancel(RefundDto.Req req) throws AllinPayException.ProcessingException {
        String key = String.format(ORDER_STATUS_REDIS_KEY, req.getRefundTrxId(), AllinPayConstants.AllinPayType.CANCEL);
        if(stringRedisTemplate.hasKey(key)) {
            throw new AllinPayException.ProcessingException("当前请求正在处理中", req.getRefundTrxId());
        }
        stringRedisTemplate.opsForValue().set(key, DateUtil.now());

        try {
            // todo... 这里获取订单信息
            CancelInput input = new CancelInput(config);
            input.setReqsn(req.getRefundTrxId())
                    .setTrxamt(req.getAmount())
                    .setOldreqsn(req.getOriginalTrxId())
                    .setRandomstr(req.getRefundTrxId());

            return this.executeCancel(input);
        } finally {
            stringRedisTemplate.delete(key);
        }
    }

    @Override
    public RefundOutput refund(RefundDto.Req req) throws AllinPayException.ProcessingException {
        String key = String.format(ORDER_STATUS_REDIS_KEY,req.getRefundTrxId(), AllinPayConstants.AllinPayType.REFUND);
        if(stringRedisTemplate.hasKey(key)) {
            throw new AllinPayException.ProcessingException("当前请求正在处理中", req.getRefundTrxId());
        }
        stringRedisTemplate.opsForValue().set(key, DateUtil.now());

        try {
            RefundInput input = new RefundInput(config);
            input.setReqsn(req.getRefundTrxId()).setRemark(req.getRemark())
                    .setTrxamt(req.getAmount())
                    .setOldreqsn(req.getOriginalTrxId())
                    .setRandomstr(req.getRefundTrxId());
            return this.executeRefund(input);
        } finally {
            stringRedisTemplate.delete(key);
        }
    }

    @Override
    public QueryOutput query(PayDto.QueryReq req) throws AllinPayException.ProcessingException {
        // 查询接口不需要幂等性控制，因为查询操作是幂等的
        
        // 参数校验：reqsn和trxid必须至少有一个
        if (StrUtil.isBlank(req.getReqsn()) && StrUtil.isBlank(req.getTrxid())) {
            throw new AllinPayException.ProcessingException("商户订单号和平台交易流水至少需要提供一个", null);
        }
        
        QueryInput input = new QueryInput(config);
        input.setReqsn(req.getReqsn());
        input.setTrxid(req.getTrxid());
        input.setRandomstr(StrUtil.uuid().replace("-", "").substring(0, 16));
        
        return this.executeQuery(input);
    }

    @Override
    public CloseOutput close(PayDto.CloseReq req) throws AllinPayException.ProcessingException {
        String key = String.format(ORDER_STATUS_REDIS_KEY, 
            StrUtil.isNotBlank(req.getReqsn()) ? req.getReqsn() : req.getTrxid(), 
            AllinPayConstants.AllinPayType.CLOSE);
        if(stringRedisTemplate.hasKey(key)) {
            throw new AllinPayException.ProcessingException("当前请求正在处理中", 
                StrUtil.isNotBlank(req.getReqsn()) ? req.getReqsn() : req.getTrxid());
        }
        stringRedisTemplate.opsForValue().set(key, DateUtil.now());

        try {
            // 参数校验：reqsn和trxid必须至少有一个
            if (StrUtil.isBlank(req.getReqsn()) && StrUtil.isBlank(req.getTrxid())) {
                throw new AllinPayException.ProcessingException("商户订单号和平台交易流水至少需要提供一个", null);
            }
            
            CloseInput input = new CloseInput(config);
            input.setReqsn(req.getReqsn());
            input.setTrxid(req.getTrxid());
            input.setRandomstr(StrUtil.uuid().replace("-", "").substring(0, 16));
            
            return this.executeClose(input);
        } finally {
            stringRedisTemplate.delete(key);
        }
    }

    /**
     * 执行支付请求
     * 
     * @param input 支付请求参数
     * @return 支付响应结果
     */
    private NativePayOutput executeNativepay(NativePayInput input) {
        Map<String, Object> requestParams = null;
        Map<String, Object> responseParams = null;
        
        try {
            // 1. 参数预处理
            preprocessPayInput(input);
            
            // 2. 构建请求参数Map
            requestParams = AllinPaySignUtil.objectToMap(input);
            
            // 3. 生成签名
            String sign = AllinPaySignUtil.sign(requestParams, config.getPrivateKey());
            requestParams.put("sign", sign);
            log.info("[统一主扫支付] - 支付参数：{}", requestParams);

            // 4. 发送HTTP请求
            String response = AllinPayHttpUtil.doPost(
                    config.getBaseUrl() + "/apiweb/unitorder/nativepay",
                requestParams, 
                config.getConnectTimeout(), 
                config.getReadTimeout()
            );
            log.info("[统一主扫支付] - 接口返回：{}", response);
            
            // 5. 解析响应
            NativePayOutput payOutput = JSONUtil.toBean(response, NativePayOutput.class);
            Objects.requireNonNull(payOutput, "[统一主扫支付] - 支付接口返回为空，请确认");
            
            // 6. 构建响应参数Map用于记录
            responseParams = BeanUtil.beanToMap(payOutput, false, true);

            if(!payOutput.isSuccess()) {
                throw new IOException(payOutput.getRetmsg());
            }
            
            // 7. 验证响应签名
            String responseSign = payOutput.getSign();
            if (responseSign != null && config.getPublicKey() != null) {
                Map<String, Object> verifyParams = BeanUtil.beanToMap(payOutput,false, true).entrySet().stream()
                    .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey, Map.Entry::getValue
                    ));
                
                boolean signValid = AllinPaySignUtil.verifySign(verifyParams, responseSign, config.getPublicKey());
                if (!signValid) {
                    throw new RuntimeException("响应签名验证失败");
                }
            }
            
            // 8. 记录API调用
            try {
                allinpayApiRecordsService.recordApiCall(
                    AllinPayConstants.AllinPayType.NATIVE_PAY,
                    input.getCusid(),
                    input.getAppid(),
                    input.getReqsn(),
                    requestParams,
                    responseParams
                );
            } catch (Exception e) {
                log.warn("记录支付API调用失败", e);
            }
            
            return payOutput;
            
        } catch (Exception e) {
            // 记录失败的API调用
            if (requestParams != null) {
                try {
                    if (responseParams == null) {
                        responseParams = Map.of("error", e.getMessage());
                    }
                    allinpayApiRecordsService.recordApiCall(
                        AllinPayConstants.AllinPayType.NATIVE_PAY,
                        input.getCusid(),
                        input.getAppid(),
                        input.getReqsn(),
                        requestParams,
                        responseParams
                    );
                } catch (Exception recordException) {
                    log.warn("记录失败的支付API调用失败", recordException);
                }
            }
            throw new RuntimeException("[统一主扫支付] - 请求异常", e);
        }
    }


    /**
     * 执行撤销请求
     * 
     * @param input 撤销请求参数
     * @return 撤销响应结果
     */
    private CancelOutput executeCancel(CancelInput input) {
        Map<String, Object> requestParams = null;
        Map<String, Object> responseParams = null;
        
        try {
            // 1. 参数预处理
            preprocessCancelInput(input);
            
            // 2. 构建请求参数Map
            requestParams = AllinPaySignUtil.objectToMap(input);
            
            // 3. 生成签名
            String sign = AllinPaySignUtil.sign(requestParams, config.getPrivateKey());
            requestParams.put("sign", sign);
            log.info("[ 统一撤销 ] - 请求参数：{}" , requestParams);
            
            // 4. 确定请求地址
            String apiUrl = config.getBaseUrl() + "/apiweb/tranx/cancel";
            
            // 5. 发送HTTP请求
            String response = AllinPayHttpUtil.doPost(
                apiUrl, 
                requestParams, 
                config.getConnectTimeout(), 
                config.getReadTimeout()
            );
            log.info("[ 统一撤销 ] - 接口返回：{}" , response);
            
            // 6. 解析响应
            CancelOutput cancelOutput = JSONUtil.toBean(response, CancelOutput.class);
            Objects.requireNonNull(cancelOutput, "[统一撤销] - 接口返回为空，请确认");
            
            // 7. 构建响应参数Map用于记录
            responseParams = BeanUtil.beanToMap(cancelOutput, false, true);

            if(!cancelOutput.isSuccess()) {
                throw new IOException(cancelOutput.getRetmsg());
            }
            
            // 8. 验证响应签名
            String responseSign = cancelOutput.getSign();
            if (responseSign != null && config.getPublicKey() != null) {
                Map<String, Object> verifyParams = BeanUtil.beanToMap(cancelOutput,false, true).entrySet().stream()
                    .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey, Map.Entry::getValue
                    ));
                
                boolean signValid = AllinPaySignUtil.verifySign(verifyParams, responseSign, config.getPublicKey());
                if (!signValid) {
                    throw new RuntimeException("响应签名验证失败");
                }
            }
            
            // 9. 记录API调用
            try {
                allinpayApiRecordsService.recordApiCall(
                    AllinPayConstants.AllinPayType.CANCEL,
                    input.getCusid(),
                    input.getAppid(),
                    input.getOldreqsn(),
                    requestParams,
                    responseParams
                );
            } catch (Exception e) {
                log.warn("记录撤销API调用失败", e);
            }
            
            // 10. 构建返回对象
            return cancelOutput;
            
        } catch (Exception e) {
            // 记录失败的API调用
            if (requestParams != null) {
                try {
                    if (responseParams == null) {
                        responseParams = Map.of("error", e.getMessage());
                    }
                    allinpayApiRecordsService.recordApiCall(
                        AllinPayConstants.AllinPayType.CANCEL,
                        input.getCusid(),
                        input.getAppid(),
                        input.getOldreqsn(),
                        requestParams,
                        responseParams
                    );
                } catch (Exception recordException) {
                    log.warn("记录失败的撤销API调用失败", recordException);
                }
            }
            throw new IllegalCallerException("撤销请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行退款请求
     * @param refundInput 退款请求参数
     * @return 退款响应结果
     */
    private RefundOutput executeRefund(RefundInput refundInput) {
        Map<String, Object> requestParams = null;
        Map<String, Object> responseParams = null;
        
        try {
            // 1. 参数预处理
            preprocessRefundInput(refundInput);
            refundInput.setNotify_url(config.getRefundNotifyUrl());
            
            // 2. 构建请求参数Map
            requestParams = AllinPaySignUtil.objectToMap(refundInput);
            
            // 3. 生成签名
            String sign = AllinPaySignUtil.sign(requestParams, config.getPrivateKey());
            requestParams.put("sign", sign);
            log.info("[ 统一退款 ] - 请求参数：{}" , requestParams);
            
            // 4. 确定请求地址
            String apiUrl = config.getBaseUrl() + "/apiweb/tranx/refund";
            
            // 5. 发送HTTP请求
            String response = AllinPayHttpUtil.doPost(
                apiUrl, 
                requestParams, 
                config.getConnectTimeout(), 
                config.getReadTimeout()
            );
            log.info("[ 统一退款 ] - 接口返回：{}" , response);
            
            // 6. 解析响应
            RefundOutput refundOutput = JSONUtil.toBean(response, RefundOutput.class);
            Objects.requireNonNull(refundOutput, "[ 统一退款 ] - 接口返回为空，请确认");
            
            // 7. 构建响应参数Map用于记录
            responseParams = BeanUtil.beanToMap(refundOutput, false, true);

            if(!refundOutput.isSuccess()) {
                throw new IOException(refundOutput.getRetmsg());
            }

            // 8. 验证响应签名
            String responseSign = refundOutput.getSign();
            if (responseSign != null && config.getPublicKey() != null) {
                Map<String, Object> verifyParams = BeanUtil.beanToMap(refundOutput, false, true).entrySet().stream()
                    .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey, Map.Entry::getValue
                    ));
                
                boolean signValid = AllinPaySignUtil.verifySign(verifyParams, responseSign, config.getPublicKey());
                if (!signValid) {
                    throw new RuntimeException("响应签名验证失败");
                }
            }
            
            // 9. 记录API调用
            try {
                allinpayApiRecordsService.recordApiCall(
                    AllinPayConstants.AllinPayType.REFUND,
                    refundInput.getCusid(),
                    refundInput.getAppid(),
                    refundInput.getReqsn(),
                    requestParams,
                    responseParams
                );
            } catch (Exception e) {
                log.warn("记录退款API调用失败", e);
            }
            
            // 10. 构建返回对象
            return refundOutput;
            
        } catch (Exception e) {
            // 记录失败的API调用
            if (requestParams != null) {
                try {
                    if (responseParams == null) {
                        responseParams = Map.of("error", e.getMessage());
                    }
                    allinpayApiRecordsService.recordApiCall(
                        AllinPayConstants.AllinPayType.REFUND,
                        refundInput.getCusid(),
                        refundInput.getAppid(),
                        refundInput.getReqsn(),
                        requestParams,
                        responseParams
                    );
                } catch (Exception recordException) {
                    log.warn("记录失败的退款API调用失败", recordException);
                }
            }
            throw new IllegalCallerException("退款请求失败: " + e.getMessage(), e);
        }
    }


    /**
     * 预处理输入参数
     */
    private void preprocessPayInput(NativePayInput input) {
        // 验证订单号
        if (input.getReqsn().isEmpty()) {
            throw new IllegalArgumentException("商户订单号不能为空");
        }
        if(!input.isValid()) {
            throw new IllegalArgumentException("支付必填参数有误，请重新确认");
        }
        // 生成随机字符串
        if (input.getRandomstr().isEmpty()) {
            input.setRandomstr(AllinPaySignUtil.generateRandomString(32));
        }
    }

    /**
     * 预处理退款输入参数
     */
    private void preprocessRefundInput(RefundInput input) {
        // 验证订单号
        if (StrUtil.isBlankIfStr(input.getOldreqsn()) && StrUtil.isBlankIfStr(input.getOldtrxid())) {
            throw new IllegalArgumentException("原交易单号和原交易流水不能同时为空");
        }
        if(!input.isValid()) {
            throw new IllegalArgumentException("支付必填参数有误，请重新确认");
        }
        // 生成随机字符串
        if (input.getRandomstr().isEmpty()) {
            input.setRandomstr(AllinPaySignUtil.generateRandomString(32));
        }
    }

    /**
     * 预处理撤销输入参数
     */
    private void preprocessCancelInput(CancelInput input) {
        // 验证订单号
        if (StrUtil.isBlankIfStr(input.getOldreqsn()) && StrUtil.isBlankIfStr(input.getOldtrxid())) {
            throw new IllegalArgumentException("原交易单号和原交易流水不能同时为空");
        }
        if(!input.isValid()) {
            throw new IllegalArgumentException("支付必填参数有误，请重新确认");
        }
        // 生成随机字符串
        if (input.getRandomstr().isEmpty()) {
            input.setRandomstr(AllinPaySignUtil.generateRandomString(32));
        }
    }

    /**
     * 执行查询请求
     * 
     * @param input 查询请求参数
     * @return 查询响应结果
     */
    private QueryOutput executeQuery(QueryInput input) {
        Map<String, Object> requestParams = null;
        Map<String, Object> responseParams = null;
        
        try {
            // 1. 参数预处理
            preprocessQueryInput(input);
            
            // 2. 构建请求参数Map
            requestParams = AllinPaySignUtil.objectToMap(input);
            
            // 3. 生成签名
            String sign = AllinPaySignUtil.sign(requestParams, config.getPrivateKey());
            requestParams.put("sign", sign);
            log.info("[统一查询] - 请求参数：{}", requestParams);
            
            // 4. 发送HTTP请求
            String response = AllinPayHttpUtil.doPost(
                    config.getBaseUrl() + "/apiweb/tranx/query",
                requestParams, 
                config.getConnectTimeout(), 
                config.getReadTimeout()
            );
            log.info("[统一查询] - 接口返回：{}", response);
            
            // 5. 解析响应
            QueryOutput queryOutput = JSONUtil.toBean(response, QueryOutput.class);
            Objects.requireNonNull(queryOutput, "[统一查询] - 查询接口返回为空，请确认");
            
            // 6. 构建响应参数Map用于记录
            responseParams = BeanUtil.beanToMap(queryOutput, false, true);

            if(!queryOutput.isSuccess()) {
                throw new IOException(queryOutput.getRetmsg());
            }
            
            // 7. 验证响应签名
            String responseSign = queryOutput.getSign();
            if (responseSign != null && config.getPublicKey() != null) {
                Map<String, Object> verifyParams = BeanUtil.beanToMap(queryOutput,false, true).entrySet().stream()
                    .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey, Map.Entry::getValue
                    ));
                
                boolean signValid = AllinPaySignUtil.verifySign(verifyParams, responseSign, config.getPublicKey());
                if (!signValid) {
                    throw new RuntimeException("响应签名验证失败");
                }
            }
            
            // 8. 记录API调用
            try {
                allinpayApiRecordsService.recordApiCall(
                    AllinPayConstants.AllinPayType.QUERY,
                    input.getCusid(),
                    input.getAppid(),
                    input.getReqsn(),
                    requestParams,
                    responseParams
                );
            } catch (Exception e) {
                log.warn("记录查询API调用失败", e);
            }
            
            return queryOutput;
            
        } catch (Exception e) {
            // 记录失败的API调用
            if (requestParams != null) {
                try {
                    if (responseParams == null) {
                        responseParams = Map.of("error", e.getMessage());
                    }
                    allinpayApiRecordsService.recordApiCall(
                        AllinPayConstants.AllinPayType.QUERY,
                        input.getCusid(),
                        input.getAppid(),
                        input.getReqsn(),
                        requestParams,
                        responseParams
                    );
                } catch (Exception recordException) {
                    log.warn("记录失败的查询API调用失败", recordException);
                }
            }
            throw new RuntimeException("[统一查询] - 请求异常", e);
        }
    }

    /**
     * 预处理查询输入参数
     */
    private void preprocessQueryInput(QueryInput input) {
        // 验证订单号或交易流水
        if (StrUtil.isBlank(input.getReqsn()) && StrUtil.isBlank(input.getTrxid())) {
            throw new IllegalArgumentException("商户订单号和平台交易流水至少需要提供一个");
        }
        if(!input.isValid()) {
            throw new IllegalArgumentException("查询必填参数有误，请重新确认");
        }
        // 生成随机字符串
        if (StrUtil.isBlank(input.getRandomstr())) {
            input.setRandomstr(AllinPaySignUtil.generateRandomString(32));
        }
    }

    /**
     * 执行订单关闭请求
     * 
     * @param input 订单关闭请求参数
     * @return 订单关闭响应结果
     */
    private CloseOutput executeClose(CloseInput input) {
        Map<String, Object> requestParams = null;
        Map<String, Object> responseParams = null;
        
        try {
            // 1. 参数预处理
            preprocessCloseInput(input);
            
            // 2. 构建请求参数Map
            requestParams = AllinPaySignUtil.objectToMap(input);
            
            // 3. 生成签名
            String sign = AllinPaySignUtil.sign(requestParams, config.getPrivateKey());
            requestParams.put("sign", sign);
            log.info("[统一订单关闭] - 请求参数：{}", requestParams);
            
            // 4. 发送HTTP请求
            String response = AllinPayHttpUtil.doPost(
                    config.getBaseUrl() + "/apiweb/unitorder/close",
                requestParams, 
                config.getConnectTimeout(), 
                config.getReadTimeout()
            );
            log.info("[统一订单关闭] - 接口返回：{}", response);
            
            // 5. 解析响应
            CloseOutput closeOutput = JSONUtil.toBean(response, CloseOutput.class);
            Objects.requireNonNull(closeOutput, "[统一订单关闭] - 关闭接口返回为空，请确认");
            
            // 6. 构建响应参数Map用于记录
            responseParams = BeanUtil.beanToMap(closeOutput, false, true);

            if(!closeOutput.isSuccess()) {
                throw new IOException(closeOutput.getRetmsg());
            }
            
            // 7. 验证响应签名
            String responseSign = closeOutput.getSign();
            if (responseSign != null && config.getPublicKey() != null) {
                Map<String, Object> verifyParams = BeanUtil.beanToMap(closeOutput,false, true).entrySet().stream()
                    .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey, Map.Entry::getValue
                    ));
                
                boolean signValid = AllinPaySignUtil.verifySign(verifyParams, responseSign, config.getPublicKey());
                if (!signValid) {
                    throw new RuntimeException("响应签名验证失败");
                }
            }
            
            // 8. 记录API调用
            try {
                allinpayApiRecordsService.recordApiCall(
                    AllinPayConstants.AllinPayType.CLOSE,
                    input.getCusid(),
                    input.getAppid(),
                    input.getReqsn(),
                    requestParams,
                    responseParams
                );
            } catch (Exception e) {
                log.warn("记录订单关闭API调用失败", e);
            }
            
            return closeOutput;
            
        } catch (Exception e) {
            // 记录失败的API调用
            if (requestParams != null) {
                try {
                    if (responseParams == null) {
                        responseParams = Map.of("error", e.getMessage());
                    }
                    allinpayApiRecordsService.recordApiCall(
                        AllinPayConstants.AllinPayType.CLOSE,
                        input.getCusid(),
                        input.getAppid(),
                        input.getReqsn(),
                        requestParams,
                        responseParams
                    );
                } catch (Exception recordException) {
                    log.warn("记录失败的订单关闭API调用失败", recordException);
                }
            }
            throw new RuntimeException("[统一订单关闭] - 请求异常", e);
        }
    }

    /**
     * 预处理订单关闭输入参数
     */
    private void preprocessCloseInput(CloseInput input) {
        // 验证订单号或交易流水
        if (StrUtil.isBlank(input.getReqsn()) && StrUtil.isBlank(input.getTrxid())) {
            throw new IllegalArgumentException("商户订单号和平台交易流水至少需要提供一个");
        }
        if(!input.isValid()) {
            throw new IllegalArgumentException("订单关闭必填参数有误，请重新确认");
        }
        // 生成随机字符串
        if (StrUtil.isBlank(input.getRandomstr())) {
            input.setRandomstr(AllinPaySignUtil.generateRandomString(32));
        }
    }
}