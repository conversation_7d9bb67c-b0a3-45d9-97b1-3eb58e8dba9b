package org.nonamespace.word.thirdpart.allinpay.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.thirdpart.allinpay.model.base.AllinPayOutputBase;

/**
 * 统一下单API响应结果
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NativePayOutput extends AllinPayOutputBase {

    /**
     * 商户号
     */
    private String cusid;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 商户交易单号
     */
    private String reqsn;

    /**
     * 通联交易单号
     */
    private String trxid;

    /**
     * 渠道商户号
     */
    private String chnltrxid;

    /**
     * 随机字符串
     */
    private String randomstr;

    /**
     * 交易金额（分）
     */
    private Integer trxamt;


    /**
     * 交易完成时间
     * yyyyMMddHHmmss
     */
    private String fintime;

    /**
     * 二维码串或支付链接
     * 扫码支付时返回二维码串
     * JS支付时返回支付链接
     */
    private String payinfo;

    /**
     * 错误代码
     */
    private String errmsg;

    /**
     * 签名
     */
    private String sign;

}