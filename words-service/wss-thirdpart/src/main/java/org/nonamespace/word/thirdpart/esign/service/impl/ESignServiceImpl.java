//package org.nonamespace.word.thirdpart.esign.service.impl;
//
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.json.JSONUtil;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.nonamespace.word.thirdpart.esign.config.ESignConfig;
//import org.nonamespace.word.thirdpart.esign.constants.ESignConstants;
//import org.nonamespace.word.thirdpart.esign.exception.ESignException;
//import org.nonamespace.word.thirdpart.esign.model.*;
//import org.nonamespace.word.thirdpart.esign.service.IESignService;
//import org.nonamespace.word.thirdpart.esign.util.ESignHttpUtil;
//import org.springframework.stereotype.Service;
//
///**
// * e签宝服务实现类
// * <AUTHOR>
// */
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class ESignServiceImpl implements IESignService {
//
//    private final ESignConfig eSignConfig;
//
//
//    @Override
//    public CreateFileByTemplateOutput createFileByTemplate(CreateFileByTemplateInput input) throws ESignException {
//        log.info("开始填写模板生成文件，模板ID: {}, 文件名: {}", input.getDocTemplateId(), input.getFileName());
//        
//        try {
//            // 参数校验
//            validateCreateFileByTemplateInput(input);
//            
//            // 构建请求URL
//            String url = ESignHttpUtil.buildUrl(eSignConfig, ESignConstants.ApiPath.CREATE_FILE_BY_TEMPLATE);
//            
//            // 发送POST请求
//            String requestBody = JSONUtil.toJsonStr(input);
//            CreateFileByTemplateOutput response = ESignHttpUtil.sendPostRequest(
//                    eSignConfig, url, requestBody, CreateFileByTemplateOutput.class);
//            
//            if (response == null) {
//                throw new ESignException.ProcessingException("接口响应为空");
//            }
//            
//            if (!response.isSuccess()) {
//                log.error("填写模板生成文件失败: {}", response.getErrorMessage());
//                throw new ESignException.ProcessingException(
//                        response.getCode() != null ? response.getCode() : -1,
//                        response.getMessage() != null ? response.getMessage() : "未知错误"
//                );
//            }
//            
//            log.info("填写模板生成文件成功，模板ID: {}, 文件ID: {}, 文件名: {}", 
//                    input.getDocTemplateId(), response.getFileId(), input.getFileName());
//            
//            return response;
//            
//        } catch (ESignException e) {
//            log.error("填写模板生成文件异常，模板ID: {}, 错误: {}", input.getDocTemplateId(), e.getMessage());
//            throw e;
//        } catch (Exception e) {
//            log.error("填写模板生成文件系统异常，模板ID: {}", input.getDocTemplateId(), e);
//            throw new ESignException.ProcessingException("系统异常: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 校验填写模板生成文件的输入参数
//     * @param input 输入参数
//     * @throws ESignException.ParameterException 参数异常
//     */
//    private void validateCreateFileByTemplateInput(CreateFileByTemplateInput input) throws ESignException.ParameterException {
//        if (input == null) {
//            throw new ESignException.ParameterException("请求参数不能为空");
//        }
//        
//        if (StrUtil.isBlank(input.getDocTemplateId())) {
//            throw new ESignException.ParameterException("合同模板ID不能为空");
//        }
//        
//        if (StrUtil.isBlank(input.getFileName())) {
//            throw new ESignException.ParameterException("文件名称不能为空");
//        }
//        
//        // 检查文件名长度
//        if (input.getFileName().length() > 100) {
//            throw new ESignException.ParameterException("文件名称长度不能超过100字符");
//        }
//        
//        // 检查文件名特殊字符
//        String fileName = input.getFileName();
//        String[] forbiddenChars = {"/", "\\", ":", "*", "\"", "<", ">", "|", "？"};
//        for (String forbiddenChar : forbiddenChars) {
//            if (fileName.contains(forbiddenChar)) {
//                throw new ESignException.ParameterException("文件名称不能包含特殊字符: " + forbiddenChar);
//            }
//        }
//        
//        if (input.getComponents() == null) {
//            throw new ESignException.ParameterException("控件列表不能为空");
//        }
//        
//        // 校验控件信息
//        for (CreateFileByTemplateInput.ComponentInfo component : input.getComponents()) {
//            if (StrUtil.isBlank(component.getComponentId()) && StrUtil.isBlank(component.getComponentKey())) {
//                throw new ESignException.ParameterException("控件ID和控件Key至少需要传入一个");
//            }
//        }
//        
//        // 检查配置
//        if (!eSignConfig.isEnabled()) {
//            throw new ESignException.ParameterException("e签宝服务未启用");
//        }
//        
//        if (StrUtil.isBlank(eSignConfig.getBaseUrl())) {
//            throw new ESignException.ParameterException("e签宝baseUrl未配置");
//        }
//        
//        if (StrUtil.isBlank(eSignConfig.getAppId())) {
//            throw new ESignException.ParameterException("e签宝appId未配置");
//        }
//        
//        if (StrUtil.isBlank(eSignConfig.getAppSecret())) {
//            throw new ESignException.ParameterException("e签宝appSecret未配置");
//        }
//    }
//
//    @Override
//    public CreateSignFlowByFileOutput createSignFlowByFile(CreateSignFlowByFileInput input) throws ESignException {
//        log.info("开始基于文件发起签署，流程主题: {}", 
//                input.getSignFlowConfig() != null ? input.getSignFlowConfig().getSignFlowTitle() : "未设置");
//        
//        try {
//            // 参数校验
//            validateCreateSignFlowByFileInput(input);
//            
//            // 构建请求URL
//            String url = ESignHttpUtil.buildUrl(eSignConfig, ESignConstants.ApiPath.CREATE_SIGN_FLOW_BY_FILE);
//            
//            // 发送POST请求
//            String requestBody = JSONUtil.toJsonStr(input);
//            CreateSignFlowByFileOutput response = ESignHttpUtil.sendPostRequest(
//                    eSignConfig, url, requestBody, CreateSignFlowByFileOutput.class);
//            
//            if (response == null) {
//                throw new ESignException.ProcessingException("接口响应为空");
//            }
//            
//            if (!response.isSuccess()) {
//                log.error("基于文件发起签署失败: {}", response.getErrorMessage());
//                throw new ESignException.ProcessingException("基于文件发起签署失败: " + response.getErrorMessage());
//            }
//            
//            log.info("基于文件发起签署成功，签署流程ID: {}, 状态: {}", 
//                    response.getData().getSignFlowId(), response.getCode());
//            
//            return response;
//            
//        } catch (ESignException e) {
//            log.error("基于文件发起签署异常，错误: {}", e.getMessage());
//            throw e;
//        } catch (Exception e) {
//            log.error("基于文件发起签署系统异常", e);
//            throw new ESignException.ProcessingException("基于文件发起签署系统异常: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 校验基于文件发起签署输入参数
//     * @param input 输入参数
//     * @throws ESignException.ParameterException 参数异常
//     */
//    private void validateCreateSignFlowByFileInput(CreateSignFlowByFileInput input) throws ESignException.ParameterException {
//        if (input == null) {
//            throw new ESignException.ParameterException("请求参数不能为空");
//        }
//        
//        // 校验签署流程配置
//        if (input.getSignFlowConfig() == null) {
//            throw new ESignException.ParameterException("签署流程配置不能为空");
//        }
//        
//        if (StrUtil.isBlank(input.getSignFlowConfig().getSignFlowTitle())) {
//            throw new ESignException.ParameterException("签署流程主题不能为空");
//        }
//        
//        // 校验待签署文件信息（如果提供了文件信息）
//        if (input.getDocs() != null && !input.getDocs().isEmpty()) {
//            for (CreateSignFlowByFileInput.DocInfo doc : input.getDocs()) {
//                if (StrUtil.isBlank(doc.getFileId())) {
//                    throw new ESignException.ParameterException("待签署文件ID不能为空");
//                }
//            }
//        }
//        
//        // 校验签署方信息（如果提供了签署方信息）
//        if (input.getSigners() != null && !input.getSigners().isEmpty()) {
//            for (CreateSignFlowByFileInput.SignerInfo signer : input.getSigners()) {
//                if (signer.getSignerType() == null) {
//                    throw new ESignException.ParameterException("签署方类型不能为空");
//                }
//                
//                // 个人签署方校验
//                if (signer.getSignerType() == 0) {
//                    if (signer.getPsnSignerInfo() == null) {
//                        throw new ESignException.ParameterException("个人签署方信息不能为空");
//                    }
//                    
//                    CreateSignFlowByFileInput.PsnSignerInfo psnSignerInfo = signer.getPsnSignerInfo();
//                    if (StrUtil.isBlank(psnSignerInfo.getPsnAccount()) && StrUtil.isBlank(psnSignerInfo.getPsnId())) {
//                        throw new ESignException.ParameterException("个人账号标识或个人账号ID至少需要传入一个");
//                    }
//                    
//                    if (psnSignerInfo.getPsnInfo() != null) {
//                        CreateSignFlowByFileInput.PsnSignerInfo.PsnInfo psnInfo = psnSignerInfo.getPsnInfo();
//                        if (StrUtil.isNotBlank(psnSignerInfo.getPsnAccount()) && StrUtil.isBlank(psnInfo.getPsnName())) {
//                            throw new ESignException.ParameterException("传入个人账号标识时，个人姓名为必传项");
//                        }
//                    }
//                }
//                
//                // 签署区信息校验
//                if (signer.getSignFields() != null && !signer.getSignFields().isEmpty()) {
//                    for (CreateSignFlowByFileInput.SignFieldInfo signField : signer.getSignFields()) {
//                        if (StrUtil.isBlank(signField.getFileId())) {
//                            throw new ESignException.ParameterException("签署区文件ID不能为空");
//                        }
//                        
//                        if (signField.getSignFieldType() != null && signField.getSignFieldType() == 0) {
//                            if (signField.getNormalSignFieldConfig() == null) {
//                                throw new ESignException.ParameterException("签章区配置项不能为空");
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }
//
//    @Override
//    public GetSignUrlOutput getSignUrl(GetSignUrlInput input) throws ESignException {
//        log.info("开始获取签署页面链接，签署流程ID: {}", input.getSignFlowId());
//        
//        try {
//            // 参数校验
//            validateGetSignUrlInput(input);
//            
//            // 构建请求URL
//            String url = ESignHttpUtil.buildUrl(eSignConfig, String.format(ESignConstants.ApiPath.GET_SIGN_URL, input.getSignFlowId()));
//            
//            // 发送POST请求
//            String requestBody = JSONUtil.toJsonStr(input);
//            GetSignUrlOutput response = ESignHttpUtil.sendPostRequest(
//                    eSignConfig, url, requestBody, GetSignUrlOutput.class);
//            
//            if (response == null) {
//                throw new ESignException.ProcessingException("接口响应为空");
//            }
//            
//            if (!response.isSuccess()) {
//                log.error("获取签署页面链接失败: {}", response.getErrorMessage());
//                throw new ESignException.ProcessingException("获取签署页面链接失败: " + response.getErrorMessage());
//            }
//            
//            log.info("获取签署页面链接成功，签署流程ID: {}, 短链接: {}", 
//                    input.getSignFlowId(), 
//                    response.getData() != null ? response.getData().getShortUrl() : "无");
//            
//            return response;
//            
//        } catch (ESignException e) {
//            log.error("获取签署页面链接异常，签署流程ID: {}, 错误: {}", input.getSignFlowId(), e.getMessage());
//            throw e;
//        } catch (Exception e) {
//            log.error("获取签署页面链接系统异常，签署流程ID: {}", input.getSignFlowId(), e);
//            throw new ESignException.ProcessingException("获取签署页面链接系统异常: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 校验获取签署页面链接输入参数
//     * @param input 输入参数
//     * @throws ESignException.ParameterException 参数异常
//     */
//    private void validateGetSignUrlInput(GetSignUrlInput input) throws ESignException.ParameterException {
//        if (input == null) {
//            throw new ESignException.ParameterException("请求参数不能为空");
//        }
//        
//        if (StrUtil.isBlank(input.getSignFlowId())) {
//            throw new ESignException.ParameterException("签署流程ID不能为空");
//        }
//        
//        // 校验操作人信息（如果提供了操作人信息）
//        if (input.getOperator() != null) {
//            GetSignUrlInput.Operator operator = input.getOperator();
//            if (StrUtil.isBlank(operator.getPsnAccount()) && StrUtil.isBlank(operator.getPsnId())) {
//                throw new ESignException.ParameterException("操作人账号标识或操作人账号ID至少需要传入一个");
//            }
//        }
//        
//        // 校验机构信息（如果提供了机构信息）
//        if (input.getOrganization() != null) {
//            GetSignUrlInput.Organization organization = input.getOrganization();
//            if (StrUtil.isBlank(organization.getOrgId())) {
//                throw new ESignException.ParameterException("机构ID不能为空");
//            }
//        }
//        
//        // 校验重定向配置（如果提供了重定向配置）
//        if (input.getRedirectConfig() != null) {
//            GetSignUrlInput.RedirectConfig redirectConfig = input.getRedirectConfig();
//            if (StrUtil.isNotBlank(redirectConfig.getRedirectUrl())) {
//                // 简单的URL格式校验
//                if (!redirectConfig.getRedirectUrl().startsWith("http://") && 
//                    !redirectConfig.getRedirectUrl().startsWith("https://")) {
//                    throw new ESignException.ParameterException("重定向URL格式不正确");
//                }
//            }
//        }
//     }
//
//    @Override
//    public GetFileDownloadUrlOutput getFileDownloadUrl(GetFileDownloadUrlInput input) throws ESignException {
//        log.info("开始获取已签署文件下载链接，签署流程ID: {}", input.getSignFlowId());
//        
//        try {
//            // 参数校验
//            validateGetFileDownloadUrlInput(input);
//            
//            // 构建请求URL
//            String path = String.format(ESignConstants.ApiPath.GET_FILE_DOWNLOAD_URL, input.getSignFlowId());
//            String url = ESignHttpUtil.buildUrl(eSignConfig, path);
//            
//            log.info("请求URL: {}", url);
//            
//            // 发送GET请求
//            GetFileDownloadUrlOutput response = ESignHttpUtil.sendGetRequest(
//                    eSignConfig, 
//                    url, 
//                    GetFileDownloadUrlOutput.class
//            );
//            
//            if (response == null) {
//                throw new ESignException.ProcessingException("接口响应为空");
//            }
//            
//            if (!response.isSuccess()) {
//                log.error("获取已签署文件下载链接失败: {}", response.getErrorMessage());
//                throw new ESignException.ProcessingException("获取已签署文件下载链接失败: " + response.getErrorMessage());
//            }
//            
//            log.info("获取已签署文件下载链接成功，签署流程ID: {}, 文件数量: {}, 附件数量: {}", 
//                    input.getSignFlowId(), 
//                    response.getData() != null && response.getData().getFiles() != null 
//                            ? response.getData().getFiles().size() : 0,
//                    response.getData() != null && response.getData().getAttachments() != null 
//                            ? response.getData().getAttachments().size() : 0);
//            
//            return response;
//            
//        } catch (ESignException e) {
//            log.error("获取已签署文件下载链接异常，签署流程ID: {}, 错误: {}", input.getSignFlowId(), e.getMessage());
//            throw e;
//        } catch (Exception e) {
//            log.error("获取已签署文件下载链接系统异常，签署流程ID: {}", input.getSignFlowId(), e);
//            throw new ESignException.ProcessingException("获取已签署文件下载链接系统异常: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 校验获取已签署文件下载链接输入参数
//     * @param input 输入参数
//     * @throws ESignException.ParameterException 参数异常
//     */
//    private void validateGetFileDownloadUrlInput(GetFileDownloadUrlInput input) throws ESignException.ParameterException {
//        if (input == null) {
//            throw new ESignException.ParameterException("请求参数不能为空");
//        }
//        
//        if (StrUtil.isBlank(input.getSignFlowId())) {
//            throw new ESignException.ParameterException("签署流程ID不能为空");
//        }
//    }
//}