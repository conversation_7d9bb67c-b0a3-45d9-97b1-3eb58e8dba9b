package org.nonamespace.word.thirdpart.allinpay.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.nonamespace.word.thirdpart.allinpay.domain.AllinpayApiRecords;
import org.nonamespace.word.thirdpart.allinpay.service.IAllinpayApiRecordsService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通联支付API记录控制器
 * 提供API调用记录的查询功能
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@RestController
@RequestMapping("/api/allinpay/records")
@RequiredArgsConstructor
public class AllinpayApiRecordsController {

    private final IAllinpayApiRecordsService allinpayApiRecordsService;

    /**
     * 分页查询API记录
     *
     * @param current 当前页
     * @param size    页大小
     * @return 分页结果
     */
    @GetMapping("/page")
    public IPage<AllinpayApiRecords> page(@RequestParam(defaultValue = "1") Long current,
                                          @RequestParam(defaultValue = "10") Long size) {
        return allinpayApiRecordsService.page(new Page<>(current, size));
    }

    /**
     * 根据商户订单号查询记录
     *
     * @param reqsn 商户订单号
     * @return API记录列表
     */
    @GetMapping("/reqsn/{reqsn}")
    public List<AllinpayApiRecords> getByReqsn(@PathVariable String reqsn) {
        return allinpayApiRecordsService.getByReqsn(reqsn);
    }

    /**
     * 根据API名称查询记录
     *
     * @param apiName API名称
     * @return API记录列表
     */
    @GetMapping("/api/{apiName}")
    public List<AllinpayApiRecords> getByApiName(@PathVariable String apiName) {
        return allinpayApiRecordsService.getByApiName(apiName);
    }

    /**
     * 根据商户ID和应用ID查询记录
     *
     * @param cusid 商户ID
     * @param appid 应用ID
     * @return API记录列表
     */
    @GetMapping("/merchant")
    public List<AllinpayApiRecords> getByCusidAndAppid(@RequestParam String cusid,
                                                       @RequestParam String appid) {
        return allinpayApiRecordsService.getByCusidAndAppid(cusid, appid);
    }

    /**
     * 根据ID查询单条记录
     *
     * @param id 记录ID
     * @return API记录
     */
    @GetMapping("/{id}")
    public AllinpayApiRecords getById(@PathVariable String id) {
        return allinpayApiRecordsService.getById(id);
    }
}