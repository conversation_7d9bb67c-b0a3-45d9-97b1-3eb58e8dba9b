package org.nonamespace.word.thirdpart.esign.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.thirdpart.esign.config.ESignConfig;
import org.nonamespace.word.thirdpart.esign.constants.ESignConstants;
import org.nonamespace.word.thirdpart.esign.domain.EsignTemplateComponents;
import org.nonamespace.word.thirdpart.esign.exception.ESignException;
import org.nonamespace.word.thirdpart.esign.mapper.EsignTemplateComponentsMapper;
import org.nonamespace.word.thirdpart.esign.model.QueryTemplateComponentsOutput;
import org.nonamespace.word.thirdpart.esign.service.IESignTemplateService;
import org.nonamespace.word.thirdpart.esign.util.ESignHttpUtil;
import org.springframework.stereotype.Service;

/**
 * 合同模板服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ESignTemplateServiceImpl extends ServiceImpl<EsignTemplateComponentsMapper, EsignTemplateComponents> implements IESignTemplateService {

    private final ESignConfig eSignConfig;

    @Override
    public QueryTemplateComponentsOutput queryTemplateComponents(String docTemplateId) throws ESignException {
        log.info("开始查询合同模板中控件详情，模板ID: {}", docTemplateId);

        try {
            // 参数校验
            validateQueryTemplateComponentsInput(docTemplateId);

            // 构建请求URL
            String path = String.format(ESignConstants.ApiPath.QUERY_TEMPLATE_COMPONENTS, docTemplateId);
            String url = ESignHttpUtil.buildUrl(eSignConfig, path);

            log.info("请求URL: {}", url);

            // 发送GET请求
            QueryTemplateComponentsOutput response = ESignHttpUtil.sendGetRequest(
                    eSignConfig,
                    url,
                    QueryTemplateComponentsOutput.class
            );

            // 检查响应结果
            if (response == null) {
                throw new ESignException.ProcessingException("接口响应为空");
            }

            if (!response.isSuccess()) {
                log.error("查询合同模板中控件详情失败: {}", response.getErrorMessage());
                throw new ESignException.ProcessingException(
                        response.getCode() != null ? response.getCode() : -1,
                        response.getMessage() != null ? response.getMessage() : "未知错误"
                );
            }

            log.info("查询合同模板中控件详情成功，模板ID: {}, 控件数量: {}",
                    docTemplateId,
                    response.getData() != null && response.getData().getComponents() != null
                            ? response.getData().getComponents().size() : 0);

            return response;

        } catch (ESignException e) {
            log.error("查询合同模板中控件详情异常，模板ID: {}, 错误: {}", docTemplateId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("查询合同模板中控件详情系统异常，模板ID: {}", docTemplateId, e);
            throw new ESignException.ProcessingException("系统异常: " + e.getMessage(), e);
        }
    }

    /**
     * 校验查询合同模板中控件详情的输入参数
     * @param docTemplateId 输入参数
     * @throws ESignException.ParameterException 参数异常
     */
    private void validateQueryTemplateComponentsInput(String docTemplateId) throws ESignException.ParameterException {

        if (StrUtil.isBlank(docTemplateId)) {
            throw new ESignException.ParameterException("合同模板ID不能为空");
        }

        // 检查配置
        if (!eSignConfig.isEnabled()) {
            throw new ESignException.ParameterException("e签宝服务未启用");
        }

        if (StrUtil.isBlank(eSignConfig.getBaseUrl())) {
            throw new ESignException.ParameterException("e签宝baseUrl未配置");
        }

        if (StrUtil.isBlank(eSignConfig.getAppId())) {
            throw new ESignException.ParameterException("e签宝appId未配置");
        }

        if (StrUtil.isBlank(eSignConfig.getAppSecret())) {
            throw new ESignException.ParameterException("e签宝appSecret未配置");
        }
    }
}
