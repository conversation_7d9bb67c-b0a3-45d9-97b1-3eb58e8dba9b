package org.nonamespace.word.thirdpart.esign.constants;

/**
 * e签宝常量
 * <AUTHOR>
 */
public class ESignConstants {

    /**
     * 响应状态码
     */
    public static class ResponseCode {
        /** 成功 */
        public static final int SUCCESS = 0;
    }

    /**
     * HTTP请求头
     */
    public static class Headers {
        public static final String ACCEPT = "*/*";
        /** 应用JSON */
        public static final String APPLICATION_JSON = "application/json";
        /** 授权头 */
        public static final String X_TSIGN_OPEN_VERSION_SDK = "X-Tsign-Open-Version-Sdk";
        /** 时间戳 */
        public static final String X_TSIGN_OPEN_TIMESTAMP = "X-Tsign-Open-Ca-Timestamp";
        /** 应用ID */
        public static final String X_TSIGN_OPEN_APP_ID = "X-Tsign-Open-App-Id";
        /** 签名 */
        public static final String X_TSIGN_OPEN_AUTH_MODE = "X-Tsign-Open-Auth-Mode";
        /** 签名值 */
        public static final String X_TSIGN_OPEN_CA_SIGNATURE = "X-Tsign-Open-Ca-Signature";

        public static final String X_TSIGN_OPEN_AUTH_MODE_SIGNATURE = "Signature";

        public static final String CONTENT_MD5 = "Content-MD5";
    }

    /**
     * API路径
     */
    public static class ApiPath {
        /**
         * 查询合同模板中控件详情
         */
        public static final String QUERY_TEMPLATE_COMPONENTS = "/v3/doc-templates/%s";

        /**
         * 填写模板生成文件
         */
        public static final String CREATE_FILE_BY_TEMPLATE = "/v3/files/create-by-doc-template";

        /**
         * 基于文件发起签署
         */
        public static final String CREATE_SIGN_FLOW_BY_FILE = "/v3/sign-flow/create-by-file";

        /**
         * 获取签署页面链接
         */
        public static final String GET_SIGN_URL = "/v3/sign-flow/%s/sign-url";

        /**
         * 下载已签署文件及附属材料
         */
        public static final String GET_FILE_DOWNLOAD_URL = "/v3/sign-flow/%s/file-download-url";
    }

    /**
     * 控件类型
     */
    public static class ComponentType {
        /** 单行文本 */
        public static final int SINGLE_LINE_TEXT = 1;
        /** 数字 */
        public static final int NUMBER = 2;
        /** 日期 */
        public static final int DATE = 3;
        /** 签章区域 */
        public static final int SIGN_AREA = 6;
        /** 多行文本 */
        public static final int MULTI_LINE_TEXT = 8;
        /** 复选 */
        public static final int CHECKBOX = 9;
        /** 单选 */
        public static final int RADIO = 10;
        /** 图片 */
        public static final int IMAGE = 11;
        /** 下拉框 */
        public static final int DROPDOWN = 14;
        /** 勾选框 */
        public static final int CHECK_BOX = 15;
        /** 身份证 */
        public static final int ID_CARD = 16;
        /** 备注区域 */
        public static final int REMARK_AREA = 17;
        /** 动态表格 */
        public static final int DYNAMIC_TABLE = 18;
        /** 手机号 */
        public static final int MOBILE_PHONE = 19;
    }
}