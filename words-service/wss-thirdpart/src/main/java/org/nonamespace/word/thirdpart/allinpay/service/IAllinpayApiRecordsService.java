package org.nonamespace.word.thirdpart.allinpay.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.thirdpart.allinpay.domain.AllinpayApiRecords;

import java.util.List;
import java.util.Map;

/**
 * 通联支付API记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IAllinpayApiRecordsService extends IService<AllinpayApiRecords> {

    /**
     * 记录通联支付API请求和响应
     * 
     * @param apiName API接口名称
     * @param cusid 商户号
     * @param appid 应用ID
     * @param reqsn 商户订单号
     * @param request 请求参数
     * @param response 响应参数
     * @return 记录ID
     */
    String recordApiCall(String apiName, String cusid, String appid, String reqsn, 
                        Map<String, Object> request, Map<String, Object> response);

    /**
     * 根据商户订单号查询API记录
     * 
     * @param reqsn 商户订单号
     * @return API记录列表
     */
    List<AllinpayApiRecords> getByReqsn(String reqsn);

    /**
     * 根据API接口名称查询记录
     * 
     * @param apiName API接口名称
     * @return API记录列表
     */
    List<AllinpayApiRecords> getByApiName(String apiName);

    /**
     * 根据商户号和应用ID查询记录
     * 
     * @param cusid 商户号
     * @param appid 应用ID
     * @return API记录列表
     */
    List<AllinpayApiRecords> getByCusidAndAppid(String cusid, String appid);

}