package org.nonamespace.word.thirdpart.esign.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.thirdpart.esign.model.base.ESignResponseBase;

/**
 * 填写模板生成文件响应参数
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateFileByTemplateOutput extends ESignResponseBase {

    /**
     * 业务数据
     */
    private DataInfo data;

    /**
     * 业务数据信息
     */
    @Data
    public static class DataInfo {
        /**
         * 填充后生成的文件ID
         */
        private String fileId;

        /**
         * 文件下载地址（有效期为60分钟，过期后可以重新调用接口获取新的下载地址）
         * 填充PDF模板时，返回填充后的文件下载地址
         * 填充HTML模板时，默认返回null，如需获取文件下载地址，建议调用【查询文件上传状态】接口
         */
        private String fileDownloadUrl;
    }

    /**
     * 判断是否成功
     * @return true-成功，false-失败
     */
    public boolean isSuccess() {
        return super.isSuccess();
    }

    /**
     * 获取文件ID
     * @return 文件ID
     */
    public String getFileId() {
        return data != null ? data.getFileId() : null;
    }

    /**
     * 获取文件下载地址
     * @return 文件下载地址
     */
    public String getFileDownloadUrl() {
        return data != null ? data.getFileDownloadUrl() : null;
    }
}