package org.nonamespace.word.thirdpart.allinpay.model;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig;
import org.nonamespace.word.thirdpart.allinpay.constants.AllinPayConstants;
import org.nonamespace.word.thirdpart.allinpay.model.base.AllinPayInputBase;

/**
 * 通联支付统一撤销API请求参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class CancelInput extends AllinPayInputBase {

    public CancelInput(AllinPayConfig config) {
        super(config);
    }

    /**
     * 版本号
     * 接口版本号，默认填11
     */
    private final Integer version = 11;

    /**
     * 商户撤销交易单号
     * 商户的撤销交易订单号，商户平台唯一
     */
    private String reqsn;

    /**
     * 交易金额
     * 原订单金额，单位为分
     */
    private Long trxamt = 0L;

    /**
     * 原交易单号
     * 原交易的商户交易单号
     * oldreqsn和oldtrxid必填其一
     */
    private String oldreqsn;

    /**
     * 原交易流水
     * 原交易的收银宝平台流水
     * oldreqsn和oldtrxid必填其一
     * 建议：商户如果同时拥有oldtrxid和oldreqsn，优先使用oldtrxid
     */
    private String oldtrxid;

    /**
     * 随机字符串
     * 随机生成的字符串
     */
    private String randomstr;

    /**
     * 签名类型
     * RSA或SM2
     */
    private final String signtype = AllinPayConstants.SignType.RSA;

    /**
     * 签名
     * 详见安全规范
     */
    private String sign;


    public boolean isValid() {
        return StrUtil.isNotBlank(getCusid()) && StrUtil.isNotBlank(getAppid()) && getTrxamt() > 0;
    }
}