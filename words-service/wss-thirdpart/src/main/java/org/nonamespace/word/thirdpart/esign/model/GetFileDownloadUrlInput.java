package org.nonamespace.word.thirdpart.esign.model;

import lombok.Data;

/**
 * 下载已签署文件及附属材料请求参数
 * <AUTHOR>
 */
@Data
public class GetFileDownloadUrlInput {

    /**
     * 已完成状态的签署流程ID
     * 必填参数
     */
    private String signFlowId;

    /**
     * 构造函数
     * @param signFlowId 签署流程ID
     */
    public GetFileDownloadUrlInput(String signFlowId) {
        this.signFlowId = signFlowId;
    }

    /**
     * 默认构造函数
     */
    public GetFileDownloadUrlInput() {
    }
}