package org.nonamespace.word.thirdpart.esign.model;

import lombok.Data;

/**
 * 获取签署页面链接请求参数
 * <AUTHOR>
 */
@Data
public class GetSignUrlInput {

    /**
     * 签署流程ID
     */
    private String signFlowId;

    /**
     * 是否需要登录打开链接（默认值 false）
     * true - 需登录打开链接，false - 免登录
     */
    private Boolean needLogin = false;

    /**
     * 链接类型（默认值 2）
     * 1 - 预览链接（仅限查看，不能签署）
     * 2 - 签署链接
     */
    private Integer urlType = 2;

    /**
     * 个人签署方（机构签署传经办人信息）
     * 当获取签署链接场景，需传入当前流程流转到的签署操作人信息
     * psnAccount与psnId二选一传入（必须与发起签署时的账号保持一致）
     */
    private Operator operator;

    /**
     * 机构签署方
     * 一个流程中存在经办人代多个机构签署时，通过此参数分别获取对应机构的签署链接
     * orgId与orgName二选一传入（必须与发起签署时账号保持一致）
     */
    private Organization organization;

    /**
     * 重定向配置项
     */
    private RedirectConfig redirectConfig;

    /**
     * 指定客户端类型，当urlType为2（签署链接）时生效
     * H5 - 移动端适配
     * PC - PC端适配
     * ALL - 自动适配移动端或PC端（默认值）
     */
    private String clientType = "ALL";

    /**
     * AppScheme，主要用于支付宝人脸认证重定向时跳回开发者自身App
     * 示例值：esign://demo/signBack
     */
    private String appScheme;

    /**
     * 签署操作人信息
     */
    @Data
    public static class Operator {
        /**
         * 签署操作人账号标识（手机号/邮箱号）
         */
        private String psnAccount;

        /**
         * 签署操作人账号ID（个人账号ID）
         */
        private String psnId;
    }

    /**
     * 机构签署方信息
     */
    @Data
    public static class Organization {
        /**
         * 机构账号ID
         */
        private String orgId;

        /**
         * 机构名称
         */
        private String orgName;
    }

    /**
     * 重定向配置项
     */
    @Data
    public static class RedirectConfig {
        /**
         * 签署完成后跳转页面（除app和小程序端集成外，地址需符合 https /http 协议地址）
         * 注：贵司的重定向域名需要在e签宝提前放行，否则会报错："您即将访问的页面可能有安全风险"
         * 签署完成后会在贵司重定向地址上拼接签署状态等字段
         */
        private String redirectUrl;

        /**
         * 操作完成重定向跳转延迟时间，单位秒（可选值0、3，默认值为 3）
         * 传0时，签署完成直接跳转重定向地址；
         * 传3时，展示签署完成结果页，倒计时3秒后，自动跳转重定向地址。
         * 注：当redirectUrl不传的情况下，该字段无需传入，签署完成不跳转
         */
        private Integer redirectDelayTime = 3;
    }
}