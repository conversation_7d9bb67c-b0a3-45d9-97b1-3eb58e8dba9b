package org.nonamespace.word.thirdpart.esign.exception;

/**
 * e签宝异常
 * <AUTHOR>
 */
public class ESignException extends Exception {

    private final int code;
    private final String message;

    public ESignException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public ESignException(String message) {
        super(message);
        this.code = -1;
        this.message = message;
    }

    public ESignException(String message, Throwable cause) {
        super(message, cause);
        this.code = -1;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    /**
     * 接口处理异常
     */
    public static class ProcessingException extends ESignException {
        public ProcessingException(int code, String message) {
            super(code, message);
        }

        public ProcessingException(String message) {
            super(message);
        }

        public ProcessingException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 网络异常
     */
    public static class NetworkException extends ESignException {
        public NetworkException(String message) {
            super(message);
        }

        public NetworkException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 参数异常
     */
    public static class ParameterException extends ESignException {
        public ParameterException(String message) {
            super(message);
        }
    }

    /**
     * 算法异常
     */
    public static class AlgorithmException extends ESignException {
        public AlgorithmException(String message) {
            super(message);
        }

        public AlgorithmException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}