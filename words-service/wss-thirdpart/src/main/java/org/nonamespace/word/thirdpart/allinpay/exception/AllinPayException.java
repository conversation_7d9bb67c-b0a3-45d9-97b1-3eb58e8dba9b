package org.nonamespace.word.thirdpart.allinpay.exception;


import lombok.Getter;


/**
 * 支付统一异常
 */
@Getter
public class AllinPayException extends Exception {

    private final Integer code;
    private static final Integer DUPLICATE_REQUEST_ERROR_CODE = 901;
    private static final Integer PROCESSING_ERROR_CODE = 902;

    public AllinPayException(String message) {
        super(message);
        this.code = null;
    }

    public AllinPayException(String message, Throwable cause) {
        super(message, cause);
        this.code = null;
    }

    public AllinPayException(String message, Integer code) {
        super(message);
        this.code = code;
    }

    public AllinPayException(String message, Integer code, Throwable cause) {
        super(message, cause);
        this.code = code;
    }


    /**
     * 重复请求异常
     */
    public static class DuplicateRequestException extends AllinPayException {

        @Getter
        private final String trxId;

        public DuplicateRequestException(String message) {
            super(message, DUPLICATE_REQUEST_ERROR_CODE);
            this.trxId = null;
        }

        public DuplicateRequestException(String message, String trxId) {
            super(message, DUPLICATE_REQUEST_ERROR_CODE);
            this.trxId = trxId;
        }

        public DuplicateRequestException(String message, String trxId, Throwable cause) {
            super(message, DUPLICATE_REQUEST_ERROR_CODE, cause);
            this.trxId = trxId;
        }
    }


    /**
     * 正在处理中异常
     */
    @Getter
    public static class ProcessingException extends AllinPayException {

        private final String trxId;

        public ProcessingException(String message) {
            super(message, PROCESSING_ERROR_CODE);
            this.trxId = null;
        }

        public ProcessingException(String message, String trxId) {
            super(message, PROCESSING_ERROR_CODE);
            this.trxId = trxId;
        }

        public ProcessingException(String message, String trxId, Throwable cause) {
            super(message, PROCESSING_ERROR_CODE, cause);
            this.trxId = trxId;
        }
    }

}
