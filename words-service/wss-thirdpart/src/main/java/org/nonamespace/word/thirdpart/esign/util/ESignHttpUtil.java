package org.nonamespace.word.thirdpart.esign.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.nonamespace.word.thirdpart.esign.config.ESignConfig;
import org.nonamespace.word.thirdpart.esign.constants.ESignConstants;
import org.nonamespace.word.thirdpart.esign.exception.ESignException;
import org.springframework.http.MediaType;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

/**
 * e签宝HTTP工具类
 * <AUTHOR>
 */
@Slf4j
public class ESignHttpUtil {

    /**
     * 发送GET请求
     * @param config e签宝配置
     * @param url 请求URL
     * @param responseClass 响应类型
     * @param <T> 响应类型泛型
     * @return 响应对象
     * @throws ESignException 异常
     */
    public static <T> T sendGetRequest(ESignConfig config, String url, Class<T> responseClass) throws ESignException {
        try {
            // 提取URL路径
            String urlPath = extractUrlPath(url);
            
            // 生成签名
            String signature = generateSignature("GET", "", "", urlPath, config.getAppSecret());
            
            // 构建请求
            HttpRequest request = HttpRequest.get(url)
                    .timeout(config.getConnectTimeout());
            
            // 设置请求头
            buildHeader(request, config, "", signature);

            log.info("发送e签宝GET请求: {}", url);
            log.info("请求头：{}", request.headers());

            // 发送请求
            HttpResponse response = request.execute();
            String responseBody = response.body();
            
            log.info("e签宝响应状态码: {}, 响应内容: {}", response.getStatus(), responseBody);
            
            if (!response.isOk()) {
                throw new ESignException.NetworkException("HTTP请求失败，状态码: " + response.getStatus());
            }
            
            // 解析响应
            return JSONUtil.toBean(responseBody, responseClass);
            
        } catch (ESignException e) {
            log.error("签名生成失败: {}", e.getMessage(), e);
            throw new ESignException.ProcessingException("签名生成失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("发送e签宝GET请求异常: {}", e.getMessage(), e);
            throw new ESignException.NetworkException("网络请求异常: " + e.getMessage(), e);
        }
    }

    /**
     * 发送POST请求
     * @param config e签宝配置
     * @param url 请求URL
     * @param requestBody 请求体
     * @param responseClass 响应类型
     * @param <T> 响应类型泛型
     * @return 响应对象
     * @throws ESignException 异常
     */
    public static <T> T sendPostRequest(ESignConfig config, String url, String requestBody, Class<T> responseClass) throws ESignException {
        try {
            // 生成GMT时间
//            String gmtDate = getGMTDate();
            
            // 提取URL路径
            String urlPath = extractUrlPath(url);
            
            // 计算Content-MD5
            String contentMd5 = "";
            if (StrUtil.isNotBlank(requestBody)) {
                try {
                    contentMd5 = doContentMD5(requestBody);
                } catch (Exception e) {
                    log.error("计算Content-MD5异常: {}", e.getMessage(), e);
                    throw new ESignException.ProcessingException("计算Content-MD5失败", e);
                }
            }
            
            // 生成签名
            String signature = generateSignature("POST", contentMd5, "", urlPath, config.getAppSecret());
            
            // 构建请求
            HttpRequest request = HttpRequest.post(url)
                    .timeout(config.getConnectTimeout())
                    .body(requestBody);
            
            // 设置请求头
            buildHeader(request, config, contentMd5, signature);

            log.info("发送e签宝POST请求: {}, 请求体: {}", url, requestBody);
            
            // 发送请求
            HttpResponse response = request.execute();
            String responseBody = response.body();
            
            log.info("e签宝响应状态码: {}, 响应内容: {}", response.getStatus(), responseBody);
            
            if (!response.isOk()) {
                throw new ESignException.NetworkException("HTTP请求失败，状态码: " + response.getStatus());
            }
            
            // 解析响应
            return JSONUtil.toBean(responseBody, responseClass);
            
        } catch (ESignException e) {
            log.error("签名生成失败: {}", e.getMessage(), e);
            throw new ESignException.ProcessingException("签名生成失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("发送e签宝POST请求异常: {}", e.getMessage(), e);
            throw new ESignException.NetworkException("网络请求异常: " + e.getMessage(), e);
        }
    }

    /**
     * 设置通用请求头
     * @param request HTTP请求对象
     * @param config e签宝配置
     * @param contentMd5 Content-MD5值
     * @param signature 签名
     */
    private static void buildHeader(HttpRequest request, ESignConfig config, String contentMd5, String signature) {
        request.header(Header.ACCEPT, ESignConstants.Headers.ACCEPT)
               .header(Header.CONTENT_TYPE, MediaType.APPLICATION_JSON_UTF8_VALUE)
               .header(ESignConstants.Headers.X_TSIGN_OPEN_APP_ID, config.getAppId())
               .header(ESignConstants.Headers.X_TSIGN_OPEN_VERSION_SDK, config.getSdkVersion())
               .header(ESignConstants.Headers.CONTENT_MD5, contentMd5)
               .header(ESignConstants.Headers.X_TSIGN_OPEN_TIMESTAMP, String.valueOf(System.currentTimeMillis()))
               .header(ESignConstants.Headers.X_TSIGN_OPEN_AUTH_MODE, ESignConstants.Headers.X_TSIGN_OPEN_AUTH_MODE_SIGNATURE)
               .header(ESignConstants.Headers.X_TSIGN_OPEN_CA_SIGNATURE, signature);
    }

    /**
     * 生成签名（使用官方EsignEncryption类的签名方式）
     *
     * @param httpMethod HTTP方法
     * @param contentMd5 Content-MD5
     * @param date       Date头
     * @param url        请求URL路径
     * @param appSecret  应用密钥
     * @return 签名
     */
    private static String generateSignature(String httpMethod, String contentMd5,
                                            String date,
                                          String url, String appSecret) throws ESignException {
        // 使用官方EsignEncryption类拼接待签名字符串
        String stringToSign = appendSignDataString(httpMethod, contentMd5, "", date, url);
        
        log.info("待签名字符串: {}", stringToSign);
        
        // 使用官方EsignEncryption类生成签名
        String signature = doSignatureBase64(stringToSign, appSecret);
        
        log.info("生成的签名: {}", signature);
        
        return signature;
    }
    
    /**
     * 获取GMT格式的当前时间
     * @return GMT时间字符串
     */
    private static String getGMTDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.US);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        return sdf.format(new Date());
    }
    
    /**
     * 从完整URL中提取路径部分
     * @param fullUrl 完整URL
     * @return URL路径
     */
    private static String extractUrlPath(String fullUrl) {
        try {
            // 找到协议后的第一个斜杠位置
            int protocolEnd = fullUrl.indexOf("://");
            if (protocolEnd == -1) {
                return fullUrl;
            }
            
            // 找到域名后的第一个斜杠位置
            int pathStart = fullUrl.indexOf("/", protocolEnd + 3);
            if (pathStart == -1) {
                return "/";
            }
            
            return fullUrl.substring(pathStart);
        } catch (Exception e) {
            log.error("提取URL路径异常: {}", e.getMessage(), e);
            return fullUrl;
        }
    }

    /**
     * 构建完整URL
     * @param config e签宝配置
     * @param path API路径
     * @return 完整URL
     */
    public static String buildUrl(ESignConfig config, String path) {
        String baseUrl = config.getBaseUrl();
        if (StrUtil.isBlank(baseUrl)) {
            throw new IllegalArgumentException("e签宝baseUrl不能为空");
        }
        
        // 确保baseUrl不以/结尾
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        
        // 确保path以/开头
        if (!path.startsWith("/")) {
            path = "/" + path;
        }
        
        return baseUrl + path;
    }

    private static String doContentMD5(String str) throws ESignException {
        byte[] md5Bytes = null;
        MessageDigest md5 = null;
        String contentMD5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            // 计算md5函数
            md5.update(str.getBytes(StandardCharsets.UTF_8));
            // 获取文件MD5的二进制数组（128位）
            md5Bytes = md5.digest();
            // 把MD5摘要后的二进制数组md5Bytes使用Base64进行编码（而不是对32位的16进制字符串进行编码）
            contentMD5 = Base64.encodeBase64String(md5Bytes);

        } catch (NoSuchAlgorithmException e) {
            throw new ESignException.AlgorithmException("不支持此算法", e);
        }
        return contentMD5;
    }

    private static String appendSignDataString(String httpMethod, String contentMd5, String headers, String date, String url) {
        StringBuilder sb = new StringBuilder();
        sb.append(httpMethod).append("\n").append(ESignConstants.Headers.ACCEPT).append("\n").append(contentMd5).append("\n")
                .append(MediaType.APPLICATION_JSON_UTF8).append("\n");

        if ("".equals(date) || date == null) {
            sb.append("\n");
        } else {
            sb.append(date).append("\n");
        }
        if ("".equals(headers) || headers == null) {
            sb.append(url);
        } else {
            sb.append(headers).append("\n").append(url);
        }
        return new String(sb);
    }

    private static String doSignatureBase64(String message, String secret) throws ESignException {
        String algorithm = "HmacSHA256";
        Mac hmacSha256;
        String digestBase64 = null;
        try {
            hmacSha256 = Mac.getInstance(algorithm);
            byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
            byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
            hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, algorithm));
            // 使用HmacSHA256对二进制数据消息Bytes计算摘要
            byte[] digestBytes = hmacSha256.doFinal(messageBytes);
            // 把摘要后的结果digestBytes使用Base64进行编码
            digestBase64 = Base64.encodeBase64String(digestBytes);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new ESignException.AlgorithmException("不支持此算法，或无效的密钥规范", e);
        }
        return digestBase64;
    }
}