package org.nonamespace.word.thirdpart.allinpay.model;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig;
import org.nonamespace.word.thirdpart.allinpay.constants.AllinPayConstants;
import org.nonamespace.word.thirdpart.allinpay.model.base.AllinPayInputBase;

/**
 * 通联支付统一退款API请求参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class RefundInput extends AllinPayInputBase {

    public RefundInput(AllinPayConfig config) {
        super(config);
    }

    /**
     * 版本号
     * 接口版本号，默认填11
     */
    private final Integer version = 11;

    /**
     * 退款金额
     * 单位为分
     */
    private Long trxamt = 0L;

    /**
     * 商户退款订单号
     * 商户的退款交易订单号，商户平台唯一
     */
    private String reqsn;

    /**
     * 原交易订单号
     * 原交易的商户订单号
     * oldreqsn和oldtrxid必填其一
     */
    private String oldreqsn;

    /**
     * 原交易流水
     * 原交易的收银宝平台流水
     * oldreqsn和oldtrxid必填其一
     * 建议：商户如果同时拥有oldtrxid和oldreqsn，优先使用oldtrxid
     */
    private String oldtrxid;

    /**
     * 备注
     * 备注信息，最大300个字节(150个中文字符)
     */
    private String remark;

    /**
     * 优惠信息
     * 只适用于银联单品优惠交易的退货
     */
    private String benefitdetail;

    /**
     * 随机字符串
     * 随机生成的字符串
     */
    private String randomstr;

    /**
     * 交易结果通知地址
     * 接收交易结果的通知回调地址，通知url必须为直接可访问的url，不能携带参数。
     * https只支持默认端口，若使用https,需使用默认443端口
     */
    private String notify_url;

    /**
     * 签名类型
     * RSA或SM2
     */
    private final String signtype = AllinPayConstants.SignType.RSA;

    /**
     * 签名
     * 详见安全规范
     */
    private String sign;


    public boolean isValid() {
        return StrUtil.isNotBlank(getCusid()) && StrUtil.isNotBlank(getAppid()) && getTrxamt() > 0;
    }
}