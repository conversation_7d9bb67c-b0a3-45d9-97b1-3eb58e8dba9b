package org.nonamespace.word.thirdpart.esign.service;

import org.nonamespace.word.thirdpart.esign.exception.ESignException;
import org.nonamespace.word.thirdpart.esign.model.*;

public interface IESignFlowService {

    /**
     * 基于文件发起签署
     * 开发者可基于已上传的合同文件或模板所填充生成的文件来发起签署流程
     *
     * @param input 签署流程参数
     * @return 签署流程响应
     * @throws ESignException 异常
     */
    CreateSignFlowByFileOutput createSignFlowByFile(CreateSignFlowByFileInput input) throws ESignException;

    /**
     * 获取签署页面链接
     * 发起签署时，开发者可指定签署方签署通知的方式（短信通知或邮件通知），
     * 签署人访问签署链接以进行相关操作；当开发者需要在业务系统中访问签署链接
     * 或自行发送签署通知时，可通过此接口获取合同的签署/预览链接
     *
     * @param input 获取签署链接参数
     * @return 签署链接响应
     * @throws ESignException 异常
     */
    GetSignUrlOutput getSignUrl(GetSignUrlInput input) throws ESignException;

    /**
     * 下载已签署文件及附属材料
     * 流程结束后，获取签署完成的文件以及相关附属材料的下载链接。
     * 未签署完成的流程，无法下载相关文件，否则会报错："流程非签署完成状态，不允许下载文档"。
     *
     * @param input 下载文件参数
     * @return 文件下载链接响应
     * @throws ESignException 异常
     */
    GetFileDownloadUrlOutput getFileDownloadUrl(GetFileDownloadUrlInput input) throws ESignException;
}
