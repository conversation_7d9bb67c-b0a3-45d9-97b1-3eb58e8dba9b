package org.nonamespace.word.thirdpart.allinpay.enums;

import lombok.Getter;

/**
 * 支付方式枚举
 * <AUTHOR>
 */
@Getter
public enum PayTypeEnum {
    
    /** 微信扫码支付 */
    W01("W01", "微信扫码支付"),
    
    /** 微信JS支付 */
    W02("W02", "微信JS支付"),
    
    /** 微信APP支付 */
    W03("W03", "微信APP支付"),
    
    /** 微信小程序支付 */
    W06("W06", "微信小程序支付"),
    
    /** 微信订单支付 */
    W11("W11", "微信订单支付"),
    
    /** 支付宝扫码支付 */
    A01("A01", "支付宝扫码支付"),
    
    /** 支付宝JS支付 */
    A02("A02", "支付宝JS支付"),
    
    /** 支付宝APP支付 */
    A03("A03", "支付宝APP支付"),
    
    /** 银联扫码支付(CSB) */
    U01("U01", "银联扫码支付(CSB)"),
    
    /** 银联JS支付 */
    U02("U02", "银联JS支付"),
    
    /** 数币扫码支付 */
    S01("S01", "数币扫码支付"),
    
    /** 数字货币H5/APP */
    S03("S03", "数字货币H5/APP"),
    
    /** 网联支付 */
    N03("N03", "网联支付");
    
    private final String code;
    private final String description;
    
    PayTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     */
    public static PayTypeEnum fromCode(String code) {
        for (PayTypeEnum payType : values()) {
            if (payType.getCode().equals(code)) {
                return payType;
            }
        }
        throw new IllegalArgumentException("未知的支付方式代码: " + code);
    }
}
