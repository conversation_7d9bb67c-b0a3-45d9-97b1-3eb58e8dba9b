package org.nonamespace.word.thirdpart.allinpay.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.thirdpart.allinpay.domain.AllinpayApiRecords;
import org.nonamespace.word.thirdpart.allinpay.mapper.AllinpayApiRecordsMapper;
import org.nonamespace.word.thirdpart.allinpay.service.IAllinpayApiRecordsService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 通联支付API记录Service实现类
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
public class AllinpayApiRecordsServiceImpl extends ServiceImpl<AllinpayApiRecordsMapper, AllinpayApiRecords> 
        implements IAllinpayApiRecordsService {

    @Override
    public String recordApiCall(String apiName, String cusid, String appid, String reqsn, 
                               Map<String, Object> request, Map<String, Object> response) {
        try {
            AllinpayApiRecords record = new AllinpayApiRecords()
                    .setAllinpayApi(apiName)
                    .setCusid(cusid)
                    .setAppid(appid)
                    .setReqsn(reqsn)
                    .setAllinpayRequest(request != null ? JSONUtil.toJsonStr(request) : null)
                    .setAllinpayResponse(response != null ? JSONUtil.toJsonStr(response) : null)
                    .setSuccess(response != null && !response.containsKey("error"));
            
            save(record);
            
            log.info("记录通联支付API调用: apiName={}, cusid={}, appid={}, reqsn={}, recordId={}", 
                    apiName, cusid, appid, reqsn, record.getId());
            
            return record.getId();
        } catch (Exception e) {
            log.error("记录通联支付API调用失败: apiName={}, cusid={}, appid={}, reqsn={}", 
                    apiName, cusid, appid, reqsn, e);
            throw new RuntimeException("记录API调用失败", e);
        }
    }

    @Override
    public List<AllinpayApiRecords> getByReqsn(String reqsn) {
        if (StrUtil.isBlank(reqsn)) {
            throw new IllegalArgumentException("商户订单号不能为空");
        }
        
        return lambdaQuery()
                .eq(AllinpayApiRecords::getReqsn, reqsn)
                .orderByDesc(AllinpayApiRecords::getCreateTime)
                .list();
    }

    @Override
    public List<AllinpayApiRecords> getByApiName(String apiName) {
        if (StrUtil.isBlank(apiName)) {
            throw new IllegalArgumentException("API接口名称不能为空");
        }
        
        return lambdaQuery()
                .eq(AllinpayApiRecords::getAllinpayApi, apiName)
                .orderByDesc(AllinpayApiRecords::getCreateTime)
                .list();
    }

    @Override
    public List<AllinpayApiRecords> getByCusidAndAppid(String cusid, String appid) {
        if (StrUtil.isBlank(cusid) || StrUtil.isBlank(appid)) {
            throw new IllegalArgumentException("商户号和应用ID不能为空");
        }
        
        return lambdaQuery()
                .eq(AllinpayApiRecords::getCusid, cusid)
                .eq(AllinpayApiRecords::getAppid, appid)
                .orderByDesc(AllinpayApiRecords::getCreateTime)
                .list();
    }

}