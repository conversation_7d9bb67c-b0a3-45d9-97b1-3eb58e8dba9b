package org.nonamespace.word.thirdpart.allinpay.service;

import org.nonamespace.word.thirdpart.allinpay.dto.PayDto;
import org.nonamespace.word.thirdpart.allinpay.dto.RefundDto;
import org.nonamespace.word.thirdpart.allinpay.exception.AllinPayException;
import org.nonamespace.word.thirdpart.allinpay.model.*;

/**
 * 通联统一支付接口
 * <AUTHOR>
 */
public interface IAllinPayService {


    /**
     * 统一主扫API
     * 
     * @param req 支付请求参数
     * @return 支付响应结果
     */
    NativePayOutput nativepay(PayDto.NativePayReq req) throws AllinPayException.ProcessingException;


    /**
     * 统一撤销API
     * 只能撤销当天的交易，全额退款，实时返回退款结果
     *
     * @param req 撤销请求参数
     * @return 撤销响应结果
     */
    CancelOutput cancel(RefundDto.Req req) throws AllinPayException.ProcessingException;


    /**
     * 统一退款API
     * 
     * @param req 退款请求参数
     * @return 退款响应结果
     */
    RefundOutput refund(RefundDto.Req req) throws AllinPayException.ProcessingException;

    /**
     * 统一查询API
     * 
     * @param req 查询请求参数
     * @return 查询响应结果
     */
    QueryOutput query(PayDto.QueryReq req) throws AllinPayException.ProcessingException;

    /**
     * 统一订单关闭API
     * 用于关闭未支付的订单，释放风控额度
     * 
     * @param req 订单关闭请求参数
     * @return 订单关闭响应结果
     */
    CloseOutput close(PayDto.CloseReq req) throws AllinPayException.ProcessingException;

}
