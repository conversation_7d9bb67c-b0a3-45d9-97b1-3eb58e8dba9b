package org.nonamespace.word.thirdpart.allinpay.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig;
import org.nonamespace.word.thirdpart.allinpay.model.base.AllinPayInputBase;
import cn.hutool.core.util.StrUtil;

/**
 * 通联支付统一订单关闭接口输入参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CloseInput extends AllinPayInputBase {

    /**
     * 商户订单号
     */
    private String reqsn;

    /**
     * 平台交易流水号
     */
    private String trxid;

    /**
     * 版本号，默认11
     */
    private String version = "11";

    /**
     * 随机字符串
     */
    private String randomstr;

    /**
     * 无参构造函数
     */
    public CloseInput() {
        super(null);
    }

    /**
     * 带配置的构造函数
     *
     * @param config 通联支付配置
     */
    public CloseInput(AllinPayConfig config) {
        super(config);
    }

    /**
     * 参数校验
     *
     * @return 是否有效
     */
    public boolean isValid() {
        // 基础参数校验
        if (StrUtil.isBlank(getCusid()) || StrUtil.isBlank(getAppid())) {
            return false;
        }
        // reqsn和trxid至少有一个不为空
        return StrUtil.isNotBlank(reqsn) || StrUtil.isNotBlank(trxid);
    }
}