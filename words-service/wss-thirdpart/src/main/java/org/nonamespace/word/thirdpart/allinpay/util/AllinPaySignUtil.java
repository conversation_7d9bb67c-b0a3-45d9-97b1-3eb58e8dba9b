package org.nonamespace.word.thirdpart.allinpay.util;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 通联支付签名工具类
 * <AUTHOR>
 */
@Slf4j
public class AllinPaySignUtil {

    private static final String SIGN_ALGORITHM = "SHA1WithRSA";
    private static final String KEY_ALGORITHM = "RSA";

    /**
     * 生成签名
     * 
     * @param params 参数Map
     * @param privateKey 私钥字符串（Base64编码）
     * @return 签名字符串
     */
    public static String sign(Map<String, Object> params, String privateKey) {
        try {
            // 1. 构建待签名字符串
            String signStr = buildSignString(params);
            log.info("[统一支付] - 签名前参数：{}", signStr);
            
            // 2. 使用私钥签名
            PrivateKey key = getPrivateKey(privateKey);
            Signature signature = Signature.getInstance(SIGN_ALGORITHM);
            signature.initSign(key);
            signature.update(signStr.getBytes(StandardCharsets.UTF_8));
            
            // 3. 返回Base64编码的签名
            byte[] signBytes = signature.sign();
            return Base64.getEncoder().encodeToString(signBytes);
        } catch (Exception e) {
            throw new RuntimeException("签名失败", e);
        }
    }

    /**
     * 验证签名
     * 
     * @param params 参数Map
     * @param sign 签名字符串
     * @param publicKey 公钥字符串（Base64编码）
     * @return 验证结果
     */
    public static boolean verifySign(Map<String, Object> params, String sign, String publicKey) {
        try {
            // 1. 构建待验签字符串
            String signStr = buildSignString(params);
            
            // 2. 使用公钥验签
            PublicKey key = getPublicKey(publicKey);
            Signature signature = Signature.getInstance(SIGN_ALGORITHM);
            signature.initVerify(key);
            signature.update(signStr.getBytes(StandardCharsets.UTF_8));
            
            // 3. 验证签名
            byte[] signBytes = Base64.getDecoder().decode(sign);
            return signature.verify(signBytes);
        } catch (Exception e) {
            throw new RuntimeException("验签失败", e);
        }
    }

    /**
     * 构建待签名字符串
     * 规则：按参数名ASCII码从小到大排序，使用URL键值对的格式（即key1=value1&key2=value2…）拼接成字符串
     * 空值不参与签名
     */
    private static String buildSignString(Map<String, Object> params) {
        return params.entrySet().stream()
                .filter(entry -> entry.getValue() != null && !entry.getValue().toString().isEmpty())
                .filter(entry -> !"sign".equals(entry.getKey())) // 排除sign字段
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
    }

    /**
     * 获取私钥对象
     */
    private static PrivateKey getPrivateKey(String privateKeyStr) throws Exception {
        // 移除私钥字符串中的头尾标识和换行符
        String cleanPrivateKey = privateKeyStr
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replace("-----BEGIN RSA PRIVATE KEY-----", "")
                .replace("-----END RSA PRIVATE KEY-----", "")
                .replaceAll("\\s+", "");
        
        byte[] keyBytes = Base64.getDecoder().decode(cleanPrivateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 获取公钥对象
     */
    private static PublicKey getPublicKey(String publicKeyStr) throws Exception {
        // 移除公钥字符串中的头尾标识和换行符
        String cleanPublicKey = publicKeyStr
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replace("-----BEGIN RSA PUBLIC KEY-----", "")
                .replace("-----END RSA PUBLIC KEY-----", "")
                .replaceAll("\\s+", "");
        
        byte[] keyBytes = Base64.getDecoder().decode(cleanPublicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 生成随机字符串
     * 
     * @param length 长度
     * @return 随机字符串
     */
    public static String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }

    /**
     * 将对象转换为Map（用于签名）
     */
    public static Map<String, Object> objectToMap(Object obj) {
        return BeanUtil.beanToMap(obj,false, true);
    }


    public static void main(String[] args) {
        Map<String, Object> params = new HashMap<>();
        params.put("appid", "00000051");
        params.put("cusid", "990581007426001");
        params.put("randomstr", "82712208");
        params.put("signtype", "RSA");
        params.put("trxid", "112094120001088317");
        params.put("version", "11");
        String sign = sign(params, "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAJgHMGYsspghvP+yCbjLG43CkZuQ3YJyDcmEKxvmgblITfmiTPx2b9Y2iwDT9gnLGExTDm1BL2A8VzMobjaHfiCmTbDctu680MLmpDDkVXmJOqdlXh0tcLjhN4+iDA2KkRqiHxsDpiaKT6MMBuecXQbJtPlVc1XjVhoUlzUgPCrvAgMBAAECgYAV9saYTGbfsdLOF5kYo0dve1JxaO7dFMCcgkV+z2ujKtNmeHtU54DlhZXJiytQY5Dhc10cjb6xfFDrftuFcfKCaLiy6h5ETR8jyv5He6KH/+X6qkcGTkJBYG1XvyyFO3PxoszQAs0mrLCqq0UItlCDn0G72MR9/NuvdYabGHSzEQJBAMXB1/DUvBTHHH4LiKDiaREruBb3QtP72JQS1ATVXA2v6xJzGPMWMBGQDvRfPvuCPVmbHENX+lRxMLp39OvIn6kCQQDEzYpPcuHW/7h3TYHYc+T0O6z1VKQT2Mxv92Lj35g1XqV4Oi9xrTj2DtMeV1lMx6n/3icobkCQtuvTI+AcqfTXAkB6bCz9NwUUK8sUsJktV9xJN/JnrTxetOr3h8xfDaJGCuCQdFY+rj6lsLPBTnFUC+Vk4mQVwJIE0mmjFf22NWW5AkAmsVaRGkAmui41Xoq52MdZ8WWm8lY0BLrlBJlvveU6EPqtcZskWW9KiU2euIO5IcRdpvrB6zNMgHpLD9GfMRcPAkBUWOV/dH13v8V2Y/Fzuag/y5k3/oXi/WQnIxdYbltad2xjmofJ7DbB7MJqiZZD8jlr8PCZPwRNzc5ntDStc959");
        System.out.println(sign);

        String sign2 = "ce6EAOj4rhoBMJM5MJCNG4qQ/CVMTWkoRuSGpSzRAnD3U3V5QyHkQUEej2eZXRaa+qSbw2/IJJSPV0sPuAia1+ccb7OnvxyZqkV9wQyimX6qAMz0K+UWFhQ5McCcQ/XsFhhezoVd5QgL7PtdvuK1AtjuzA3J9yzNmwuPssPnKnc=";
        System.out.println(sign.equals(sign2));
    }
}