package org.nonamespace.word.thirdpart.allinpay.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 通联支付配置
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "allinpay")
public class AllinPayConfig {

    /**
     * 统一支付API
     */
    private String baseUrl;

    /**
     * 集团/代理商商户号
     */
    private String orgid;

    /**
     * 商户号
     */
    private String cusid;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 商户私钥
     */
    private String privateKey;

    /**
     * 通联公钥
     */
    private String publicKey;


    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 30000;

    /**
     * 默认通知地址
     */
    private String notifyUrl;
    private String refundNotifyUrl;

    /**
     * 默认前端跳转地址
     */
    private String frontUrl;

    private Integer validTime = 5;
}