//package org.nonamespace.word.thirdpart.allinpay.controller;
//
//
//import com.ruoyi.common.annotation.Anonymous;
//import com.ruoyi.common.core.domain.AjaxResult;
//import jakarta.validation.Valid;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.nonamespace.word.thirdpart.allinpay.dto.PayDto;
//import org.nonamespace.word.thirdpart.allinpay.dto.RefundDto;
//import org.nonamespace.word.thirdpart.allinpay.model.NativePayOutput;
//import org.nonamespace.word.thirdpart.allinpay.service.IAllinPayService;
//import org.nonamespace.word.thirdpart.allinpay.service.IQRCodeService;
//import org.springframework.http.MediaType;
//import org.springframework.web.bind.annotation.*;
//
//@Slf4j
//@RestController
//@RequestMapping("/api/allinpay")
//@RequiredArgsConstructor
//@Anonymous
//public class AllinPayController {
//
//
//    private final IAllinPayService allinPayService;
//    private final IQRCodeService qrCodeService;
//
//
//    /**
//     * 4.15 统一主扫支付
//     * @return
//     */
//    @PostMapping(value = "nativepay", produces = MediaType.IMAGE_PNG_VALUE)
//    public AjaxResult nativepay(@RequestBody @Valid PayDto.NativePayReq req) throws Exception {
//        NativePayOutput nativepay = allinPayService.nativepay(req);
//        // 生成二维码
//        byte[] qrCode = qrCodeService.generateQRCode(nativepay.getPayinfo(), 200, 200);
//        return AjaxResult.success(qrCode);
//    }
//
//
//    /**
//     * 4.3统一撤销API
//     * 使用场景：只能撤销当天的交易，全额退款，实时返回退款结果
//     * @param req
//     * @return
//     */
//    @PutMapping("cancel")
//    public AjaxResult cancel(@RequestBody @Valid RefundDto.Req req) throws Exception {
//        return AjaxResult.success(allinPayService.cancel(req));
//    }
//
//
//    /**
//     * 4.4统一退款API
//     * 使用场景：支持部分金额退款，隔天交易退款。（建议在交易完成后间隔几分钟（最短5分钟）再调用退款接口，避免出现订单状态同步不及时导致退款失败。）
//     * 注：云闪付（银联扫码）含单品优惠交易只能整单退款，不支持部分退款
//     * @param req
//     * @return
//     */
//    @PutMapping("refund")
//    public AjaxResult refund(@RequestBody @Valid RefundDto.Req req) throws Exception {
//        return AjaxResult.success(allinPayService.refund(req));
//    }
//
//
//}
