package org.nonamespace.word.thirdpart.esign.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.thirdpart.esign.domain.EsignTemplateComponents;
import org.nonamespace.word.thirdpart.esign.exception.ESignException;
import org.nonamespace.word.thirdpart.esign.model.QueryTemplateComponentsOutput;

public interface IESignTemplateService extends IService<EsignTemplateComponents> {

    /**
     * 查询合同模板中控件详情
     * 通过合同模板ID查询该模板中所有控件的详细信息，包括控件类型、位置、样式、属性等
     *
     * @param docTemplateId 查询参数
     * @return 控件详情响应
     * @throws ESignException 接口调用异常
     */
    QueryTemplateComponentsOutput queryTemplateComponents(String docTemplateId) throws ESignException;

}
