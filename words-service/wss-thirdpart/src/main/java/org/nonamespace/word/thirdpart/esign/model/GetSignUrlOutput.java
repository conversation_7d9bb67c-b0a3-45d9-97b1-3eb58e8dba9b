package org.nonamespace.word.thirdpart.esign.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.thirdpart.esign.model.base.ESignResponseBase;

/**
 * 获取签署页面链接响应参数
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetSignUrlOutput extends ESignResponseBase {

    /**
     * 业务数据
     */
    private DataInfo data;

    /**
     * 业务数据信息
     */
    @Data
    public static class DataInfo {
        /**
         * 签署短链接（有效期180天）
         */
        private String shortUrl;

        /**
         * 签署长链接（永久有效）
         * 注：小程序H5内嵌场景需要使用长链接，支持自定义域名
         */
        private String url;
    }
}