package org.nonamespace.word.thirdpart.allinpay.dto;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 通联支付交易结果通知DTO
 * 根据通联支付接口文档定义的通知参数
 * <AUTHOR>
 */
@Data
@Slf4j
public class AllinPayNotifyDto {

    /**
     * 收银宝APPID
     */
    private String appid;

    /**
     * 第三方交易号（暂未启用）
     */
    private String outtrxid;

    /**
     * 交易类型
     */
    private String trxcode;

    /**
     * 收银宝交易单号
     */
    private String trxid;

    /**
     * 原始下单金额
     */
    private String initamt;

    /**
     * 交易金额（单位：分）
     */
    private Long trxamt;

    /**
     * 交易请求日期（yyyymmdd）
     */
    private String trxdate;

    /**
     * 交易完成时间（yyyymmddhhmmss）
     */
    private String paytime;

    /**
     * 渠道流水号（如支付宝、微信平台订单号）
     */
    private String chnltrxid;

    /**
     * 交易结果码
     */
    private String trxstatus;

    /**
     * 商户编号
     */
    private String cusid;

    /**
     * 终端编号
     */
    private String termno;

    /**
     * 终端批次号
     */
    private String termbatchid;

    /**
     * 终端流水号
     */
    private String termtraceno;

    /**
     * 终端授权码
     */
    private String termauthno;

    /**
     * 终端参考号
     */
    private String termrefnum;

    /**
     * 业务关联内容（交易备注）
     */
    private String trxreserved;

    /**
     * 原交易流水（通联原交易流水，冲正撤销交易本字段不为空）
     */
    private String srctrxid;

    /**
     * 业务流水（统一下单对应的reqsn订单号）
     */
    private String cusorderid;

    /**
     * 交易账号
     */
    private String acct;

    /**
     * 手续费（单位：分）
     */
    private String fee;

    /**
     * 签名类型（MD5/RSA/SM2，为空默认MD5）
     */
    private String signtype;

    /**
     * 渠道子商户号（仅微信/支付宝交易有效）
     */
    private String cmid;

    /**
     * 渠道号（仅微信交易有效）
     */
    private String chnlid;

    /**
     * 渠道信息（仅返回云闪付/微信/支付宝的渠道信息）
     */
    private String chnldata;

    /**
     * 借贷标识（00-借记卡，02-信用卡，99-其他（花呗/余额等））
     */
    private String accttype;

    /**
     * 发卡行
     */
    private String bankcode;

    /**
     * 用户账号（支付宝买家账号、云闪付userid）
     */
    private String logonid;

    /**
     * 分期数
     */
    private String fqnum;

    /**
     * sign校验码
     */
    private String sign;

    /**
     * 结算周期（1预收，2日结，4月结，8向持卡人收）
     */
    private String feecycle;

    /**
     * 通联渠道侧OPENID
     */
    private String tlopenid;

    /**
     * 基础营销权益抵扣明细（基础营销抵扣明细列表List<MktResult>的json字符串）
     */
    private String mktresult;

    /**
     * 分期商户贴息手续费
     */
    private String feefq;

    /**
     * 判断交易是否成功
     * @return true-成功，false-失败
     */
    public boolean isSuccess() {
        return "0000".equals(trxstatus);
    }

    /**
     * 判断交易是否处理中
     * @return true-处理中，false-非处理中
     */
    public boolean isProcessing() {
        return "2008".equals(trxstatus);
    }

    /**
     * 判断交易是否失败
     * @return true-失败，false-非失败
     */
    public boolean isFailed() {
        return !isSuccess() && !isProcessing();
    }
}