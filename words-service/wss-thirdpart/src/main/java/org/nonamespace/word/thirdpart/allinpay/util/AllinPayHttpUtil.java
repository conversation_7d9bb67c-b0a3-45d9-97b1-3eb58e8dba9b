package org.nonamespace.word.thirdpart.allinpay.util;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import org.springframework.http.MediaType;

import java.util.Map;

/**
 * 通联支付HTTP工具类
 * <AUTHOR>
 */
public class AllinPayHttpUtil {

    /**
     * 发送POST请求
     * 
     * @param url 请求地址
     * @param params 请求参数
     * @param connectTimeout 连接超时时间（毫秒）
     * @param readTimeout 读取超时时间（毫秒）
     * @return 响应结果
     */
    public static String doPost(String url, Map<String, Object> params, int connectTimeout, int readTimeout) {
        try {
            HttpResponse response = HttpUtil.createPost(url)
                    .form(params) // 自动处理表单参数
                    .timeout(connectTimeout + readTimeout)
                    .setConnectionTimeout(connectTimeout)
                    .setReadTimeout(readTimeout)
                    .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE).execute();
            // 返回响应内容
            return response.body();
        } catch (Exception e) {
            throw new RuntimeException("HTTP请求失败: " + e.getMessage(), e);
        }
    }
}