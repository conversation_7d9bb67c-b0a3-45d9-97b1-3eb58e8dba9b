package org.nonamespace.word.thirdpart.esign.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.nonamespace.word.thirdpart.esign.model.inner.TemplateComponentPosition;

/**
 * e签宝合同模板控件管理表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "esign_template_components", autoResultMap = true)
public class EsignTemplateComponents {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String docTemplateId;
    private String componentId;
    private String componentKey;
    private String componentName;
    private Boolean required;
    private Integer componentType;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private TemplateComponentPosition componentPosition;


    private String reflectPropertyId;

}
