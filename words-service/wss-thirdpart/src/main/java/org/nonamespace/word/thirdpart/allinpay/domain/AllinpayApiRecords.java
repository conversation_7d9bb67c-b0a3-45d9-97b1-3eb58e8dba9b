package org.nonamespace.word.thirdpart.allinpay.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 通联支付API记录实体类
 * 用于记录通联支付交易接口的请求参数和响应参数
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@Accessors(chain = true)
@TableName(value = "allinpay_api_records", autoResultMap = true)
public class AllinpayApiRecords {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /** 通联支付请求参数 */
    private String allinpayRequest;

    /** 通联支付响应参数 */
    private String allinpayResponse;

    /** 通联支付API接口名称 */
    private String allinpayApi;

    /** 商户号 */
    private String cusid;

    /** 应用ID */
    private String appid;

    /** 商户订单号 */
    private String reqsn;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT, value = "create_time")
    private Date createTime;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "update_time")
    private Date updateTime;

    private Boolean success;

}
