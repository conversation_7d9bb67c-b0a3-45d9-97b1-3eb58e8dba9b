package org.nonamespace.word.thirdpart.esign.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.thirdpart.esign.config.ESignConfig;
import org.nonamespace.word.thirdpart.esign.exception.ESignException;
import org.nonamespace.word.thirdpart.esign.model.*;
import org.nonamespace.word.thirdpart.esign.service.IESignContractsFileService;
import org.nonamespace.word.thirdpart.esign.service.IESignFlowService;
import org.nonamespace.word.thirdpart.esign.service.IESignTemplateService;
import org.springframework.web.bind.annotation.*;


/**
 * e签宝接口控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/esign")
@RequiredArgsConstructor
public class ESignController {

    private final ESignConfig eSignConfig;
    private final IESignTemplateService esignTemplateComponentsService;
    private final IESignContractsFileService eSignContractsFileService;
    private final IESignFlowService eSignFlowService;

    /**
     * 查询合同模板中控件详情
     * @return 控件详情响应
     */
    @Anonymous
    @GetMapping("/template/components")
    @Operation(summary = "查询合同模板中控件详情", description = "通过合同模板ID查询该模板中所有控件的详细信息")
    public AjaxResult queryTemplateComponents() {

        try {
            log.info("接收查询合同模板中控件详情请求，模板ID: {}", eSignConfig.getDocTemplateId());

            // 构建请求参数
//            QueryTemplateComponentsInput input = QueryTemplateComponentsInput.builder()
//                    .docTemplateId(eSignConfig.getDocTemplateId())
//                    .build();

            // 调用服务
            QueryTemplateComponentsOutput result = esignTemplateComponentsService.queryTemplateComponents(eSignConfig.getDocTemplateId());

//            List<EsignTemplateComponents> esignTemplateComponents = BeanUtil.copyToList(result.getData().getComponents(), EsignTemplateComponents.class);
//            esignTemplateComponents.forEach(component -> component.setDocTemplateId(eSignConfig.getDocTemplateId()));
//            esignTemplateComponentsService.saveBatch(esignTemplateComponents);

            log.info("查询合同模板中控件详情成功，模板ID: {}", eSignConfig.getDocTemplateId());
            return AjaxResult.success(result);

        } catch (Exception e) {
            log.error("查询合同模板中控件详情系统异常，模板ID: {}", eSignConfig.getDocTemplateId(), e);
            return AjaxResult.error("系统异常");
        }
    }

    /**
     * 填写模板生成文件
     * @param input 填写参数
     * @return 生成文件响应
     */
    @Anonymous
    @PostMapping("/template/fill")
    @Operation(summary = "填写模板生成文件", description = "基于合同模板编号和模板中的控件来填充自定义的内容，最终生成一份pdf文件")
    public AjaxResult fillFileByTemplate(@RequestBody CreateFileByTemplateInput input) {

        try {
            log.info("接收填写模板生成文件请求，模板ID: {}, 文件名: {}", input.getDocTemplateId(), input.getFileName());

            // 调用服务
            CreateFileByTemplateOutput result = eSignContractsFileService.createFileByTemplate(input);

            log.info("填写模板生成文件成功，模板ID: {}, 文件ID: {}, 文件名: {}", 
                    input.getDocTemplateId(), result.getFileId(), input.getFileName());
            return AjaxResult.success(result);

        } catch (Exception e) {
            log.error("填写模板生成文件系统异常，模板ID: {}, 文件名: {}", 
                    input.getDocTemplateId(), input.getFileName(), e);
            return AjaxResult.error("系统异常: " + e.getMessage());
        }
    }

    /**
     * 基于文件发起签署
     * @param input 签署流程参数
     * @return 签署流程响应
     */
    @Anonymous
    @PostMapping("/sign-flow/initiate")
    @Operation(summary = "基于文件发起签署", description = "开发者可基于已上传的合同文件或模板所填充生成的文件来发起签署流程")
    public AjaxResult initiateSignFlowByFile(@RequestBody CreateSignFlowByFileInput input) {

        try {
            log.info("收到基于文件发起签署请求，流程主题: {}", 
                    input.getSignFlowConfig() != null ? input.getSignFlowConfig().getSignFlowTitle() : "未设置");
            
            CreateSignFlowByFileOutput result = eSignFlowService.createSignFlowByFile(input);
            
            log.info("基于文件发起签署成功，签署流程ID: {}", result.getData().getSignFlowId());
            return AjaxResult.success(result);
            
        } catch (ESignException e) {
            log.error("基于文件发起签署异常: {}", e.getMessage(), e);
            return AjaxResult.error("基于文件发起签署失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("基于文件发起签署系统异常: {}", e.getMessage(), e);
            return AjaxResult.error("基于文件发起签署失败: " + e.getMessage());
        }
    }

}