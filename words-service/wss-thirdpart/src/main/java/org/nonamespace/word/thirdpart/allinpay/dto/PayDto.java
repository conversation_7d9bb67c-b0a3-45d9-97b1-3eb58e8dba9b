package org.nonamespace.word.thirdpart.allinpay.dto;


import jakarta.validation.constraints.NotBlank;
import lombok.*;

/**
 * 请求支付 request
 */
@Data
public class PayDto {


    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NativePayReq {

        @NotBlank(message = "商户交易流水号不能为空")
        private String cusTrxSeq;

        private String body;
        private String remark;

        private Long trxamt = 0L;


    }

    @Getter
    @Setter
    public static class QueryReq {

        /**
         * 商户订单号
         */
        private String reqsn;

        /**
         * 平台交易流水
         */
        private String trxid;
    }

    @Getter
    @Setter
    public static class CloseReq {

        /**
         * 商户订单号
         */
        private String reqsn;

        /**
         * 平台交易流水号
         */
        private String trxid;
    }
}
