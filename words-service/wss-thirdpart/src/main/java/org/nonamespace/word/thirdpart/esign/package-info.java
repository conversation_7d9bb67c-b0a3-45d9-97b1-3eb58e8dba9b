/**
 * e签宝电子合同接口对接包
 * 
 * <p>本包提供与e签宝电子合同平台的接口对接功能，主要包括：</p>
 * <ul>
 *   <li>合同模板管理</li>
 *   <li>控件信息查询</li>
 *   <li>电子签名相关功能</li>
 * </ul>
 * 
 * <p>包结构说明：</p>
 * <ul>
 *   <li>{@code config} - 配置类，包含e签宝相关配置信息</li>
 *   <li>{@code constants} - 常量定义，包含API路径、响应码等</li>
 *   <li>{@code controller} - 控制器层，提供REST API接口</li>
 *   <li>{@code exception} - 异常定义，包含各种业务异常</li>
 *   <li>{@code model} - 数据模型，包含请求和响应的数据结构</li>
 *   <li>{@code service} - 服务层，包含业务逻辑实现</li>
 *   <li>{@code util} - 工具类，包含HTTP请求、签名生成等工具方法</li>
 * </ul>
 * 
 * <p>使用示例：</p>
 * <pre>{@code
 * // 注入服务
 * @Autowired
 * private IESignService eSignService;
 * 
 * // 查询合同模板中控件详情
 * QueryTemplateComponentsInput input = QueryTemplateComponentsInput.builder()
 *     .docTemplateId("模板ID")
 *     .build();
 * QueryTemplateComponentsOutput output = eSignService.queryTemplateComponents(input);
 * }</pre>
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024
 */
package org.nonamespace.word.thirdpart.esign;