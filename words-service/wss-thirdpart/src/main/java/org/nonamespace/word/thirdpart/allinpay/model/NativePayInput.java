package org.nonamespace.word.thirdpart.allinpay.model;


import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig;
import org.nonamespace.word.thirdpart.allinpay.constants.AllinPayConstants;
import org.nonamespace.word.thirdpart.allinpay.model.base.AllinPayInputBase;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

/**
 * 支付dto
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class NativePayInput extends AllinPayInputBase {

    public NativePayInput(AllinPayConfig config) {
        super(config);
    }

    /**
     * 交易金额	单位为分
     */
    @NonNull
    private Long trxamt = 0L;

    /**
     * 商户交易单号  保证商户平台唯一
     */
    @NonNull
    private String reqsn;

    /**
     * 订单商品名称，为空则以商户名作为商品名称
     */
    @Nullable
    private String body;

    /**
     * 备注信息
     */
    @Nullable
    private String remark;


    /**
     * 订单超时时间。格式为yyyyMMddHHmmss
     */
    @NonNull
    private String expiretime;

    /**
     * 门店号
     */
    @Nullable
    private String subbranch;

    /**
     * 交易完成后平台会将交易结果以后台通
     * 知的方式发送到该地址商户需要以此通
     * 知判断用户订单交易是否成功
     * https只支持默认端口
     */
    @Nullable
    private String notify_url;

    /**
     * 交易完成后平台会按照此地址将用户的
     * 交易结果页面重定向到商户网站同时该
     * 参数为返回商户按钮的指向链接
     */
    @Nullable
    private String front_url;

    private final String signtype = AllinPayConstants.SignType.RSA;

    /**
     * 签名
     */
    @NonNull
    private String sign;

    @NonNull
    private String randomstr;

    /**
     * 版本号
     * 接口版本号，默认填12
     */
    private final Integer version = 12;


    public boolean isValid() {
        return StrUtil.isNotBlank(getCusid()) && StrUtil.isNotBlank(getAppid()) && getTrxamt() > 0;
    }
}
