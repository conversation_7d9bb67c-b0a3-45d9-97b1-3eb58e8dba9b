package org.nonamespace.word.thirdpart.esign.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.thirdpart.esign.model.base.ESignResponseBase;

/**
 * 基于文件发起签署响应参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CreateSignFlowByFileOutput extends ESignResponseBase {

    private SignFlow data;

    @Data
    public static class SignFlow {

        /**
         * 签署流程ID
         */
        private String signFlowId;

    }

}