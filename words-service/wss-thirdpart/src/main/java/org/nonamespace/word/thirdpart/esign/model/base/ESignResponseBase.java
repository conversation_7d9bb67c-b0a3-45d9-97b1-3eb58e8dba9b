package org.nonamespace.word.thirdpart.esign.model.base;

import lombok.Data;
import org.nonamespace.word.thirdpart.esign.constants.ESignConstants;

/**
 * e签宝响应基类
 * <AUTHOR>
 */
@Data
public class ESignResponseBase {

    /**
     * 业务码，0表示成功，非0表示异常
     */
    private Integer code;

    /**
     * 业务信息
     * 请根据 code 来判断错误情况，不应该依赖 message匹配，因为 message 可能会调整
     */
    private String message;

    /**
     * 判断请求是否成功
     * @return true-成功，false-失败
     */
    public boolean isSuccess() {
        return ESignConstants.ResponseCode.SUCCESS == (code != null ? code : -1);
    }

    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return String.format("[%d] %s", code, message);
    }
}