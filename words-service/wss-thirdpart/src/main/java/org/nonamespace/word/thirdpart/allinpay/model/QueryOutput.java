package org.nonamespace.word.thirdpart.allinpay.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.thirdpart.allinpay.model.base.AllinPayOutputBase;

/**
 * 通联支付统一查询接口输出参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryOutput extends AllinPayOutputBase {

    /**
     * 商户号
     */
    private String cusid;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 交易单号
     */
    private String trxid;

    /**
     * 支付渠道交易单号
     */
    private String chnltrxid;

    /**
     * 商户订单号
     */
    private String reqsn;

    /**
     * 交易类型
     */
    private String trxcode;

    /**
     * 交易金额（单位为分）
     */
    private String trxamt;

    /**
     * 支付平台用户标识
     */
    private String acct;

    /**
     * 交易完成时间
     */
    private String fintime;

    /**
     * 随机字符串
     */
    private String randomstr;

    /**
     * 错误原因
     */
    private String errmsg;

    /**
     * 渠道子商户号
     */
    private String cmid;

    /**
     * 渠道号
     */
    private String chnlid;

    /**
     * 原交易金额
     */
    private String initamt;

    /**
     * 手续费
     */
    private String fee;

    /**
     * 渠道信息
     */
    private String chnldata;

    /**
     * 借贷标识
     */
    private String accttype;

    /**
     * 所属银行
     */
    private String bankcode;

    /**
     * 交易备注
     */
    private String trxreserve;

    /**
     * 签名
     * 响应签名
     */
    private String sign;

}