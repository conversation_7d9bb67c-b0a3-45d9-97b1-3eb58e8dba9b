package org.nonamespace.word.thirdpart.esign.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.thirdpart.esign.config.ESignConfig;
import org.nonamespace.word.thirdpart.esign.constants.ESignConstants;
import org.nonamespace.word.thirdpart.esign.exception.ESignException;
import org.nonamespace.word.thirdpart.esign.model.CreateFileByTemplateInput;
import org.nonamespace.word.thirdpart.esign.model.CreateFileByTemplateOutput;
import org.nonamespace.word.thirdpart.esign.service.IESignContractsFileService;
import org.nonamespace.word.thirdpart.esign.util.ESignHttpUtil;
import org.springframework.stereotype.Service;


/**
 * 合同文件服务类u
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ESignContractsFileServiceImpl implements IESignContractsFileService {

    private final ESignConfig eSignConfig;


    @Override
    public CreateFileByTemplateOutput createFileByTemplate(CreateFileByTemplateInput input) throws ESignException {
        log.info("开始填写模板生成文件，模板ID: {}, 文件名: {}", input.getDocTemplateId(), input.getFileName());

        try {
            // 参数校验
            validateCreateFileByTemplateInput(input);

            // 构建请求URL
            String url = ESignHttpUtil.buildUrl(eSignConfig, ESignConstants.ApiPath.CREATE_FILE_BY_TEMPLATE);

            // 发送POST请求
            String requestBody = JSONUtil.toJsonStr(input);
            CreateFileByTemplateOutput response = ESignHttpUtil.sendPostRequest(
                    eSignConfig, url, requestBody, CreateFileByTemplateOutput.class);

            if (response == null) {
                throw new ESignException.ProcessingException("接口响应为空");
            }

            if (!response.isSuccess()) {
                log.error("填写模板生成文件失败: {}", response.getErrorMessage());
                throw new ESignException.ProcessingException(
                        response.getCode() != null ? response.getCode() : -1,
                        response.getMessage() != null ? response.getMessage() : "未知错误"
                );
            }

            log.info("填写模板生成文件成功，模板ID: {}, 文件ID: {}, 文件名: {}",
                    input.getDocTemplateId(), response.getFileId(), input.getFileName());

            return response;

        } catch (ESignException e) {
            log.error("填写模板生成文件异常，模板ID: {}, 错误: {}", input.getDocTemplateId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("填写模板生成文件系统异常，模板ID: {}", input.getDocTemplateId(), e);
            throw new ESignException.ProcessingException("系统异常: " + e.getMessage(), e);
        }
    }

    /**
     * 校验填写模板生成文件的输入参数
     * @param input 输入参数
     * @throws ESignException.ParameterException 参数异常
     */
    private void validateCreateFileByTemplateInput(CreateFileByTemplateInput input) throws ESignException.ParameterException {
        if (input == null) {
            throw new ESignException.ParameterException("请求参数不能为空");
        }

        if (StrUtil.isBlank(input.getDocTemplateId())) {
            throw new ESignException.ParameterException("合同模板ID不能为空");
        }

        if (StrUtil.isBlank(input.getFileName())) {
            throw new ESignException.ParameterException("文件名称不能为空");
        }

        // 检查文件名长度
        if (input.getFileName().length() > 100) {
            throw new ESignException.ParameterException("文件名称长度不能超过100字符");
        }

        // 检查文件名特殊字符
        String fileName = input.getFileName();
        String[] forbiddenChars = {"/", "\\", ":", "*", "\"", "<", ">", "|", "？"};
        for (String forbiddenChar : forbiddenChars) {
            if (fileName.contains(forbiddenChar)) {
                throw new ESignException.ParameterException("文件名称不能包含特殊字符: " + forbiddenChar);
            }
        }

        if (input.getComponents() == null) {
            throw new ESignException.ParameterException("控件列表不能为空");
        }

        // 校验控件信息
        for (CreateFileByTemplateInput.ComponentInfo component : input.getComponents()) {
            if (StrUtil.isBlank(component.getComponentId()) && StrUtil.isBlank(component.getComponentKey())) {
                throw new ESignException.ParameterException("控件ID和控件Key至少需要传入一个");
            }
        }

        // 检查配置
        if (!eSignConfig.isEnabled()) {
            throw new ESignException.ParameterException("e签宝服务未启用");
        }

        if (StrUtil.isBlank(eSignConfig.getBaseUrl())) {
            throw new ESignException.ParameterException("e签宝baseUrl未配置");
        }

        if (StrUtil.isBlank(eSignConfig.getAppId())) {
            throw new ESignException.ParameterException("e签宝appId未配置");
        }

        if (StrUtil.isBlank(eSignConfig.getAppSecret())) {
            throw new ESignException.ParameterException("e签宝appSecret未配置");
        }
    }
}
