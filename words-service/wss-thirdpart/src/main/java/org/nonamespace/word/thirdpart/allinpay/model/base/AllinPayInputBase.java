package org.nonamespace.word.thirdpart.allinpay.model.base;

import lombok.Getter;
import org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig;
import org.nonamespace.word.thirdpart.allinpay.constants.AllinPayConstants;


/**
 * 聚合支付 基类
 * <AUTHOR>
 */
@Getter
public class AllinPayInputBase {

    /**
     * 集团/代理商商户号
     * 共享集团号/代理商参数时必填
     */
    private final String orgid;

    /**
     * 商户号
     * 平台分配的商户号
     */
    private final String cusid;

    /**
     * 应用ID
     * 平台分配的APPID
     */
    private final String appid;

    /**
     * 签名类型，RSA
     */
    private final String signtype = AllinPayConstants.SignType.RSA;

    private AllinPayInputBase() {
        throw new IllegalCallerException("该类暂不支持无参构造，谢谢！");
    }

    public AllinPayInputBase(AllinPayConfig config) {
        this.cusid = config.getCusid();
        this.appid = config.getAppid();
        this.orgid = config.getOrgid();
    }
}
