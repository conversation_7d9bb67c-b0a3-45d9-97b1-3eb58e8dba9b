package org.nonamespace.word.thirdpart.allinpay.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.thirdpart.allinpay.constants.AllinPayConstants;
import org.nonamespace.word.thirdpart.allinpay.model.base.AllinPayOutputBase;

/**
 * 通联支付统一退款API响应参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RefundOutput extends AllinPayOutputBase {

    /**
     * 商户号
     * 平台分配的商户号
     */
    private String cusid;

    /**
     * 应用ID
     * 平台分配的APPID
     */
    private String appid;

    /**
     * 交易单号
     * 收银宝平台的退款交易流水号
     */
    private String trxid;

    /**
     * 商户订单号
     * 商户的退款交易订单号
     */
    private String reqsn;

    /**
     * 退款金额
     * 单位为分
     */
    private Integer trxamt;

    /**
     * 交易完成时间
     * yyyyMMddHHmmss
     */
    private String fintime;

    /**
     * 错误原因
     * 失败的原因说明
     */
    private String errmsg;

    /**
     * 手续费
     */
    private String fee;

    /**
     * 交易类型
     * 见附录交易类型说明
     */
    private String trxcode;

    /**
     * 随机字符串
     * 随机生成的字符串
     */
    private String randomstr;

    /**
     * 渠道流水号
     * 如支付宝，微信平台订单号
     */
    private String chnltrxid;

    /**
     * 渠道信息
     */
    private String chnldata;

    /**
     * 所属银行
     */
    private String bankcode;

    /**
     * 签名
     * 详见安全规范
     */
    private String sign;

}