package org.nonamespace.word.thirdpart.allinpay.model.base;

import lombok.Data;
import org.nonamespace.word.thirdpart.allinpay.constants.AllinPayConstants;

@Data
public class AllinPayOutputBase {

    /**
     * SUCCESS/FAIL
     */
    private String retcode;

    /**
     * 返回码说明
     */
    private String retmsg;

    /**
     * 交易状态
     * 交易的状态，见附录交易返回码说明
     */
    private String trxstatus;

    /**
     * 判断请求是否成功
     * @return true-成功，false-失败
     */
    public boolean isSuccess() {
        return AllinPayConstants.ResponseStatus.SUCCESS.equals(getRetcode());
    }

    /**
     * 判断交易是否成功
     * @return true-交易成功，false-交易失败或处理中
     */
    public boolean isTransactionSuccess() {
        return isSuccess() && AllinPayConstants.ResponseStatus.TRX_SUCCESS.equals(trxstatus);
    }

    /**
     * 判断交易是否处理中
     * @return true-处理中，false-非处理中
     */
    public boolean isProcessing() {
        return isSuccess() && AllinPayConstants.ResponseStatus.TRX_PROCESSING.equals(trxstatus);
    }
}
