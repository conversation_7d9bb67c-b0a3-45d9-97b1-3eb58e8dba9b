package org.nonamespace.word.thirdpart.allinpay.constants;

public interface AllinPayConstants {

    interface SignType {
        String RSA = "RSA";
        String SM2 = "SM2";
    }

    interface ResponseStatus {
        String SUCCESS = "SUCCESS";
        String FAIL = "FAIL";

        String TRX_SUCCESS = "0000";
        String TRX_PROCESSING = "2008";
    }

    /**
     * 交易接口类型
     */
    interface AllinPayType {
        String NATIVE_PAY = "nativepay";
        String CANCEL = "cancel";
        String REFUND = "refund";
        String QUERY = "query";
        String CLOSE = "close";
        String PAY_NOTIFY = "pay_notify";
    }

}
