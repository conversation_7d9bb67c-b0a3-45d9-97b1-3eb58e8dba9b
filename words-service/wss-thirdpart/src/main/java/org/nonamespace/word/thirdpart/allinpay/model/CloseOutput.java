package org.nonamespace.word.thirdpart.allinpay.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.thirdpart.allinpay.model.base.AllinPayOutputBase;

/**
 * 通联支付统一订单关闭接口输出参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CloseOutput extends AllinPayOutputBase {

    /**
     * 商户号
     */
    private String cusid;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 平台交易流水号
     */
    private String trxid;

    /**
     * 商户订单号
     */
    private String reqsn;

    /**
     * 交易金额（分）
     */
    private String trxamt;

    /**
     * 完成时间
     */
    private String fintime;

    /**
     * 错误信息
     */
    private String errmsg;

    /**
     * 签名
     */
    private String sign;

    /**
     * 获取签名
     *
     * @return 签名
     */
    public String getSign() {
        return sign;
    }
}