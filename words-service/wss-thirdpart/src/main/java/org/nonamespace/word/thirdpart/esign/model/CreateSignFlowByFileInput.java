package org.nonamespace.word.thirdpart.esign.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.nonamespace.word.thirdpart.esign.model.inner.SignFieldPosition;

import java.util.List;

/**
 * 基于文件发起签署请求参数
 * <AUTHOR>
 */
@Data
public class CreateSignFlowByFileInput {

    /**
     * 设置待签署文件信息
     */
    private List<DocInfo> docs;

    /**
     * 设置附属材料信息（附件，指无需签名的文件，仅用于查看）
     */
    private List<AttachmentInfo> attachments;

    /**
     * 签署流程配置项
     */
    private SignFlowConfig signFlowConfig;

    /**
     * 签署方信息
     */
    private List<SignerInfo> signers;

    /**
     * 待签署文件信息
     */
    @Data
    @NoArgsConstructor
    public static class DocInfo {

        public DocInfo(String fileId, String fileName) {
            this.fileId = fileId;
            this.fileName = fileName;
        }

        /**
         * 待签署文件ID
         * 41f624ba500944d4b606094c7bbfe897
         */
        private String fileId;

        /**
         * 文件名称（需要添加PDF文件后缀名，"xxx.pdf"）
         * 注：文件名称不可含有以下9个特殊字符：/ \ : * " < > | ？以及所有emoji表情
         * 文件名称长度限制不能超过100字符
         */
        private String fileName;

        /**
         * 是否需要密码，默认false
         */
        private Boolean neededPwd;

        /**
         * 文件编辑密码
         */
        private String fileEditPwd;

        /**
         * 合同类型ID
         */
        private String contractBizTypeId;

        /**
         * 文件在签署页面的展示顺序
         * 按序展示时支持传入顺序值：1 - 50（值越小越靠前）
         */
        private Integer order;
    }

    /**
     * 附属材料信息
     */
    @Data
    public static class AttachmentInfo {
        /**
         * 附属材料文件ID
         */
        private String fileId;

        /**
         * 附属材料名称（需要添加文件真实后缀名，例如："xxx.pdf"）
         * 注：名称不可含有以下9个特殊字符：/ \ : * " < > | ？以及所有emoji表情
         */
        private String fileName;
    }

    /**
     * 签署流程配置项
     */
    @Data
    public static class SignFlowConfig {
        /**
         * 签署流程主题（将展示在签署通知和签署页的任务信息中）
         * 注：主题名称不可含有以下9个特殊字符：/ \ : * " < > | ？以及所有emoji表情
         */
        private String signFlowTitle;

        /**
         * 签署截止时间， unix时间戳（毫秒）格式
         * 默认在签署流程创建后的90天时截止（指定值最大不能超过90天，只能指定90天内的时间戳）
         */
        private Long signFlowExpireTime;

        /**
         * 自动开启签署流程，默认值 true
         * true - 自动开启（发起签署流程，将直接进入"签署中"状态）
         * false - 非自动开启（发起"草稿"状态的签署流程，需调用【开启签署流程】接口后流程进入"签署中"状态）
         */
        private Boolean autoStart;

        /**
         * 所有签署方签署完成后流程自动完结，默认值 false
         * true - 自动完结
         * false - 非自动完结，需调用【完结签署流程】接口完结
         */
        private Boolean autoFinish;

        /**
         * 身份校验配置项，默认：true
         * true - 接口报错（提示：传入的指定签署人信息与实名信息不一致相关报错）
         * false - 不报错，正常发起（签署人可以在签署链接中修改账号信息）
         */
        private Boolean identityVerify;

        /**
         * 接收相关回调通知的地址
         */
        private String notifyUrl;

        private RedirectConfig redirectConfig;

        /**
         * 通知配置
         */
        private NoticeConfig noticeConfig;


        /**
         * 合同相关配置项
         */
        private ContractConfig contractConfig;


        @Data
        public static class RedirectConfig {

            /**
             * 签署完成后的跳转地址
             */
            private String redirectUrl;

            /**
             * 操作完成重定向跳转延迟时间，单位秒（可选值0、3，默认值为 3）
             *
             * 传0时，签署完成直接跳转重定向地址；
             * 传3时，展示签署完成结果页，倒计时3秒后，自动跳转重定向地址。
             * 【注】当redirectUrl不传的情况下，该字段无需传入，签署完成不跳转
             */
            private int redirectDelayTime = 3;

        }


        @Data
        public static class ContractConfig {
            /**
             * 合同保密配置，默认：1 - 合同不保密
             * 1 - 合同不保密
             * 2 - 合同全部保密
             * （合同保密功能同e签宝SaaS官网一致，点击了解）
             */
            private Integer contractSecrecy;

            /**
             * 该签署流程是否允许发起解约，默认true
             * true - 允许
             * false - 不允许
             */
            private Boolean allowToRescind;

            /**
             * 合同到期时间， unix时间戳（毫秒）格式
             * 补充说明：
             * 当贵司签署的合同即将到期需要提醒客户进行续约，可设置该时间；
             * 默认合同到期前30天进行提醒，如需更改提醒规则，请通过e签宝SaaS官网进行设置，点此了解 如何设置合同履约提醒（履约提醒设置仅高级版及以上版本可用）。
             */
            private Long contractExpirationDate;
        }
    }

    /**
     * 签署方信息
     */
    @Data
    public static class SignerInfo {
        /**
         * 签署方类型
         * 0 - 个人签署方
         * 1 - 企业签署方
         */
        private Integer signerType;


        /**
         * 通知配置
         */
        private NoticeConfig noticeConfig;


        /**
         * 个人签署方信息
         * 当签署主体为个人时请传此对象，psnAccount与psnId二选一传值即可
         */
        private PsnSignerInfo psnSignerInfo;

        /**
         * 签署区信息
         */
        private List<SignFieldInfo> signFields;
    }

    @Data
    public static class PsnSignerInfo {
        /**
         * 个人账号标识（手机号或邮箱）用于登录e签宝官网的凭证
         * 【注】为了保证签署人准确，必须配合psnName（个人姓名）传入
         */
        private String psnAccount;
        /**
         * 个人账号ID
         * 【注】用户在e签宝注册实名后才有账号ID，账号ID获取方式请使用【查询个人认证信息】接口通过个人账号标识（手机号或邮箱）/个人用户的证件号进行查询
         */
        private String psnId;
        /**
         * 个人签署方身份信息
         * 补充说明：
         * 已实名用户，若传入的psnInfo与在e签宝绑定的psnAccount一致，则无需重复实名，签署页直接进行签署意愿认证；
         * 已实名用户，若传入的psnInfo与在e签宝绑定的psnAccount不一致，则接口将会报错，建议核实用户身份信息后重新发起流程；
         * 未实名用户，签署页将根据传入的身份信息进行用户实名认证。
         */
        private PsnInfo psnInfo;


        @Data
        public static class PsnInfo {

            /**
             * 个人姓名
             * 【注】传psnAccount（个人账号标识）时，该参数为必传项
             */
            private String psnName;
            /**
             * 个人证件号
             */
            private String psnIDCardNum;
            /**
             * 个人证件类型，可选值如下：
             * CRED_PSN_CH_IDCARD - 中国大陆居民身份证（默认值）
             * CRED_PSN_CH_HONGKONG - 香港来往大陆通行证（回乡证）
             * CRED_PSN_CH_MACAO - 澳门来往大陆通行证（回乡证）
             * CRED_PSN_CH_TWCARD - 台湾来往大陆通行证（台胞证）
             * CRED_PSN_PASSPORT - 护照
             * 【注】CRED_PSN_CH_IDCARD 类型同时兼容港澳台居住证（81、82、83开头18位证件号）、外国人永久居住证（9开头18位证件号）
             */
            private String psnIDCardType;
            /**
             * 个人手机号（运营商实名登记手机号或银行卡预留手机号，仅用于认证）
             */
            private String psnMobile;
            /**
             * 个人银行卡号
             */
            private String bankCardNum;

        }
    }

    /**
     * 签署区信息
     */
    @Data
    public static class SignFieldInfo {
        /**
         * 文件ID
         */
        private String fileId;

        /**
         * 签署区类型，默认值为 0
         * 0 - 签章区 （添加印章、签名等）
         * 1 - 备注区（添加备注文字信息等）（点击了解 备注签署）
         * 2 - 独立签署日期（添加单独的签署日期）
         */
        private Integer signFieldType;

        /**
         * 签章区配置项（指定signFieldType为 0 - 签章区时，该参数为必传项）
         */
        private NormalSignFieldConfig normalSignFieldConfig;

        /**
         * 是否必须签署，默认true
         */
        private Boolean mustSign;
    }


    /**
     *
     */
    @Data
    public static class NormalSignFieldConfig {
        private Boolean freeMode = Boolean.FALSE;
        private Boolean autoSign = Boolean.FALSE;
        private Boolean movableSignField = Boolean.FALSE;
        private String assignedSealId;

        /**
         * 签章区样式
         * 1 - 单页签章，2 - 骑缝签章
         */
        private Integer signFieldStyle = 1;

        private SignFieldPosition signFieldPosition;
    }




    @Data
    public static class NoticeConfig {
        /**
         * 通知类型，通知发起方、签署方、抄送方，默认不通知（值为""空字符串），允许多种通知方式，请使用英文逗号分隔
         * （点解了解 指定e签宝短信/邮件通知签署）
         * "" - 不通知（默认值）
         * 1 - 短信通知（如果套餐内带“分项”字样，请确保开通【电子签名流量费（分项）认证】中的子项：【短信服务】，否则短信通知收不到）
         * 2 - 邮件通知
         * 3 - 钉钉工作通知（需使用e签宝钉签产品）
         * 5 - 微信通知（用户需关注“e签宝电子签名”微信公众号且使用过e签宝微信小程序）
         * 6 - 企业微信通知（需要使用e签宝企微版产品）
         * 7 - 飞书通知（需要使用e签宝飞书版产品）
         * 补充说明：
         * 1、2：个人账号中需要绑定短信/邮件才有对应的通知方式；
         * 3、5、6、7：仅限e签宝正式环境调用才会有。
         */
        private String noticeTypes;

        /**
         * 通知给企业印章用印审批人员的通知类型，按照账号中的手机号或邮箱的填写情况进行通知。
         * true - 发送消息（短信+邮件+e签宝官网站内信）
         * （如果套餐内带“分项”字样，请确保开通【电子签名流量费（分项）认证】中的子项：【短信服务】，否则短信通知收不到）
         * false - 不发送消息
         * 【注】不传值默认取noticeTypes配置的通知方式
         */
        private Boolean examineNotice;
    }
}