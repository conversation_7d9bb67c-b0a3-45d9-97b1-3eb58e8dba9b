package org.nonamespace.word.thirdpart.allinpay.dto;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.validator.constraints.Length;

@Data
public class RefundDto {


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Req {

        @NotNull(message = "退款金额不能为空")
        @Min(value = 1, message = "退款金额不能小于1分")
        private Long amount;


        @NotBlank(message = "原商户退款交易流水号不能为空")
        private String originalTrxId;

        @NotBlank(message = "商户退款交易流水号不能为空")
        private String refundTrxId;


        /**
         * 备注
         */
        @Length(max = 150, message = "备注信息不能超过150个字符")
        private String remark;
    }
}
