package org.nonamespace.word.thirdpart.esign.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.thirdpart.esign.model.base.ESignResponseBase;

import java.util.List;

/**
 * 下载已签署文件及附属材料响应参数
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetFileDownloadUrlOutput extends ESignResponseBase {

    /**
     * 业务数据
     */
    private DataInfo data;

    /**
     * 业务数据信息
     */
    @Data
    public static class DataInfo {
        /**
         * 签署文件信息
         */
        private List<FileInfo> files;

        /**
         * 附属材料信息
         */
        private List<AttachmentInfo> attachments;

        /**
         * 海外签证书报告下载地址（有效期为60分钟，过期后可以重新调用接口获取新的下载地址）
         * 注：默认中国大陆签署不返回值
         */
        private String certificateDownloadUrl;
    }

    /**
     * 签署文件信息
     */
    @Data
    public static class FileInfo {
        /**
         * 签署文件ID
         */
        private String fileId;

        /**
         * 签署文件名称
         */
        private String fileName;

        /**
         * 已签署文件下载链接（有效期为60分钟，过期后可以重新调用接口获取新的下载地址）
         */
        private String downloadUrl;
    }

    /**
     * 附属材料信息
     */
    @Data
    public static class AttachmentInfo {
        /**
         * 附属材料文件ID
         */
        private String fileId;

        /**
         * 附属材料文件名称
         */
        private String fileName;

        /**
         * 附属材料文件下载链接（有效期为60分钟，过期后可以重新调用接口获取新的下载地址）
         */
        private String downloadUrl;
    }
}