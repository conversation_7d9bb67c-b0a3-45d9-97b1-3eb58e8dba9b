package org.nonamespace.word.thirdpart.allinpay.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.thirdpart.allinpay.constants.AllinPayConstants;
import org.nonamespace.word.thirdpart.allinpay.model.base.AllinPayOutputBase;

/**
 * 通联支付统一撤销API响应结果
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CancelOutput extends AllinPayOutputBase {

    /**
     * 商户号
     * 平台分配的商户号
     * 以下信息只有当retcode为SUCCESS时有返回
     */
    private String cusid;

    /**
     * 应用ID
     * 平台分配的APPID
     */
    private String appid;

    /**
     * 交易单号
     * 收银宝平台的退款交易流水号
     */
    private String trxid;

    /**
     * 商户订单号
     * 商户的退款交易订单号
     */
    private String reqsn;

    /**
     * 交易完成时间
     * yyyyMMddHHmmss
     */
    private String fintime;

    /**
     * 错误原因
     * 失败的原因说明
     */
    private String errmsg;

    /**
     * 交易类型
     * 见交易类型说明
     */
    private String trxcode;

    /**
     * 随机字符串
     * 随机生成的字符串
     */
    private String randomstr;

    /**
     * 所属银行
     */
    private String bankcode;

    /**
     * 签名
     * 详见安全规范
     */
    private String sign;

}