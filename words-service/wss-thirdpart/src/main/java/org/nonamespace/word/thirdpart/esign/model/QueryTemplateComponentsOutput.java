package org.nonamespace.word.thirdpart.esign.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.thirdpart.esign.model.base.ESignResponseBase;
import org.nonamespace.word.thirdpart.esign.model.inner.TemplateComponentPosition;

import java.util.List;

/**
 * 查询合同模板中控件详情响应
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryTemplateComponentsOutput extends ESignResponseBase {

    /**
     * 响应数据
     */
    private TemplateData data;

    /**
     * 模板数据
     */
    @Data
    public static class TemplateData {
        /**
         * 合同模板ID
         */
        private String docTemplateId;

        /**
         * 合同模板名称
         */
        private String docTemplateName;

        /**
         * 创建时间（毫秒时间戳）
         */
        private Long createTime;

        /**
         * 更新时间（毫秒时间戳）
         */
        private Long updateTime;

        /**
         * 文件下载链接
         */
        private String fileDownloadUrl;

        /**
         * 控件列表
         */
        private List<Component> components;
    }

    /**
     * 控件信息
     */
    @Data
    public static class Component {
        /**
         * 控件ID
         */
        private String componentId;

        /**
         * 控件类型
         * 1-单行文本，2-数字，3-日期，6-签章区域，8-多行文本，9-复选，10-单选，11-图片，14-下拉框，15-勾选框，16-身份证，17-备注区域，18-动态表格，19-手机号
         */
        private Integer componentType;

        /**
         * 控件名称
         */
        private String componentName;

        /**
         * 控件Key
         */
        private String componentKey;

        /**
         * 是否必填
         * true-必填，false-非必填
         */
        private Boolean required;

        /**
         * 控件位置信息
         */
        private TemplateComponentPosition componentPosition;

        /**
         * 控件特有属性
         */
        private ComponentAttribute componentSpecialAttribute;
    }

    /**
     * 控件特有属性
     */
    @Data
    public static class ComponentAttribute {
        /**
         * 日期格式（日期控件特有）
         */
        private String dateFormat;

        /**
         * 图片类型（图片控件特有）
         * IDCard_widthwise（身份证横向，等比缩放大小）
         * IDCard_longitudinal（身份证纵向，等比缩放大小）
         * other （其他，自由缩放大小）
         */
        private String imageType;


        /**
         * 选项（下拉框控件、单选控件、多选控件特有）
         */
        private List<Options> options;

        /**
         * 签署方角色标识（签署区控件）
         */
        private String signerRole;


        @Data
        public static class Options {
            /**
             * 选项内容
             */
            private String optionContent;

            /**
             * 选项顺序
             */
            private Integer optionOrder;

            /**
             * 选项是否默认选中
             */
            private Boolean selected;

        }
    }
}