//package org.nonamespace.word.thirdpart.esign.service;
//
//import org.junit.jupiter.api.Test;
//import org.nonamespace.word.thirdpart.esign.model.CreateSignFlowByFileInput;
//import org.nonamespace.word.thirdpart.esign.model.CreateSignFlowByFileOutput;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * e签宝基于文件发起签署接口测试
// *
// * <AUTHOR>
// * @date 2024-01-01
// */
//@SpringBootTest
//@ActiveProfiles("test")
//public class CreateSignFlowByFileTest {
//
//    @Resource
//    private IESignService eSignService;
//
//    @Test
//    public void testCreateSignFlowByFile() {
//        try {
//            // 创建基于文件发起签署请求
//            CreateSignFlowByFileInput input = new CreateSignFlowByFileInput();
//
//            // 设置文档信息
//            List<CreateSignFlowByFileInput.DocInfo> docs = new ArrayList<>();
//            CreateSignFlowByFileInput.DocInfo doc = new CreateSignFlowByFileInput.DocInfo();
//            doc.setFileId("test_file_id_123456");
//            doc.setFileName("测试合同.pdf");
//            docs.add(doc);
//            input.setDocs(docs);
//
//            // 设置签署流程配置
//            CreateSignFlowByFileInput.SignFlowConfig config = new CreateSignFlowByFileInput.SignFlowConfig();
//            config.setSignFlowTitle("测试合同签署流程");
//            config.setAutoArchive(true);
//            config.setBusinessScene("PERSONAL_CONTRACT");
//            config.setContractRemind(true);
//            config.setContractValidity("2024-12-31 23:59:59");
//            config.setSignPlatform(1);
//            input.setSignFlowConfig(config);
//
//            // 设置签署人信息
//            List<CreateSignFlowByFileInput.SignerInfo> signers = new ArrayList<>();
//            CreateSignFlowByFileInput.SignerInfo signer = new CreateSignFlowByFileInput.SignerInfo();
//            signer.setSignerType(0);
//            signer.setPsnAccount("<EMAIL>");
//            signer.setSignerName("测试用户");
//            signer.setMobile("***********");
//
//            // 设置签署域
//            List<CreateSignFlowByFileInput.SignField> signFields = new ArrayList<>();
//            CreateSignFlowByFileInput.SignField signField = new CreateSignFlowByFileInput.SignField();
//            signField.setFileId("test_file_id_123456");
//
//            CreateSignFlowByFileInput.NormalSignFieldConfig fieldConfig = new CreateSignFlowByFileInput.NormalSignFieldConfig();
//            fieldConfig.setFreeMode(true);
//            fieldConfig.setSignFieldStyle(1);
//
//            CreateSignFlowByFileInput.SignFieldPosition position = new CreateSignFlowByFileInput.SignFieldPosition();
//            position.setPositionPage("1");
//            position.setPositionX(100.0);
//            position.setPositionY(200.0);
//            fieldConfig.setSignFieldPosition(position);
//
//            signField.setNormalSignFieldConfig(fieldConfig);
//            signFields.add(signField);
//            signer.setSignFields(signFields);
//
//            signers.add(signer);
//            input.setSigners(signers);
//
//            // 调用接口
//            CreateSignFlowByFileOutput result = eSignService.createSignFlowByFile(input);
//
//            System.out.println("=== 基于文件发起签署测试结果 ===");
//            System.out.println("签署流程ID: " + result.getSignFlowId());
//            System.out.println("状态: " + result.getStatus());
//            System.out.println("创建时间: " + result.getCreateTime());
//            System.out.println("截止时间: " + result.getDeadline());
//
//        } catch (Exception e) {
//            System.err.println("基于文件发起签署测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testCreateSignFlowByFileWithAttachments() {
//        try {
//            // 创建基于文件发起签署请求（包含附件）
//            CreateSignFlowByFileInput input = new CreateSignFlowByFileInput();
//
//            // 设置文档信息
//            List<CreateSignFlowByFileInput.DocInfo> docs = new ArrayList<>();
//            CreateSignFlowByFileInput.DocInfo doc = new CreateSignFlowByFileInput.DocInfo();
//            doc.setFileId("main_file_id_123456");
//            doc.setFileName("主合同文件.pdf");
//            docs.add(doc);
//            input.setDocs(docs);
//
//            // 设置附件信息
//            List<CreateSignFlowByFileInput.AttachmentInfo> attachments = new ArrayList<>();
//            CreateSignFlowByFileInput.AttachmentInfo attachment = new CreateSignFlowByFileInput.AttachmentInfo();
//            attachment.setFileId("attachment_file_id_123456");
//            attachment.setFileName("附件文档.pdf");
//            attachments.add(attachment);
//            input.setAttachments(attachments);
//
//            // 设置签署流程配置
//            CreateSignFlowByFileInput.SignFlowConfig config = new CreateSignFlowByFileInput.SignFlowConfig();
//            config.setSignFlowTitle("带附件的合同签署流程");
//            config.setAutoArchive(true);
//            config.setBusinessScene("PERSONAL_CONTRACT");
//            config.setContractRemind(true);
//            config.setContractValidity("2024-12-31 23:59:59");
//            config.setSignPlatform(1);
//            input.setSignFlowConfig(config);
//
//            // 设置签署人信息
//            List<CreateSignFlowByFileInput.SignerInfo> signers = new ArrayList<>();
//            CreateSignFlowByFileInput.SignerInfo signer = new CreateSignFlowByFileInput.SignerInfo();
//            signer.setSignerType(0);
//            signer.setPsnAccount("<EMAIL>");
//            signer.setSignerName("测试用户2");
//            signer.setMobile("***********");
//
//            // 设置签署域
//            List<CreateSignFlowByFileInput.SignField> signFields = new ArrayList<>();
//            CreateSignFlowByFileInput.SignField signField = new CreateSignFlowByFileInput.SignField();
//            signField.setFileId("main_file_id_123456");
//
//            CreateSignFlowByFileInput.NormalSignFieldConfig fieldConfig = new CreateSignFlowByFileInput.NormalSignFieldConfig();
//            fieldConfig.setFreeMode(false);
//            fieldConfig.setSignFieldStyle(2);
//
//            CreateSignFlowByFileInput.SignFieldPosition position = new CreateSignFlowByFileInput.SignFieldPosition();
//            position.setPositionPage("1");
//            position.setPositionX(150.0);
//            position.setPositionY(300.0);
//            fieldConfig.setSignFieldPosition(position);
//
//            signField.setNormalSignFieldConfig(fieldConfig);
//            signFields.add(signField);
//            signer.setSignFields(signFields);
//
//            signers.add(signer);
//            input.setSigners(signers);
//
//            // 调用接口
//            CreateSignFlowByFileOutput result = eSignService.createSignFlowByFile(input);
//
//            System.out.println("=== 带附件的基于文件发起签署测试结果 ===");
//            System.out.println("签署流程ID: " + result.getSignFlowId());
//            System.out.println("状态: " + result.getStatus());
//            System.out.println("创建时间: " + result.getCreateTime());
//            System.out.println("截止时间: " + result.getDeadline());
//
//        } catch (Exception e) {
//            System.err.println("带附件的基于文件发起签署测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//}