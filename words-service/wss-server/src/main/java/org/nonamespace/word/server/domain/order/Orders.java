package org.nonamespace.word.server.domain.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.nonamespace.word.server.domain.Product;
import org.nonamespace.word.server.entity.DataEntity;

import java.util.Date;
import java.util.Map;

/**
 * 订单表
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName(value = "orders", autoResultMap = true)
public class Orders extends DataEntity {

    /** "订单号 **/
    @TableField("no")
    private String no;

    /** "订单来源 **/
    @TableField("source")
    private String source;

    /** "订单标题 **/
    @TableField("body")
    private String body;

    /** "订单状态 **/
    @TableField("order_status")
    private String orderStatus;

    /** "订单总金额(分) **/
    @TableField("total_amt")
    private Long totalAmt;

    /** "销售员ID **/
    @TableField("saler_id")
    private String salerId;

    /** "学生ID **/
    @TableField("student_id")
    private String studentId;

    /** "原始订单信息 **/
    @TableField(value = "origin_order", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> originOrder;

    /** "备注 **/
    @TableField("remark")
    private String remark;

    /** "已支付金额(分) **/
    @TableField("amt_paid")
    private Long amtPaid;

    /** "未支付金额(分) **/
    @TableField("amt_unpaid")
    private Long amtUnpaid;

    /** "最后支付时间 **/
    @TableField("last_pay_time")
    private Date lastPayTime;

    /**
     * 合同签署状态：无需签署，未签署，已签署
     */
    private String signStatus;
    /**
     * 交易方式：全额支付，分期支付
     */
    private String trxMethod;

    @TableField(value = "products", typeHandler = JacksonTypeHandler.class)
    private Product products;
    private String productId;
}
