package org.nonamespace.word.server.service.order;

import org.nonamespace.word.thirdpart.allinpay.dto.AllinPayNotifyDto;

/**
 * 通联支付回调通知服务接口
 * <AUTHOR>
 */
public interface IAllinPayNotifyService {
    
    /**
     * 处理支付通知业务逻辑
     * 该方法保证幂等性，同一笔交易的重复通知不会重复处理
     * 
     * @param notifyDto 通知DTO对象
     * @return 处理结果
     */
    boolean processPaymentNotify(AllinPayNotifyDto notifyDto);


    boolean processRefundNotify(AllinPayNotifyDto notifyDto);
}
