package org.nonamespace.word.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.management.booking.CourseBookingDto;
import org.nonamespace.word.server.dto.management.teachingleader.TeachingGroupLeaderDto;
import org.nonamespace.word.server.facade.CurriculumFacade;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 教学组长服务实现
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeachingGroupLeaderServiceImpl implements ITeachingGroupLeaderService {

    private final ITeachingGroupService teachingGroupService;
    private final ITeachingGroupMemberService teachingGroupMemberService;
    private final ICourseBookingApplicationService courseBookingApplicationService;
    private final ICourseBookingApplicationReviewService courseBookingApplicationReviewService;
    private final ICourseBookingAutoProcessService courseBookingAutoProcessService;
    private final UserStudentExtService userStudentExtService;
    private final ISaleProfileService saleProfileService;
    private final ISalesGroupService salesGroupService;
    private final ITeacherProfileService teacherProfileService;
    private final SystemDataQueryUtil systemDataQueryUtil;
    private final CurriculumFacade curriculumFacade;
    private final IWechatNotificationService wechatNotificationService;
    private final ITeacherTimeSlotService teacherTimeSlotService;

    @Override
    public String getTeachingGroupIdByLeader(String leaderId) {
        try {
            log.info("根据组长ID获取教学组: leaderId={}", leaderId);

            // 首先查询用户是否是教学组长
            TeachingGroup group = teachingGroupService.lambdaQuery()
                    .eq(TeachingGroup::getLeaderId, leaderId)
                    .eq(TeachingGroup::getDeleted, false)
                    .one();

            if (group != null) {
                log.info("找到教学组(组长): groupId={}, groupName={}", group.getId(), group.getName());
                return group.getId();
            }

            // 如果不是组长，检查是否是教学组成员且有教学组长角色
            if (systemDataQueryUtil.isTeacherGroupManager()) {
                log.info("用户有教学组长角色，查询所属教学组");

                List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                        .eq(TeachingGroupMember::getTeacherId, leaderId)
                        .eq(TeachingGroupMember::getDeleted, false)
                        .eq(TeachingGroupMember::getStatus, "active")
                        .list();

                if (!members.isEmpty()) {
                    // 返回第一个教学组（通常用户只在一个教学组中）
                    String groupId = members.getFirst().getGroupId();
                    TeachingGroup memberGroup = teachingGroupService.getById(groupId);
                    if (memberGroup != null && !memberGroup.getDeleted()) {
                        log.info("找到教学组(成员): groupId={}, groupName={}", memberGroup.getId(), memberGroup.getName());
                        return memberGroup.getId();
                    }
                }
            }

            log.warn("未找到教学组: leaderId={}", leaderId);
            return null;

        } catch (Exception e) {
            log.error("根据组长ID获取教学组失败", e);
            return null;
        }
    }

    @Override
    public IPage<CourseBookingDto.BasicResp> getPendingApplicationsForGroup(String teachingGroupId, TeachingGroupLeaderDto.GetPendingApplicationsReq req) {
        try {
            log.info("获取教学组待审核申请: groupId={}, pageNum={}, pageSize={}", teachingGroupId, req.getPageNum(), req.getPageSize());

            // 1. 先获取该教学组的所有老师ID
            List<String> teacherIds = teachingGroupMemberService.lambdaQuery()
                    .eq(TeachingGroupMember::getGroupId, teachingGroupId)
                    .eq(TeachingGroupMember::getDeleted, false)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .list()
                    .stream()
                    .map(TeachingGroupMember::getTeacherId)
                    .toList();

            if (teacherIds.isEmpty()) {
                log.warn("教学组没有活跃的老师: groupId={}", teachingGroupId);
                return new Page<>(req.getPageNum(), req.getPageSize(), 0);
            }

            log.info("教学组活跃老师数量: groupId={}, teacherCount={}", teachingGroupId, teacherIds.size());

            // 2. 查询申请记录，条件是候选老师中包含该教学组的老师
            Page<CourseBookingApplication> page = new Page<>(req.getPageNum(), req.getPageSize());

            // 构建查询条件：申请的候选老师中包含该教学组的老师
            var wrapper = courseBookingApplicationService.lambdaQuery()
                    .eq(CourseBookingApplication::getDeleted, false)
                    .orderByDesc(CourseBookingApplication::getCreateTime);

            // 处理状态查询 - 需要根据教学组的个性化状态进行过滤
            applyGroupSpecificStatusFilter(wrapper, req, teachingGroupId);

            // 添加其他查询条件
            if (StrUtil.isNotEmpty(req.getSubject())) {
                wrapper.eq(CourseBookingApplication::getSubject, req.getSubject());
            }
            if (StrUtil.isNotEmpty(req.getSpecification())) {
                wrapper.eq(CourseBookingApplication::getSpecification, req.getSpecification());
            }

            // 添加申请时间范围过滤
            if (StrUtil.isNotEmpty(req.getCreateTimeStart())) {
                wrapper.ge(CourseBookingApplication::getCreateTime, req.getCreateTimeStart());
            }
            if (StrUtil.isNotEmpty(req.getCreateTimeEnd())) {
                wrapper.le(CourseBookingApplication::getCreateTime, req.getCreateTimeEnd());
            }

            // 应用学生信息过滤
            applyStudentFilter(wrapper, req);

            // 应用老师信息过滤（但不重复添加数组过滤条件）
            applyTeacherFilterForSearch(wrapper, req, teacherIds);

            // 添加候选老师过滤条件 - 使用PostgreSQL数组操作符，确保类型匹配
            String teacherIdsStr = teacherIds.stream()
                    .map(id -> "'" + id + "'::varchar")
                    .collect(Collectors.joining(","));

            wrapper.apply("preferred_teachers && ARRAY[" + teacherIdsStr + "]::varchar[]");

            // 分页查询
            IPage<CourseBookingApplication> applicationPage = courseBookingApplicationService.page(page, wrapper.getWrapper());

            // 转换为DTO - 使用教学组特定的转换方法
            IPage<CourseBookingDto.BasicResp> result = applicationPage.convert(app -> convertToBasicRespWithGroupStatus(app, teachingGroupId));

            log.info("获取教学组待审核申请成功: groupId={}, total={}", teachingGroupId, result.getTotal());
            return result;

        } catch (Exception e) {
            log.error("获取教学组待审核申请失败: groupId={}", teachingGroupId, e);
            throw new RuntimeException("获取待审核申请失败: " + e.getMessage(), e);
        }
    }

    @Override
    public IPage<CourseBookingDto.BasicResp> getAllPendingApplications(TeachingGroupLeaderDto.GetPendingApplicationsReq req) {
        try {
            log.info("获取所有待审核申请列表: pageNum={}, pageSize={}", req.getPageNum(), req.getPageSize());

            // 构建分页对象
            Page<CourseBookingApplication> page = new Page<>(req.getPageNum(), req.getPageSize());

            // 构建查询条件 - 查询所有申请（支持状态过滤）
            var queryWrapper = courseBookingApplicationService.lambdaQuery()
                    .eq(CourseBookingApplication::getDeleted, false)
                    .orderByDesc(CourseBookingApplication::getCreateTime);

            // 处理状态查询（支持多选，默认查询待审核状态）
            if (req.getStatusList() != null && !req.getStatusList().isEmpty()) {
                queryWrapper.in(CourseBookingApplication::getStatus, req.getStatusList());
            } else if (StrUtil.isNotEmpty(req.getStatus())) {
                // 兼容单个状态查询
                queryWrapper.eq(CourseBookingApplication::getStatus, req.getStatus());
            } else {
                // 默认查询待审核状态
                queryWrapper.eq(CourseBookingApplication::getStatus, CourseBookingApplication.Status.PENDING.getCode());
            }

            // 添加其他查询条件
            if (StrUtil.isNotEmpty(req.getSubject())) {
                queryWrapper.eq(CourseBookingApplication::getSubject, req.getSubject());
            }
            if (StrUtil.isNotEmpty(req.getSpecification())) {
                queryWrapper.eq(CourseBookingApplication::getSpecification, req.getSpecification());
            }

            // 添加销售相关过滤条件
            if (StrUtil.isNotEmpty(req.getSalesGroupId())) {
                queryWrapper.eq(CourseBookingApplication::getSalesGroupId, req.getSalesGroupId());
            }
            if (StrUtil.isNotEmpty(req.getSalesId())) {
                queryWrapper.eq(CourseBookingApplication::getSalesId, req.getSalesId());
            }

            // 添加申请时间范围过滤
            if (StrUtil.isNotEmpty(req.getCreateTimeStart())) {
                queryWrapper.ge(CourseBookingApplication::getCreateTime, req.getCreateTimeStart());
            }
            if (StrUtil.isNotEmpty(req.getCreateTimeEnd())) {
                queryWrapper.le(CourseBookingApplication::getCreateTime, req.getCreateTimeEnd());
            }

            // 应用学生信息过滤
            applyStudentFilter(queryWrapper, req);

            // 应用老师信息过滤
            applyTeacherFilterForSearch(queryWrapper, req, new ArrayList<>());

            // 执行查询 - 修复：使用正确的分页查询方式
            IPage<CourseBookingApplication> applicationPage = courseBookingApplicationService.page(page, queryWrapper.getWrapper());

            // 转换为响应对象
            IPage<CourseBookingDto.BasicResp> result = convertToBasicRespPage(applicationPage);

            log.info("获取所有待审核申请成功: total={}", result.getTotal());
            return result;

        } catch (Exception e) {
            log.error("获取所有待审核申请失败", e);
            throw new RuntimeException("获取所有待审核申请失败: " + e.getMessage());
        }
    }

    @Override
    public IPage<CourseBookingDto.BasicResp> getApplicationsWithRoleConstraints(String currentUserId, TeachingGroupLeaderDto.GetPendingApplicationsReq req) {
        try {
            log.info("根据用户角色获取预约课申请列表: userId={}, pageNum={}, pageSize={}", currentUserId, req.getPageNum(), req.getPageSize());

            // 构建分页对象
            Page<CourseBookingApplication> page = new Page<>(req.getPageNum(), req.getPageSize());

            // 构建基础查询条件
            var queryWrapper = courseBookingApplicationService.lambdaQuery()
                    .eq(CourseBookingApplication::getDeleted, false)
                    .orderByDesc(CourseBookingApplication::getCreateTime);

            // 处理状态查询（支持多选，默认查询待审核状态）
            // 应用基于教学组视角的状态过滤
            applyTeachingGroupStatusFilter(queryWrapper, req, currentUserId);

            // 添加基础查询条件
            if (StrUtil.isNotEmpty(req.getSubject())) {
                queryWrapper.eq(CourseBookingApplication::getSubject, req.getSubject());
            }
            if (StrUtil.isNotEmpty(req.getSpecification())) {
                queryWrapper.eq(CourseBookingApplication::getSpecification, req.getSpecification());
            }

            // 添加销售相关过滤条件
            if (StrUtil.isNotEmpty(req.getSalesGroupId())) {
                queryWrapper.eq(CourseBookingApplication::getSalesGroupId, req.getSalesGroupId());
            }
            if (StrUtil.isNotEmpty(req.getSalesId())) {
                queryWrapper.eq(CourseBookingApplication::getSalesId, req.getSalesId());
            }

            // 添加申请时间范围过滤
            if (StrUtil.isNotEmpty(req.getCreateTimeStart())) {
                queryWrapper.ge(CourseBookingApplication::getCreateTime, req.getCreateTimeStart());
            }
            if (StrUtil.isNotEmpty(req.getCreateTimeEnd())) {
                queryWrapper.le(CourseBookingApplication::getCreateTime, req.getCreateTimeEnd());
            }

            // 应用学生信息过滤
            applyStudentFilter(queryWrapper, req);

            // 应用老师信息过滤
            applyTeacherFilterForSearch(queryWrapper, req, new ArrayList<>());

            // 根据用户角色添加数据访问约束
            applyRoleBasedConstraints(queryWrapper, currentUserId);

            // 执行查询
            IPage<CourseBookingApplication> applicationPage = courseBookingApplicationService.page(page, queryWrapper.getWrapper());

            // 转换为响应对象
            IPage<CourseBookingDto.BasicResp> result = convertToBasicRespPage(applicationPage);

            log.info("根据用户角色获取预约课申请列表成功: total={}", result.getTotal());
            return result;

        } catch (Exception e) {
            log.error("根据用户角色获取预约课申请列表失败", e);
            throw new RuntimeException("获取预约课申请列表失败: " + e.getMessage());
        }
    }

    @Override
    public boolean hasReviewPermission(String userId, String applicationId) {
        try {
            log.info("检查审核权限: userId={}, applicationId={}", userId, applicationId);

            // 获取申请信息
            CourseBookingApplication application = courseBookingApplicationService.getById(applicationId);
            if (application == null || application.getDeleted()) {
                log.warn("申请不存在: applicationId={}", applicationId);
                return false;
            }

            // 获取用户的教学组
            String userGroupId = getTeachingGroupIdByLeader(userId);
            if (userGroupId == null) {
                log.warn("用户不是教学组长: userId={}", userId);
                return false;
            }

            // 获取申请的候选老师列表
            List<String> preferredTeachers = application.getPreferredTeachers();
            if (preferredTeachers == null || preferredTeachers.isEmpty()) {
                log.warn("申请没有候选老师: applicationId={}", applicationId);
                return false;
            }

            // 获取该教学组的所有老师ID
            List<String> groupTeacherIds = teachingGroupMemberService.lambdaQuery()
                    .eq(TeachingGroupMember::getGroupId, userGroupId)
                    .eq(TeachingGroupMember::getDeleted, false)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .list()
                    .stream()
                    .map(TeachingGroupMember::getTeacherId)
                    .collect(Collectors.toList());

            // 检查申请的候选老师中是否有该教学组的老师
            boolean hasPermission = preferredTeachers.stream()
                    .anyMatch(groupTeacherIds::contains);

            if (!hasPermission) {
                log.info("教学组没有相关候选老师: userGroupId={}, preferredTeachers={}, groupTeacherIds={}",
                        userGroupId, preferredTeachers, groupTeacherIds);
                return false;
            }

            // 检查该教学组是否已经审核过此申请
            boolean hasReviewed = courseBookingApplicationReviewService.hasReviewed(applicationId, userGroupId);
            if (hasReviewed) {
                log.info("该教学组已经审核过此申请: userGroupId={}, applicationId={}", userGroupId, applicationId);
                return false;
            }

            log.info("权限检查通过: userGroupId={}, applicationId={}", userGroupId, applicationId);
            return true;

        } catch (Exception e) {
            log.error("检查审核权限失败", e);
            return false;
        }
    }

    @Override
    public boolean reviewApplication(TeachingGroupLeaderDto.ReviewApplicationReq req, String reviewerId) {
        log.info("审核申请: applicationId={}, result={}, reviewerId={}", req.getApplicationId(), req.getReviewResult(), reviewerId);

        // 获取申请
        CourseBookingApplication application = courseBookingApplicationService.getById(req.getApplicationId());
        if (application == null || application.getDeleted()) {
            throw new RuntimeException("申请不存在");
        }

        // 检查状态
        if (!CourseBookingApplication.Status.PENDING.getCode().equals(application.getStatus())) {
            throw new RuntimeException("申请已处理，无法重复审核");
        }

        // 获取当前审核人的教学组ID
        String currentTeachingGroupId = getTeachingGroupIdByLeader(reviewerId);
        if (currentTeachingGroupId == null) {
            throw new RuntimeException("当前用户不是教学组长");
        }

        // 检查是否已经审核过
        if (courseBookingApplicationReviewService.hasReviewed(req.getApplicationId(), currentTeachingGroupId)) {
            throw new RuntimeException("该教学组已经审核过此申请");
        }

        if (StrUtil.isEmpty(application.getNature())) {
            application.setNature("试听课");
        }

        if (StrUtil.isEmpty(application.getSpecification())) {
            application.setSpecification("单词课");
        }

        if (StrUtil.isEmpty(application.getSubject())) {
            application.setSubject("英语");
        }

        // 创建审核记录
        CourseBookingApplicationReview review = new CourseBookingApplicationReview();
        review.setApplicationId(req.getApplicationId());
        review.setTeachingGroupId(currentTeachingGroupId);
        review.setReviewerId(reviewerId);
        review.setReviewResult(req.getReviewResult());
        review.setReviewComment(req.getReviewComment());
        review.setRejectionReason(req.getRejectionReason());
        review.setReviewTime(WssContext.now());

        if ("已通过".equals(req.getReviewResult())) {
            return handleApprovalReview(req, application, review);
        } else if ("已拒绝".equals(req.getReviewResult())) {
            return handleRejectionReview(req, application, review);
        } else {
            throw new RuntimeException("不支持的审核结果: " + req.getReviewResult());
        }
    }

    @Override
    public TeachingGroupLeaderDto.BatchReviewResp batchReviewApplications(TeachingGroupLeaderDto.BatchReviewReq req, String reviewerId) {
        try {
            log.info("批量审核申请: count={}, reviewerId={}", req.getApplicationIds().size(), reviewerId);

            TeachingGroupLeaderDto.BatchReviewResp result = new TeachingGroupLeaderDto.BatchReviewResp();
            result.setSuccessCount(0);
            result.setFailedCount(0);
            result.setFailedApplicationIds(new ArrayList<>());

            for (String applicationId : req.getApplicationIds()) {
                try {
                    // 构建单个审核请求
                    TeachingGroupLeaderDto.ReviewApplicationReq reviewReq = new TeachingGroupLeaderDto.ReviewApplicationReq();
                    reviewReq.setApplicationId(applicationId);
                    reviewReq.setReviewResult(req.getReviewResult());
                    reviewReq.setReviewComment(req.getReviewComment());
                    reviewReq.setRejectionReason(req.getRejectionReason());

                    // 执行审核
                    boolean success = reviewApplication(reviewReq, reviewerId);

                    if (success) {
                        result.setSuccessCount(result.getSuccessCount() + 1);
                    } else {
                        result.setFailedCount(result.getFailedCount() + 1);
                        result.getFailedApplicationIds().add(applicationId);
                    }

                } catch (Exception e) {
                    log.error("批量审核单个申请失败: applicationId={}", applicationId, e);
                    result.setFailedCount(result.getFailedCount() + 1);
                    result.getFailedApplicationIds().add(applicationId);
                }
            }

            log.info("批量审核完成: success={}, failed={}", result.getSuccessCount(), result.getFailedCount());
            return result;

        } catch (Exception e) {
            log.error("批量审核申请失败", e);
            throw new RuntimeException("批量审核失败: " + e.getMessage());
        }
    }

    @Override
    public TeachingGroupLeaderDto.ReviewStatsResp getReviewStatsForGroup(String teachingGroupId) {
        return new TeachingGroupLeaderDto.ReviewStatsResp();
//        try {
//            log.info("获取教学组审核统计: groupId={}", teachingGroupId);
//
//            TeachingGroupLeaderDto.ReviewStatsResp stats = new TeachingGroupLeaderDto.ReviewStatsResp();
//
//            // 待审核数量
//            long pendingCount = courseBookingApplicationService.lambdaQuery()
//                    .eq(CourseBookingApplication::getTeachingGroupId, teachingGroupId)
//                    .eq(CourseBookingApplication::getStatus, CourseBookingApplication.Status.PENDING.getCode())
//                    .eq(CourseBookingApplication::getDeleted, false)
//                    .count();
//            stats.setPendingCount((int) pendingCount);
//
//            // 今日已审核数量
//            long todayReviewedCount = courseBookingApplicationService.lambdaQuery()
//                    .eq(CourseBookingApplication::getTeachingGroupId, teachingGroupId)
//                    .ne(CourseBookingApplication::getStatus, CourseBookingApplication.Status.PENDING.getCode())
//                    .eq(CourseBookingApplication::getDeleted, false)
//                    .ge(CourseBookingApplication::getApprovalTime, WssContext.now()) // 修复：使用正确的字段名
//                    .count();
//            stats.setTodayReviewedCount((int) todayReviewedCount);
//
//            // 通过率（简化计算）
//            long approvedCount = courseBookingApplicationService.lambdaQuery()
//                    .eq(CourseBookingApplication::getTeachingGroupId, teachingGroupId)
//                    .eq(CourseBookingApplication::getStatus, CourseBookingApplication.Status.APPROVED.getCode())
//                    .eq(CourseBookingApplication::getDeleted, false)
//                    .count();
//
//            long totalReviewed = courseBookingApplicationService.lambdaQuery()
//                    .eq(CourseBookingApplication::getTeachingGroupId, teachingGroupId)
//                    .ne(CourseBookingApplication::getStatus, CourseBookingApplication.Status.PENDING.getCode())
//                    .eq(CourseBookingApplication::getDeleted, false)
//                    .count();
//
//            if (totalReviewed > 0) {
//                stats.setApprovalRate((double) approvedCount / totalReviewed * 100);
//            } else {
//                stats.setApprovalRate(0.0);
//            }
//
//            // 超时未审核数量（简化处理）
//            stats.setOverdueCount(0);
//
//            log.info("获取教学组审核统计成功: pending={}, todayReviewed={}, approvalRate={}",
//                    stats.getPendingCount(), stats.getTodayReviewedCount(), stats.getApprovalRate());
//
//            return stats;
//
//        } catch (Exception e) {
//            log.error("获取教学组审核统计失败", e);
//            throw new RuntimeException("获取审核统计失败: " + e.getMessage());
//        }
    }

    @Override
    public TeachingGroupLeaderDto.ReviewStatsResp getAllReviewStats() {
        try {
            log.info("获取所有审核统计信息");

            TeachingGroupLeaderDto.ReviewStatsResp stats = new TeachingGroupLeaderDto.ReviewStatsResp();

            // 待审核数量（所有教学组）
            long pendingCount = courseBookingApplicationService.lambdaQuery()
                    .eq(CourseBookingApplication::getStatus, CourseBookingApplication.Status.PENDING.getCode())
                    .eq(CourseBookingApplication::getDeleted, false)
                    .count();
            stats.setPendingCount((int) pendingCount);

            // 今日已审核数量（所有教学组）
            long todayReviewedCount = courseBookingApplicationService.lambdaQuery()
                    .ne(CourseBookingApplication::getStatus, CourseBookingApplication.Status.PENDING.getCode())
                    .eq(CourseBookingApplication::getDeleted, false)
                    .ge(CourseBookingApplication::getApprovalTime, WssContext.now()) // 修复：使用正确的字段名
                    .count();
            stats.setTodayReviewedCount((int) todayReviewedCount);

            // 通过率（所有教学组）
            long approvedCount = courseBookingApplicationService.lambdaQuery()
                    .eq(CourseBookingApplication::getStatus, CourseBookingApplication.Status.APPROVED.getCode())
                    .eq(CourseBookingApplication::getDeleted, false)
                    .count();

            long totalReviewed = courseBookingApplicationService.lambdaQuery()
                    .ne(CourseBookingApplication::getStatus, CourseBookingApplication.Status.PENDING.getCode())
                    .eq(CourseBookingApplication::getDeleted, false)
                    .count();

            if (totalReviewed > 0) {
                stats.setApprovalRate((double) approvedCount / totalReviewed * 100);
            } else {
                stats.setApprovalRate(0.0);
            }

            // 超时未审核数量（简化处理）
            stats.setOverdueCount(0);

            log.info("获取所有审核统计成功: pending={}, todayReviewed={}, approvalRate={}",
                    stats.getPendingCount(), stats.getTodayReviewedCount(), stats.getApprovalRate());

            return stats;

        } catch (Exception e) {
            log.error("获取所有审核统计失败", e);
            throw new RuntimeException("获取所有审核统计失败: " + e.getMessage());
        }
    }

    // 其他方法的简化实现
    @Override
    public List<TeachingGroupLeaderDto.GroupTeacherResp> getGroupTeachers(String groupId) {
        try {
            log.info("获取教学组教师列表: groupId={}", groupId);

            // 1. 获取教学组成员
            List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                    .eq(TeachingGroupMember::getGroupId, groupId)
                    .eq(TeachingGroupMember::getDeleted, false)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .list();

            if (members.isEmpty()) {
                log.warn("教学组没有活跃成员: groupId={}", groupId);
                return new ArrayList<>();
            }

            // 2. 获取教师ID列表
            List<String> teacherIds = members.stream()
                    .map(TeachingGroupMember::getTeacherId)
                    .collect(Collectors.toList());

            // 3. 查询教师详细信息
            List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                    .in(TeacherProfile::getTeacherId, teacherIds)
                    .eq(TeacherProfile::getDeleted, false)
//                    .eq(TeacherProfile::getStatus, "active")
                    .list();

            // 4. 转换为DTO
            List<TeachingGroupLeaderDto.GroupTeacherResp> result = teachers.stream()
                    .map(teacher -> {
                        TeachingGroupLeaderDto.GroupTeacherResp resp = new TeachingGroupLeaderDto.GroupTeacherResp();
                        resp.setTeacherId(teacher.getTeacherId());
                        resp.setTeacherName(teacher.getRealName());
                        resp.setTeacherPhone(teacher.getPhonenumber());
                        resp.setStatus(teacher.getStatus());

                        // 设置默认值
                        resp.setTeachingSubjects(new ArrayList<>());
                        resp.setTeachingSpecifications(new ArrayList<>());
                        resp.setCurrentStudentCount(0);
                        resp.setMaxStudentCount(100);
                        resp.setIsAvailable(true);
                        resp.setUnavailableReason("");

                        return resp;
                    })
                    .collect(Collectors.toList());

            log.info("获取教学组教师列表成功: groupId={}, teacherCount={}", groupId, result.size());
            return result;

        } catch (Exception e) {
            log.error("获取教学组教师列表失败: groupId={}", groupId, e);
            throw new RuntimeException("获取教学组教师列表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TeachingGroupLeaderDto.GroupTeacherResp> getAppliedTeachers(String applicationId, String groupId) {
        try {
            log.info("获取指定申请中本组内的候选教师列表: applicationId={}, groupId={}", applicationId, groupId);

            // 1. 查询指定的申请记录
            CourseBookingApplication application = courseBookingApplicationService.lambdaQuery()
                    .eq(CourseBookingApplication::getId, applicationId)
                    .eq(CourseBookingApplication::getDeleted, false)
                    .one();

            if (application == null) {
                log.warn("申请记录不存在: applicationId={}", applicationId);
                return new ArrayList<>();
            }

            // 2. 获取申请中的候选老师ID列表
            List<String> candidateTeacherIds = application.getPreferredTeachers();
            if (candidateTeacherIds == null || candidateTeacherIds.isEmpty()) {
                log.warn("申请记录中没有候选老师: applicationId={}", applicationId);
                return new ArrayList<>();
            }

            // 3. 获取教学组成员ID列表
            List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                    .eq(TeachingGroupMember::getGroupId, groupId)
                    .eq(TeachingGroupMember::getDeleted, false)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .list();

            if (members.isEmpty()) {
                log.warn("教学组没有活跃成员: groupId={}", groupId);
                return new ArrayList<>();
            }

            List<String> groupTeacherIds = members.stream()
                    .map(TeachingGroupMember::getTeacherId)
                    .collect(Collectors.toList());

            // 4. 筛选出既是候选老师又是本组成员的老师ID
            List<String> targetTeacherIds = candidateTeacherIds.stream()
                    .filter(teacherId -> StrUtil.isNotEmpty(teacherId) && groupTeacherIds.contains(teacherId))
                    .collect(Collectors.toList());

            if (targetTeacherIds.isEmpty()) {
                log.info("该申请中没有本组内的候选老师: applicationId={}, groupId={}", applicationId, groupId);
                return new ArrayList<>();
            }

            // 5. 查询目标教师的详细信息
            List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                    .in(TeacherProfile::getTeacherId, targetTeacherIds)
                    .eq(TeacherProfile::getDeleted, false)
//                    .eq(TeacherProfile::getStatus, "active")
                    .list();

            // 6. 转换为DTO
            List<TeachingGroupLeaderDto.GroupTeacherResp> result = teachers.stream()
                    .map(teacher -> {
                        TeachingGroupLeaderDto.GroupTeacherResp resp = new TeachingGroupLeaderDto.GroupTeacherResp();
                        resp.setTeacherId(teacher.getTeacherId());
                        resp.setTeacherName(teacher.getRealName());
                        resp.setTeacherPhone(teacher.getPhonenumber());
                        resp.setStatus(teacher.getStatus());

                        // 设置默认值
                        resp.setTeachingSubjects(new ArrayList<>());
                        resp.setTeachingSpecifications(new ArrayList<>());
                        resp.setCurrentStudentCount(0);
                        resp.setMaxStudentCount(100);
                        resp.setIsAvailable(true);
                        resp.setUnavailableReason("");

                        return resp;
                    })
                    .collect(Collectors.toList());

            log.info("获取指定申请中本组内候选教师列表成功: applicationId={}, groupId={}, teacherCount={}",
                    applicationId, groupId, result.size());
            return result;

        } catch (Exception e) {
            log.error("获取指定申请中本组内候选教师列表失败: applicationId={}, groupId={}", applicationId, groupId, e);
            throw new RuntimeException("获取指定申请中本组内候选教师列表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TeachingGroupLeaderDto.TrialTimeSlotResp> getTeacherAvailableSlots(String teacherId, String applicationId) {
        try {
            log.info("获取教师在指定申请试听课时间的可选时间段: teacherId={}, applicationId={}", teacherId, applicationId);

            // 1. 查询申请记录，获取试听课时间
            CourseBookingApplication application = courseBookingApplicationService.lambdaQuery()
                    .eq(CourseBookingApplication::getId, applicationId)
                    .eq(CourseBookingApplication::getDeleted, false)
                    .one();

            if (application == null) {
                log.warn("申请记录不存在: applicationId={}", applicationId);
                return new ArrayList<>();
            }

            if (application.getTrialClassDate() == null ||
                application.getTrialClassStartTime() == null ||
                application.getTrialClassEndTime() == null) {
                log.warn("申请记录中没有完整的试听课时间: applicationId={}", applicationId);
                return new ArrayList<>();
            }

            // 2. 获取试听课时间信息
            Date trialDate = application.getTrialClassDate();
            String trialStartTime = application.getTrialClassStartTime().toString();
            String trialEndTime = application.getTrialClassEndTime().toString();

            // 3. 生成试听课时间段选项（每5分钟一个时间段，每个时间段持续1小时）
            List<TeachingGroupLeaderDto.TrialTimeSlotResp> timeSlots = generateTrialTimeSlots(trialDate, trialStartTime, trialEndTime);

            // 4. 检查老师在这些时间段的可用性
            checkTeacherAvailability(teacherId, timeSlots);

            log.info("获取教师可选试听课时间段成功: teacherId={}, applicationId={}, availableCount={}",
                    teacherId, applicationId, timeSlots.stream().mapToInt(slot -> slot.getAvailable() ? 1 : 0).sum());
            return timeSlots;

        } catch (Exception e) {
            log.error("获取教师可选试听课时间段失败: teacherId={}, applicationId={}", teacherId, applicationId, e);
            throw new RuntimeException("获取教师可选试听课时间段失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成试听课时间段选项
     */
    private List<TeachingGroupLeaderDto.TrialTimeSlotResp> generateTrialTimeSlots(Date trialDate, String startTime, String endTime) {
        List<TeachingGroupLeaderDto.TrialTimeSlotResp> timeSlots = new ArrayList<>();

        try {
            // 解析时间
            LocalTime start = LocalTime.parse(startTime.length() == 5 ? startTime : startTime.substring(0, 5));
            LocalTime end = LocalTime.parse(endTime.length() == 5 ? endTime : endTime.substring(0, 5));

            // 格式化日期
            LocalDate date = trialDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            String dateStr = date.toString();

            // 每5分钟生成一个时间段，每个时间段持续1小时
            LocalTime current = start;
            while (current.plusHours(1).isBefore(end) || current.plusHours(1).equals(end)) {
                TeachingGroupLeaderDto.TrialTimeSlotResp slot = new TeachingGroupLeaderDto.TrialTimeSlotResp();
                slot.setDate(dateStr);
                slot.setStartTime(current.toString());
                slot.setEndTime(current.plusHours(1).toString());
                slot.setAvailable(true); // 初始状态为可用，后续检查老师可用性
                slot.setUnavailableReason("");

                timeSlots.add(slot);

                // 增加5分钟
                current = current.plusMinutes(5);
            }

        } catch (Exception e) {
            log.error("生成试听课时间段失败: startTime={}, endTime={}", startTime, endTime, e);
        }

        return timeSlots;
    }

    /**
     * 检查老师在指定时间段的可用性
     */
    private void checkTeacherAvailability(String teacherId, List<TeachingGroupLeaderDto.TrialTimeSlotResp> timeSlots) {
        try {
            // 获取老师的可用时间段
            List<TeacherTimeSlot> teacherTimeSlots = teacherTimeSlotService.lambdaQuery()
                    .eq(TeacherTimeSlot::getTeacherId, teacherId)
                    .eq(TeacherTimeSlot::getDeleted, false)
                    .eq(TeacherTimeSlot::getStatus, "available")
                    .list();

            for (TeachingGroupLeaderDto.TrialTimeSlotResp slot : timeSlots) {
                try {
                    // 解析试听课时间段的日期和时间
                    LocalDate slotDate = LocalDate.parse(slot.getDate());
                    LocalTime slotStart = LocalTime.parse(slot.getStartTime());
                    LocalTime slotEnd = LocalTime.parse(slot.getEndTime());

                    // 计算星期几 (1=周一, 7=周日)
                    int weekday = slotDate.getDayOfWeek().getValue();

                    // 检查是否有匹配的可用时间段
                    boolean isAvailable = teacherTimeSlots.stream().anyMatch(teacherSlot -> {
                        if (teacherSlot.getWeekday() != weekday) {
                            return false;
                        }

                        try {
                            // TeacherTimeSlot的startTime和endTime已经是LocalTime类型
                            LocalTime teacherStart = teacherSlot.getStartTime();
                            LocalTime teacherEnd = teacherSlot.getEndTime();

                            // 检查试听课时间段是否完全包含在老师的可用时间段内
                            return !slotStart.isBefore(teacherStart) && !slotEnd.isAfter(teacherEnd);
                        } catch (Exception e) {
                            log.warn("检查老师时间段可用性失败: teacherSlot={}", teacherSlot, e);
                            return false;
                        }
                    });

                    slot.setAvailable(isAvailable);
                    if (!isAvailable) {
                        slot.setUnavailableReason("老师在该时间段不可用");
                    }

                } catch (Exception e) {
                    log.warn("检查时间段可用性失败: slot={}", slot, e);
                    slot.setAvailable(false);
                    slot.setUnavailableReason("时间段解析失败");
                }
            }

        } catch (Exception e) {
            log.error("检查老师可用性失败: teacherId={}", teacherId, e);
            // 如果检查失败，将所有时间段标记为不可用
            timeSlots.forEach(slot -> {
                slot.setAvailable(false);
                slot.setUnavailableReason("检查可用性失败");
            });
        }
    }

    @Override
    public boolean assignTeacherToApplication(TeachingGroupLeaderDto.AssignTeacherReq req, String assignerId) {
        // TODO: 实现分配教师到申请
        return true;
    }

    @Override
    public IPage<TeachingGroupLeaderDto.ReviewHistoryResp> getReviewHistory(String teachingGroupId, TeachingGroupLeaderDto.GetReviewHistoryReq req) {
        // TODO: 实现获取审核历史
        return new Page<>();
    }

    @Override
    public String exportReviewReport(String teachingGroupId, TeachingGroupLeaderDto.ExportReviewReportReq req) {
        // TODO: 实现导出审核报告
        return "";
    }

    @Override
    public TeachingGroupLeaderDto.ReviewRulesResp getReviewRules(String teachingGroupId) {
        // TODO: 实现获取审核规则
        return new TeachingGroupLeaderDto.ReviewRulesResp();
    }

    @Override
    public boolean setReviewRules(String teachingGroupId, TeachingGroupLeaderDto.SetReviewRulesReq req, String operatorId) {
        // TODO: 实现设置审核规则
        return true;
    }

    @Override
    public TeachingGroupLeaderDto.TeachingGroupInfoResp getTeachingGroupInfoByLeader(String leaderId) {
        try {
            log.info("根据组长ID获取教学组信息: leaderId={}", leaderId);

            // 获取教学组ID
            String teachingGroupId = getTeachingGroupIdByLeader(leaderId);
            if (teachingGroupId == null) {
                throw new RuntimeException("当前用户不是教学组长");
            }

            // TODO: 实现获取教学组详细信息
            TeachingGroupLeaderDto.TeachingGroupInfoResp result = new TeachingGroupLeaderDto.TeachingGroupInfoResp();
            result.setGroupId(teachingGroupId);
            result.setGroupName("教学组"); // 简化实现

            log.info("获取教学组信息成功: groupId={}", teachingGroupId);
            return result;

        } catch (Exception e) {
            log.error("获取教学组信息失败", e);
            throw new RuntimeException("获取教学组信息失败: " + e.getMessage());
        }
    }

    /**
     * 转换为基础响应DTO
     */
    private CourseBookingDto.BasicResp convertToBasicResp(CourseBookingApplication application) {
        CourseBookingDto.BasicResp resp = new CourseBookingDto.BasicResp();

        // 基础信息
        resp.setId(application.getId());
        resp.setStudentId(application.getStudentId());
        resp.setSubject(application.getSubject());
        resp.setSpecification(application.getSpecification());
        resp.setStatus(application.getStatus());
        resp.setApplicationReason(application.getApplicationReason());
        resp.setCreateTime(application.getCreateTime());
        resp.setUpdateTime(application.getUpdateTime());
//        resp.setTeachingGroupId(application.getTeachingGroupId());

        // 设置销售信息（直接从申请对象获取）
        resp.setSalesId(application.getSalesId());
        resp.setSalesGroupId(application.getSalesGroupId());

        // 设置状态文本
        resp.setStatusText(getStatusText(application.getStatus()));

        // 查询学生信息
        if (StrUtil.isNotEmpty(application.getStudentId())) {
            UserStudentExt student = userStudentExtService.lambdaQuery()
                    .eq(UserStudentExt::getStudentId, application.getStudentId())
                    .eq(UserStudentExt::getDeleted, false)
                    .one();
            if (student != null) {
                resp.setStudentName(student.getName());
                resp.setStudentPhone(student.getPhone());
            }
        }

        // 查询销售信息（使用申请对象中的销售ID）
        if (StrUtil.isNotEmpty(application.getSalesId())) {
            try {
                SaleProfile sales = saleProfileService.lambdaQuery()
                        .eq(SaleProfile::getSalesId, application.getSalesId())
                        .eq(SaleProfile::getDeleted, false)
                        .one();
                if (sales != null) {
                    resp.setSalesName(sales.getSalesName());
                    resp.setSalesGroupName(sales.getSalesGroupName());
                }
            } catch (Exception e) {
                log.warn("查询销售信息失败: salesId={}", application.getSalesId(), e);
            }
        }

        // 查询销售组信息（如果申请中有销售组ID）
        if (StrUtil.isNotEmpty(application.getSalesGroupId())) {
            try {
                SalesGroup salesGroup = salesGroupService.getById(application.getSalesGroupId());
                if (salesGroup != null && !salesGroup.getDeleted()) {
                    resp.setSalesGroupName(salesGroup.getName());
                }
            } catch (Exception e) {
                log.warn("查询销售组信息失败: salesGroupId={}", application.getSalesGroupId(), e);
            }
        }

        // 处理首选教师信息
        if (CollUtil.isNotEmpty(application.getPreferredTeachers())) {
            List<CourseBookingDto.PreferredTeacherInfo> teacherInfos = parsePreferredTeachers(application.getPreferredTeachers());
            resp.setPreferredTeacherInfos(teacherInfos);
        }

        // 处理首选时间段
        if (CollUtil.isNotEmpty(application.getPreferredTimeSlots())) {
            List<CourseBookingDto.PreferredTimeSlot> timeSlots = parsePreferredTimeSlots(application.getPreferredTimeSlots());
            resp.setPreferredTimeSlots(timeSlots);
        }

        // 处理审核信息
        resp.setApprovedTeacherId(application.getApprovedTeacherId());
        resp.setApprovalTime(application.getApprovalTime());
        resp.setApprovalBy(application.getApprovalBy());
        resp.setRejectionReason(application.getRejectionReason());
        resp.setCourseHoursPackageId(application.getCourseHoursPackageId());

        // 查询审核教师信息
        if (StrUtil.isNotEmpty(application.getApprovedTeacherId())) {
            TeacherProfile approvedTeacher = teacherProfileService.lambdaQuery()
                    .eq(TeacherProfile::getTeacherId, application.getApprovedTeacherId())
                    .eq(TeacherProfile::getDeleted, false)
                    .one();
            if (approvedTeacher != null) {
                resp.setApprovedTeacherName(approvedTeacher.getRealName());
            }
        }

        // 查询审核人信息
        if (StrUtil.isNotEmpty(application.getApprovalBy())) {
            TeacherProfile approvalBy = teacherProfileService.lambdaQuery()
                    .eq(TeacherProfile::getTeacherId, application.getApprovalBy())
                    .eq(TeacherProfile::getDeleted, false)
                    .one();
            if (approvalBy != null) {
                resp.setApprovalByName(approvalBy.getRealName());
            }
        }

        // 设置审核权限：只有待审核状态且用户有教学组长权限才能审核
        resp.setCanReview(
            CourseBookingApplication.Status.PENDING.getCode().equals(application.getStatus())
            && systemDataQueryUtil.isTeacherGroupManager()
        );

        return resp;
    }

    /**
     * 根据用户角色添加数据访问约束
     */
    private void applyRoleBasedConstraints(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper<CourseBookingApplication> queryWrapper, String currentUserId) {
        try {
            log.info("应用角色权限约束: userId={}", currentUserId);

            // Admin和HR：查看所有申请，无需添加约束
            if (systemDataQueryUtil.isAdminOrHr()) {
                log.info("Admin/HR用户，无需添加数据访问约束");
                return;
            }

            // 教学组长：查看自己组内被预约的老师的申请
            if (systemDataQueryUtil.isTeacherGroupManager()) {
                String teachingGroupId = getTeachingGroupIdByLeader(currentUserId);
                if (teachingGroupId != null) {
                    // 获取该教学组的所有老师ID
                    List<String> teacherIds = teachingGroupMemberService.lambdaQuery()
                            .eq(TeachingGroupMember::getGroupId, teachingGroupId)
                            .eq(TeachingGroupMember::getDeleted, false)
                            .eq(TeachingGroupMember::getStatus, "active")
                            .list()
                            .stream()
                            .map(TeachingGroupMember::getTeacherId)
                            .toList();

                    if (!teacherIds.isEmpty()) {
                        // 添加候选老师过滤条件 - 使用PostgreSQL数组操作符
                        String teacherIdsStr = teacherIds.stream()
                                .map(id -> "'" + id + "'::varchar")
                                .collect(Collectors.joining(","));
                        queryWrapper.apply("preferred_teachers && ARRAY[" + teacherIdsStr + "]::varchar[]");
                        log.info("教学组长权限约束: groupId={}, teacherCount={}", teachingGroupId, teacherIds.size());
                    } else {
                        // 如果教学组没有老师，则查询不到任何数据
                        queryWrapper.eq(CourseBookingApplication::getId, "IMPOSSIBLE_ID");
                        log.warn("教学组没有活跃老师: groupId={}", teachingGroupId);
                    }
                } else {
                    // 如果不是教学组长，则查询不到任何数据
                    queryWrapper.eq(CourseBookingApplication::getId, "IMPOSSIBLE_ID");
                    log.warn("用户不是教学组长: userId={}", currentUserId);
                }
                return;
            }

            // 销售相关角色：根据销售权限查看申请
            if (systemDataQueryUtil.isSalesRole()) {
                applySalesRoleConstraints(queryWrapper, currentUserId);
                return;
            }

            // 其他角色：无权限查看任何数据
            queryWrapper.eq(CourseBookingApplication::getId, "IMPOSSIBLE_ID");
            log.warn("用户无权限查看预约课申请: userId={}", currentUserId);

        } catch (Exception e) {
            log.error("应用角色权限约束失败: userId={}", currentUserId, e);
            // 出错时为安全起见，不返回任何数据
            queryWrapper.eq(CourseBookingApplication::getId, "IMPOSSIBLE_ID");
        }
    }

    /**
     * 应用销售角色权限约束
     */
    private void applySalesRoleConstraints(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper<CourseBookingApplication> queryWrapper, String currentUserId) {
        try {
            // 销售总监：查看所有申请
            if (systemDataQueryUtil.isSalesDirector()) {
                log.info("销售总监权限：查看所有申请");
                return; // 无需添加约束
            }

            // 销售组长：查看自己和组内成员提交的申请
            if (systemDataQueryUtil.isSalesGroupLeader()) {
                // 获取销售组内所有成员ID（包括自己）
                List<String> salesIds = getSalesGroupMemberIds(currentUserId);
                if (!salesIds.isEmpty()) {
                    // 通过学生表关联查询销售ID
                    List<String> studentIds = userStudentExtService.lambdaQuery()
                            .in(UserStudentExt::getSalesId, salesIds)
                            .eq(UserStudentExt::getDeleted, false)
                            .list()
                            .stream()
                            .map(UserStudentExt::getStudentId)
                            .toList();

                    if (!studentIds.isEmpty()) {
                        queryWrapper.in(CourseBookingApplication::getStudentId, studentIds);
                        log.info("销售组长权限约束: salesCount={}, studentCount={}", salesIds.size(), studentIds.size());
                    } else {
                        queryWrapper.eq(CourseBookingApplication::getId, "IMPOSSIBLE_ID");
                        log.warn("销售组长没有关联学生: userId={}", currentUserId);
                    }
                } else {
                    queryWrapper.eq(CourseBookingApplication::getId, "IMPOSSIBLE_ID");
                    log.warn("销售组长没有组员: userId={}", currentUserId);
                }
                return;
            }

            // 普通销售：查看自己提交的申请
            List<String> studentIds = userStudentExtService.lambdaQuery()
                    .eq(UserStudentExt::getSalesId, currentUserId)
                    .eq(UserStudentExt::getDeleted, false)
                    .list()
                    .stream()
                    .map(UserStudentExt::getStudentId)
                    .toList();

            if (!studentIds.isEmpty()) {
                queryWrapper.in(CourseBookingApplication::getStudentId, studentIds);
                log.info("销售权限约束: studentCount={}", studentIds.size());
            } else {
                queryWrapper.eq(CourseBookingApplication::getId, "IMPOSSIBLE_ID");
                log.warn("销售没有关联学生: userId={}", currentUserId);
            }

        } catch (Exception e) {
            log.error("应用销售角色权限约束失败: userId={}", currentUserId, e);
            queryWrapper.eq(CourseBookingApplication::getId, "IMPOSSIBLE_ID");
        }
    }

    /**
     * 获取销售组成员ID列表（包括自己）
     */
    private List<String> getSalesGroupMemberIds(String salesGroupLeaderId) {
        try {
            // 获取销售组长的销售组ID
            SaleProfile leaderProfile = saleProfileService.lambdaQuery()
                    .eq(SaleProfile::getSalesId, salesGroupLeaderId)
                    .eq(SaleProfile::getDeleted, false)
                    .one();

            if (leaderProfile == null || StrUtil.isEmpty(leaderProfile.getSalesGroupId())) {
                return List.of(salesGroupLeaderId); // 至少包含自己
            }

            // 获取同组所有销售ID
            List<String> groupMemberIds = saleProfileService.lambdaQuery()
                    .eq(SaleProfile::getSalesGroupId, leaderProfile.getSalesGroupId())
                    .eq(SaleProfile::getDeleted, false)
                    .list()
                    .stream()
                    .map(SaleProfile::getSalesId)
                    .toList();

            return groupMemberIds.isEmpty() ? List.of(salesGroupLeaderId) : groupMemberIds;

        } catch (Exception e) {
            log.error("获取销售组成员失败: salesGroupLeaderId={}", salesGroupLeaderId, e);
            return List.of(salesGroupLeaderId); // 出错时至少返回自己
        }
    }

    /**
     * 转换为基础响应分页DTO（优化版本：批量查询）
     */
    private IPage<CourseBookingDto.BasicResp> convertToBasicRespPage(IPage<CourseBookingApplication> applicationPage) {
        Page<CourseBookingDto.BasicResp> result = new Page<>(
                applicationPage.getCurrent(),
                applicationPage.getSize(),
                applicationPage.getTotal()
        );

        // 使用批量转换方法
        List<CourseBookingDto.BasicResp> records = convertToBasicRespBatch(applicationPage.getRecords());
        result.setRecords(records);
        return result;
    }

    /**
     * 批量转换为基础响应DTO（避免N+1查询问题）
     */
    private List<CourseBookingDto.BasicResp> convertToBasicRespBatch(List<CourseBookingApplication> applications) {
        if (CollUtil.isEmpty(applications)) {
            return new ArrayList<>();
        }

        try {
            // 收集所有需要查询的ID
            Set<String> studentIds = new HashSet<>();
            Set<String> salesIds = new HashSet<>();
            Set<String> salesGroupIds = new HashSet<>();
            Set<String> approvedTeacherIds = new HashSet<>();
            Set<String> approvalByIds = new HashSet<>();
            Set<String> allTeacherIds = new HashSet<>();

            for (CourseBookingApplication app : applications) {
                if (StrUtil.isNotEmpty(app.getStudentId())) {
                    studentIds.add(app.getStudentId());
                }
                if (StrUtil.isNotEmpty(app.getSalesId())) {
                    salesIds.add(app.getSalesId());
                }
                if (StrUtil.isNotEmpty(app.getSalesGroupId())) {
                    salesGroupIds.add(app.getSalesGroupId());
                }
                if (StrUtil.isNotEmpty(app.getApprovedTeacherId())) {
                    approvedTeacherIds.add(app.getApprovedTeacherId());
                }
                if (StrUtil.isNotEmpty(app.getApprovalBy())) {
                    approvalByIds.add(app.getApprovalBy());
                }
                if (CollUtil.isNotEmpty(app.getPreferredTeachers())) {
                    allTeacherIds.addAll(app.getPreferredTeachers());
                }
            }
            allTeacherIds.addAll(approvedTeacherIds);
            allTeacherIds.addAll(approvalByIds);

            // 批量查询所有相关数据
            Map<String, UserStudentExt> studentMap = batchQueryStudents(studentIds);
            Map<String, SaleProfile> salesMap = batchQuerySales(salesIds);
            Map<String, SalesGroup> salesGroupMap = batchQuerySalesGroups(salesGroupIds);
            Map<String, TeacherProfile> teacherMap = batchQueryTeachers(allTeacherIds);
            Map<String, String> teacherGroupMap = batchQueryTeacherGroups(allTeacherIds);

            // 转换结果
            return applications.stream()
                    .map(app -> convertToBasicRespWithCache(app, studentMap, salesMap, salesGroupMap, teacherMap, teacherGroupMap))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("批量转换基础响应DTO失败", e);
            // 降级处理：使用原有的单个转换方法
            return applications.stream()
                    .map(this::convertToBasicResp)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 应用基于教学组视角的状态过滤
     */
    private void applyTeachingGroupStatusFilter(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper<CourseBookingApplication> queryWrapper,
                                               TeachingGroupLeaderDto.GetPendingApplicationsReq req, String currentUserId) {
        try {
            // 处理状态查询
            if (req.getStatusList() != null && !req.getStatusList().isEmpty()) {
                // 检查是否包含"待审核"状态
                if (req.getStatusList().contains("待审核")) {
                    // 对于"待审核"状态，需要特殊处理：排除当前教学组已处理的申请
                    applyPendingStatusFilter(queryWrapper, currentUserId, req.getStatusList());
                } else {
                    // 其他状态直接按全局状态查询
                    queryWrapper.in(CourseBookingApplication::getStatus, req.getStatusList());
                }
            } else if (StrUtil.isNotEmpty(req.getStatus())) {
                if ("待审核".equals(req.getStatus())) {
                    // 对于"待审核"状态，排除当前教学组已处理的申请
                    applyPendingStatusFilter(queryWrapper, currentUserId, Arrays.asList("待审核"));
                } else {
                    // 其他状态直接按全局状态查询
                    queryWrapper.eq(CourseBookingApplication::getStatus, req.getStatus());
                }
            }

            log.debug("应用教学组状态过滤: 用户ID={}, 状态列表={}, 单一状态={}",
                    currentUserId, req.getStatusList(), req.getStatus());

        } catch (Exception e) {
            log.error("应用教学组状态过滤失败: userId={}", currentUserId, e);
            // 降级处理：使用原有的简单状态过滤
            if (req.getStatusList() != null && !req.getStatusList().isEmpty()) {
                queryWrapper.in(CourseBookingApplication::getStatus, req.getStatusList());
            } else if (StrUtil.isNotEmpty(req.getStatus())) {
                queryWrapper.eq(CourseBookingApplication::getStatus, req.getStatus());
            }
        }
    }

    /**
     * 应用待审核状态过滤（排除当前教学组已处理的申请）
     */
    private void applyPendingStatusFilter(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper<CourseBookingApplication> queryWrapper,
                                         String currentUserId, List<String> statusList) {
        try {
            // 获取当前用户的教学组ID
            List<String> userTeachingGroupIds = getCurrentUserTeachingGroupIds(currentUserId);

            if (!userTeachingGroupIds.isEmpty()) {
                // 查找当前用户教学组已经审核过的申请ID
                List<String> reviewedApplicationIds = courseBookingApplicationReviewService.lambdaQuery()
                        .select(CourseBookingApplicationReview::getApplicationId)
                        .in(CourseBookingApplicationReview::getTeachingGroupId, userTeachingGroupIds)
                        .eq(CourseBookingApplicationReview::getDeleted, false)
                        .list()
                        .stream()
                        .map(CourseBookingApplicationReview::getApplicationId)
                        .distinct()
                        .collect(Collectors.toList());

                log.debug("用户 {} 的教学组 {} 已审核的申请: {}", currentUserId, userTeachingGroupIds, reviewedApplicationIds);

                // 应用状态过滤
                queryWrapper.in(CourseBookingApplication::getStatus, statusList);

                // 排除已审核的申请
                if (!reviewedApplicationIds.isEmpty()) {
                    queryWrapper.notIn(CourseBookingApplication::getId, reviewedApplicationIds);
                    log.debug("排除已审核申请: {}", reviewedApplicationIds);
                }
            } else {
                // 如果用户没有教学组，直接按状态查询
                queryWrapper.in(CourseBookingApplication::getStatus, statusList);
                log.debug("用户没有教学组，直接按状态查询");
            }

        } catch (Exception e) {
            log.error("应用待审核状态过滤失败: userId={}", currentUserId, e);
            // 降级处理：直接按状态查询
            queryWrapper.in(CourseBookingApplication::getStatus, statusList);
        }
    }

    /**
     * 获取基于教学组视角的状态文本
     */
    private String getTeachingGroupPerspectiveStatusText(CourseBookingApplication application) {
        try {
            String currentUserId = SecurityUtils.getUserId().toString();
            List<String> userTeachingGroupIds = getCurrentUserTeachingGroupIds(currentUserId);

            log.debug("获取教学组视角状态: 申请ID={}, 用户ID={}, 教学组={}",
                    application.getId(), currentUserId, userTeachingGroupIds);

            if (userTeachingGroupIds.isEmpty()) {
                log.debug("用户没有管理的教学组，返回默认状态");
                return getStatusText(application.getStatus());
            }

            // 查询当前用户教学组对该申请的审核记录
            List<CourseBookingApplicationReview> userGroupReviews = courseBookingApplicationReviewService.lambdaQuery()
                    .eq(CourseBookingApplicationReview::getApplicationId, application.getId())
                    .in(CourseBookingApplicationReview::getTeachingGroupId, userTeachingGroupIds)
                    .eq(CourseBookingApplicationReview::getDeleted, false)
                    .list();

            log.debug("申请ID: {}, 审核记录数量: {}", application.getId(), userGroupReviews.size());

            String globalStatus = application.getStatus();

            if ("待审核".equals(globalStatus)) {
                // 全局状态为待审核
                if (!userGroupReviews.isEmpty()) {
                    // 当前用户教学组已审核
                    CourseBookingApplicationReview userReview = userGroupReviews.get(0);
                    log.debug("本组审核结果: {}", userReview.getReviewResult());

                    if ("已通过".equals(userReview.getReviewResult())) {
                        return "待审核(本组已通过)";
                    } else if ("已拒绝".equals(userReview.getReviewResult())) {
                        return "待审核(本组已拒绝)";
                    }
                }
                return "待审核";
            } else if ("已通过".equals(globalStatus)) {
                // 全局状态为已通过
                if (!userGroupReviews.isEmpty()) {
                    CourseBookingApplicationReview userReview = userGroupReviews.get(0);
                    if ("已通过".equals(userReview.getReviewResult())) {
                        return "已通过(本组通过)";
                    } else if ("已拒绝".equals(userReview.getReviewResult())) {
                        return "已通过(本组拒绝)";
                    }
                }
                return "已通过";
            } else if ("已拒绝".equals(globalStatus)) {
                // 全局状态为已拒绝
                if (!userGroupReviews.isEmpty()) {
                    CourseBookingApplicationReview userReview = userGroupReviews.get(0);
                    if ("已拒绝".equals(userReview.getReviewResult())) {
                        return "已拒绝(本组拒绝)";
                    }
                }
                return "已拒绝";
            }

            return getStatusText(globalStatus);
        } catch (Exception e) {
            log.error("获取教学组视角状态文本失败: applicationId={}", application.getId(), e);
            return getStatusText(application.getStatus());
        }
    }

    /**
     * 获取当前用户管理的教学组ID列表
     */
    private List<String> getCurrentUserTeachingGroupIds(String userId) {
        try {
            // 获取该用户管理的所有教学组
            List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                    .eq(TeachingGroup::getLeaderId, userId)
                    .eq(TeachingGroup::getDeleted, false)
                    .list();

            List<String> groupIds = managedGroups.stream()
                    .map(TeachingGroup::getId)
                    .collect(Collectors.toList());

            log.debug("用户 {} 管理的教学组: {}", userId, groupIds);
            return groupIds;
        } catch (Exception e) {
            log.warn("获取用户教学组失败: userId={}", userId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(String status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case "pending":
                return "待审核";
            case "approved":
                return "已通过";
            case "rejected":
                return "已拒绝";
            case "confirmed":
                return "已确认";
            case "cancelled":
                return "已取消";
            default:
                return status;
        }
    }

    /**
     * 解析首选教师信息（优化版本：批量查询，包含教学组信息）
     */
    private List<CourseBookingDto.PreferredTeacherInfo> parsePreferredTeachers(List<String> preferredTeachers) {
        List<CourseBookingDto.PreferredTeacherInfo> result = new ArrayList<>();

        if (CollUtil.isEmpty(preferredTeachers)) {
            return result;
        }

        try {
            // 批量查询教师基本信息
            List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                    .in(TeacherProfile::getTeacherId, preferredTeachers)
                    .eq(TeacherProfile::getDeleted, false)
                    .list();

            if (CollUtil.isEmpty(teachers)) {
                return result;
            }

            // 批量查询教学组成员关系
            List<String> teacherIds = teachers.stream()
                    .map(TeacherProfile::getTeacherId)
                    .collect(Collectors.toList());

            List<TeachingGroupMember> groupMembers = teachingGroupMemberService.lambdaQuery()
                    .in(TeachingGroupMember::getTeacherId, teacherIds)
                    .eq(TeachingGroupMember::getDeleted, false)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .list();

            // 批量查询教学组信息
            List<String> groupIds = groupMembers.stream()
                    .map(TeachingGroupMember::getGroupId)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询教学组信息并构建映射
            final Map<String, String> groupNameMap;
            if (CollUtil.isNotEmpty(groupIds)) {
                List<TeachingGroup> groups = teachingGroupService.lambdaQuery()
                        .in(TeachingGroup::getId, groupIds)
                        .eq(TeachingGroup::getDeleted, false)
                        .list();

                groupNameMap = groups.stream()
                        .collect(Collectors.toMap(
                                TeachingGroup::getId,
                                TeachingGroup::getName,
                                (existing, replacement) -> existing
                        ));
            } else {
                groupNameMap = new HashMap<>();
            }

            // 构建教师ID到教学组名称的映射
            Map<String, String> teacherGroupMap = groupMembers.stream()
                    .collect(Collectors.toMap(
                            TeachingGroupMember::getTeacherId,
                            member -> groupNameMap.getOrDefault(member.getGroupId(), ""),
                            (existing, replacement) -> existing
                    ));

            // 构建结果
            return teachers.stream().map(teacher -> {
                CourseBookingDto.PreferredTeacherInfo info = new CourseBookingDto.PreferredTeacherInfo();
                info.setTeacherId(teacher.getTeacherId());
                info.setTeacherName(teacher.getRealName());
                info.setTeacherPhone(teacher.getPhonenumber());

                // 设置教学组名称，格式：张山【教学一组】
                String groupName = teacherGroupMap.get(teacher.getTeacherId());
                if (StrUtil.isNotEmpty(groupName)) {
                    info.setTeacherName(teacher.getRealName() + "【" + groupName + "】");
                    info.setGroupName(groupName);
                } else {
                    info.setGroupName("");
                }

                return info;
            }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("解析首选教师信息失败: preferredTeachers={}", preferredTeachers, e);
            // 降级处理：只返回基本教师信息
            return preferredTeachers.stream().map(teacherId -> {
                CourseBookingDto.PreferredTeacherInfo info = new CourseBookingDto.PreferredTeacherInfo();
                info.setTeacherId(teacherId);
                info.setTeacherName("教师信息获取失败");
                info.setTeacherPhone("");
                info.setGroupName("");
                return info;
            }).collect(Collectors.toList());
        }
    }

    /**
     * 解析首选时间段
     */
    private List<CourseBookingDto.PreferredTimeSlot> parsePreferredTimeSlots(List<CourseBookingApplication.PreferredTimeSlot> timeSlots) {
        List<CourseBookingDto.PreferredTimeSlot> result = new ArrayList<>();

        if (CollUtil.isEmpty(timeSlots)) {
            return result;
        }

        for (CourseBookingApplication.PreferredTimeSlot slot : timeSlots) {
            CourseBookingDto.PreferredTimeSlot dto = new CourseBookingDto.PreferredTimeSlot();
            dto.setWeekday(slot.getWeekday());
            dto.setStartTime(slot.getStartTime());
            dto.setEndTime(slot.getEndTime());
            // 移除优先级字段
            result.add(dto);
        }

        return result;
    }

    /**
     * 应用学生过滤条件
     */
    private void applyStudentFilter(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper<CourseBookingApplication> queryWrapper,
                                    TeachingGroupLeaderDto.GetPendingApplicationsReq req) {
        try {
            List<String> studentIds = new ArrayList<>();

            // 根据学生姓名查询学生ID
            if (StrUtil.isNotEmpty(req.getStudentName())) {
                List<UserStudentExt> students = userStudentExtService.lambdaQuery()
                        .like(UserStudentExt::getName, req.getStudentName())
                        .eq(UserStudentExt::getDeleted, false)
                        .list();
                studentIds.addAll(students.stream().map(UserStudentExt::getStudentId).collect(Collectors.toList()));
            }

            // 根据学生手机号查询学生ID
            if (StrUtil.isNotEmpty(req.getStudentPhone())) {
                List<UserStudentExt> students = userStudentExtService.lambdaQuery()
                        .like(UserStudentExt::getPhone, req.getStudentPhone())
                        .eq(UserStudentExt::getDeleted, false)
                        .list();
                studentIds.addAll(students.stream().map(UserStudentExt::getStudentId).collect(Collectors.toList()));
            }

            if (!studentIds.isEmpty()) {
                // 去重
                studentIds = studentIds.stream().distinct().collect(Collectors.toList());
                queryWrapper.in(CourseBookingApplication::getStudentId, studentIds);
            } else if (StrUtil.isNotEmpty(req.getStudentName()) || StrUtil.isNotEmpty(req.getStudentPhone())) {
                // 如果有查询条件但没有找到匹配的学生，返回空结果
                queryWrapper.eq(CourseBookingApplication::getStudentId, "NONE");
            }

        } catch (Exception e) {
            log.error("应用学生过滤条件失败", e);
            // 出错时不显示任何结果
            queryWrapper.eq(CourseBookingApplication::getStudentId, "NONE");
        }
    }

    /**
     * 应用老师过滤条件
     */
    private void applyTeacherFilter(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper<CourseBookingApplication> queryWrapper,
                                    TeachingGroupLeaderDto.GetPendingApplicationsReq req, List<String> allowedTeacherIds) {
        try {
            List<String> teacherIds = new ArrayList<>();

            // 根据老师姓名查询老师ID
            if (StrUtil.isNotEmpty(req.getTeacherName())) {
                List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                        .like(TeacherProfile::getRealName, req.getTeacherName())
                        .eq(TeacherProfile::getDeleted, false)
                        .list();
                teacherIds.addAll(teachers.stream().map(TeacherProfile::getTeacherId).collect(Collectors.toList()));
            }

            // 根据老师手机号查询老师ID
            if (StrUtil.isNotEmpty(req.getTeacherPhone())) {
                List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                        .like(TeacherProfile::getPhonenumber, req.getTeacherPhone())
                        .eq(TeacherProfile::getDeleted, false)
                        .list();
                teacherIds.addAll(teachers.stream().map(TeacherProfile::getTeacherId).collect(Collectors.toList()));
            }

            if (!teacherIds.isEmpty()) {
                // 去重并且只保留允许的老师ID（本组的老师）
                teacherIds = teacherIds.stream()
                        .distinct()
                        .filter(allowedTeacherIds::contains)
                        .collect(Collectors.toList());

                if (!teacherIds.isEmpty()) {
                    // 使用PostgreSQL数组操作符过滤申请（候选老师中包含查询的老师）
                    String teacherIdsStr = teacherIds.stream()
                            .map(id -> "'" + id + "'::varchar")
                            .collect(Collectors.joining(","));

                    queryWrapper.apply("preferred_teachers && ARRAY[" + teacherIdsStr + "]::varchar[]");
                } else {
                    // 查询的老师不在本组中，返回空结果
                    queryWrapper.eq(CourseBookingApplication::getId, "NONE");
                }
            } else if (StrUtil.isNotEmpty(req.getTeacherName()) || StrUtil.isNotEmpty(req.getTeacherPhone())) {
                // 如果有查询条件但没有找到匹配的老师，返回空结果
                queryWrapper.eq(CourseBookingApplication::getId, "NONE");
            }

        } catch (Exception e) {
            log.error("应用老师过滤条件失败", e);
            // 出错时不显示任何结果
            queryWrapper.eq(CourseBookingApplication::getId, "NONE");
        }
    }

    /**
     * 应用老师过滤条件（用于搜索，不添加重复的数组过滤）
     */
    private void applyTeacherFilterForSearch(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper<CourseBookingApplication> queryWrapper,
                                             TeachingGroupLeaderDto.GetPendingApplicationsReq req, List<String> allowedTeacherIds) {
        try {
            List<String> teacherIds = new ArrayList<>();

            // 根据老师姓名查询老师ID
            if (StrUtil.isNotEmpty(req.getTeacherName())) {
                List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                        .like(TeacherProfile::getRealName, req.getTeacherName())
                        .eq(TeacherProfile::getDeleted, false)
                        .list();
                teacherIds.addAll(teachers.stream().map(TeacherProfile::getTeacherId).collect(Collectors.toList()));
            }

            // 根据老师手机号查询老师ID
            if (StrUtil.isNotEmpty(req.getTeacherPhone())) {
                List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                        .like(TeacherProfile::getPhonenumber, req.getTeacherPhone())
                        .eq(TeacherProfile::getDeleted, false)
                        .list();
                teacherIds.addAll(teachers.stream().map(TeacherProfile::getTeacherId).collect(Collectors.toList()));
            }

            if (!teacherIds.isEmpty()) {
                // 去重并且只保留允许的老师ID（本组的老师）
                teacherIds = teacherIds.stream()
                        .distinct()
                        .filter(allowedTeacherIds::contains)
                        .collect(Collectors.toList());

                if (teacherIds.isEmpty()) {
                    // 查询的老师不在本组中，返回空结果
                    queryWrapper.eq(CourseBookingApplication::getId, "NONE");
                }
                // 注意：这里不添加数组过滤条件，因为主查询已经有了
            } else if (StrUtil.isNotEmpty(req.getTeacherName()) || StrUtil.isNotEmpty(req.getTeacherPhone())) {
                // 如果有查询条件但没有找到匹配的老师，返回空结果
                queryWrapper.eq(CourseBookingApplication::getId, "NONE");
            }

        } catch (Exception e) {
            log.error("应用老师过滤条件失败", e);
            // 出错时不显示任何结果
            queryWrapper.eq(CourseBookingApplication::getId, "NONE");
        }
    }

    /**
     * 计算下一个符合条件的课程日期
     */
    private LocalDate calculateNextCourseDate(int weekday) {
        LocalDate today = LocalDate.now();
        while(today.getDayOfWeek().getValue() != weekday){
            today = today.plusDays(1);
        }
        return today;
    }

    // ==================== 批量查询优化方法 ====================

    /**
     * 批量查询学生信息
     */
    private Map<String, UserStudentExt> batchQueryStudents(Set<String> studentIds) {
        if (CollUtil.isEmpty(studentIds)) {
            return new HashMap<>();
        }

        try {
            List<UserStudentExt> students = userStudentExtService.lambdaQuery()
                    .in(UserStudentExt::getStudentId, studentIds)
                    .eq(UserStudentExt::getDeleted, false)
                    .list();

            return students.stream()
                    .collect(Collectors.toMap(
                            UserStudentExt::getStudentId,
                            student -> student,
                            (existing, replacement) -> existing
                    ));
        } catch (Exception e) {
            log.error("批量查询学生信息失败: studentIds={}", studentIds, e);
            return new HashMap<>();
        }
    }

    /**
     * 批量查询销售信息
     */
    private Map<String, SaleProfile> batchQuerySales(Set<String> salesIds) {
        if (CollUtil.isEmpty(salesIds)) {
            return new HashMap<>();
        }

        try {
            List<SaleProfile> sales = saleProfileService.lambdaQuery()
                    .in(SaleProfile::getSalesId, salesIds)
                    .eq(SaleProfile::getDeleted, false)
                    .list();

            return sales.stream()
                    .collect(Collectors.toMap(
                            SaleProfile::getSalesId,
                            sale -> sale,
                            (existing, replacement) -> existing
                    ));
        } catch (Exception e) {
            log.error("批量查询销售信息失败: salesIds={}", salesIds, e);
            return new HashMap<>();
        }
    }

    /**
     * 批量查询销售组信息
     */
    private Map<String, SalesGroup> batchQuerySalesGroups(Set<String> salesGroupIds) {
        if (CollUtil.isEmpty(salesGroupIds)) {
            return new HashMap<>();
        }

        try {
            List<SalesGroup> salesGroups = salesGroupService.lambdaQuery()
                    .in(SalesGroup::getId, salesGroupIds)
                    .eq(SalesGroup::getDeleted, false)
                    .list();

            return salesGroups.stream()
                    .collect(Collectors.toMap(
                            SalesGroup::getId,
                            group -> group,
                            (existing, replacement) -> existing
                    ));
        } catch (Exception e) {
            log.error("批量查询销售组信息失败: salesGroupIds={}", salesGroupIds, e);
            return new HashMap<>();
        }
    }

    /**
     * 批量查询教师信息
     */
    private Map<String, TeacherProfile> batchQueryTeachers(Set<String> teacherIds) {
        if (CollUtil.isEmpty(teacherIds)) {
            return new HashMap<>();
        }

        try {
            List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                    .in(TeacherProfile::getTeacherId, teacherIds)
                    .eq(TeacherProfile::getDeleted, false)
                    .list();

            return teachers.stream()
                    .collect(Collectors.toMap(
                            TeacherProfile::getTeacherId,
                            teacher -> teacher,
                            (existing, replacement) -> existing
                    ));
        } catch (Exception e) {
            log.error("批量查询教师信息失败: teacherIds={}", teacherIds, e);
            return new HashMap<>();
        }
    }

    /**
     * 批量查询教师教学组信息
     */
    private Map<String, String> batchQueryTeacherGroups(Set<String> teacherIds) {
        if (CollUtil.isEmpty(teacherIds)) {
            return new HashMap<>();
        }

        try {
            // 批量查询教学组成员关系
            List<TeachingGroupMember> groupMembers = teachingGroupMemberService.lambdaQuery()
                    .in(TeachingGroupMember::getTeacherId, teacherIds)
                    .eq(TeachingGroupMember::getDeleted, false)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .list();

            if (CollUtil.isEmpty(groupMembers)) {
                return new HashMap<>();
            }

            // 批量查询教学组信息
            Set<String> groupIds = groupMembers.stream()
                    .map(TeachingGroupMember::getGroupId)
                    .collect(Collectors.toSet());

            List<TeachingGroup> groups = teachingGroupService.lambdaQuery()
                    .in(TeachingGroup::getId, groupIds)
                    .eq(TeachingGroup::getDeleted, false)
                    .list();

            Map<String, String> groupNameMap = groups.stream()
                    .collect(Collectors.toMap(
                            TeachingGroup::getId,
                            TeachingGroup::getName,
                            (existing, replacement) -> existing
                    ));

            // 构建教师ID到教学组名称的映射
            return groupMembers.stream()
                    .collect(Collectors.toMap(
                            TeachingGroupMember::getTeacherId,
                            member -> groupNameMap.getOrDefault(member.getGroupId(), ""),
                            (existing, replacement) -> existing
                    ));

        } catch (Exception e) {
            log.error("批量查询教师教学组信息失败: teacherIds={}", teacherIds, e);
            return new HashMap<>();
        }
    }

    /**
     * 使用缓存数据转换为基础响应DTO
     */
    private CourseBookingDto.BasicResp convertToBasicRespWithCache(
            CourseBookingApplication application,
            Map<String, UserStudentExt> studentMap,
            Map<String, SaleProfile> salesMap,
            Map<String, SalesGroup> salesGroupMap,
            Map<String, TeacherProfile> teacherMap,
            Map<String, String> teacherGroupMap) {

        CourseBookingDto.BasicResp resp = new CourseBookingDto.BasicResp();

        // 基础信息
        resp.setId(application.getId());
        resp.setStudentId(application.getStudentId());
        resp.setSubject(application.getSubject());
        resp.setSpecification(application.getSpecification());
        resp.setStatus(application.getStatus());
        resp.setApplicationReason(application.getApplicationReason());
        resp.setCreateTime(application.getCreateTime());
        resp.setUpdateTime(application.getUpdateTime());

        // 转换试听课时间
        if (application.getTrialClassDate() != null &&
            application.getTrialClassStartTime() != null &&
            application.getTrialClassEndTime() != null) {
            CourseBookingDto.TrialClassTime trialClassTime = new CourseBookingDto.TrialClassTime();
            trialClassTime.setDate(application.getTrialClassDate());
            trialClassTime.setStartTime(application.getTrialClassStartTime().toString());
            trialClassTime.setEndTime(application.getTrialClassEndTime().toString());
            resp.setTrialClassTime(trialClassTime);
        }

        // 转换首选时间段
        if (application.getPreferredTimeSlots() != null) {
            List<CourseBookingDto.PreferredTimeSlot> timeSlots = application.getPreferredTimeSlots().stream()
                    .map(slot -> {
                        CourseBookingDto.PreferredTimeSlot timeSlot = new CourseBookingDto.PreferredTimeSlot();
                        timeSlot.setWeekday(slot.getWeekday());
                        timeSlot.setStartTime(slot.getStartTime());
                        timeSlot.setEndTime(slot.getEndTime());
                        return timeSlot;
                    })
                    .collect(Collectors.toList());
            resp.setPreferredTimeSlots(timeSlots);
        }

        // 设置销售信息（直接从申请对象获取）
        resp.setSalesId(application.getSalesId());
        resp.setSalesGroupId(application.getSalesGroupId());

        // 设置基于教学组视角的状态文本
        String statusText = getTeachingGroupPerspectiveStatusText(application);
        resp.setStatusText(statusText);

        // 设置学生信息（从缓存获取）
        if (StrUtil.isNotEmpty(application.getStudentId())) {
            UserStudentExt student = studentMap.get(application.getStudentId());
            if (student != null) {
                resp.setStudentName(student.getName());
                resp.setStudentPhone(student.getPhone());
            }
        }

        // 设置销售信息（从缓存获取）
        if (StrUtil.isNotEmpty(application.getSalesId())) {
            SaleProfile sales = salesMap.get(application.getSalesId());
            if (sales != null) {
                resp.setSalesName(sales.getSalesName());
                resp.setSalesGroupName(sales.getSalesGroupName());
            }
        }

        // 设置销售组信息（从缓存获取）
        if (StrUtil.isNotEmpty(application.getSalesGroupId())) {
            SalesGroup salesGroup = salesGroupMap.get(application.getSalesGroupId());
            if (salesGroup != null) {
                resp.setSalesGroupName(salesGroup.getName());
            }
        }

        // 处理首选教师信息（优化版本）
        if (CollUtil.isNotEmpty(application.getPreferredTeachers())) {
            List<CourseBookingDto.PreferredTeacherInfo> teacherInfos = parsePreferredTeachersWithCache(
                    application.getPreferredTeachers(), teacherMap, teacherGroupMap);
            resp.setPreferredTeacherInfos(teacherInfos);
        }

        // 处理首选时间段
        if (CollUtil.isNotEmpty(application.getPreferredTimeSlots())) {
            List<CourseBookingDto.PreferredTimeSlot> timeSlots = parsePreferredTimeSlots(application.getPreferredTimeSlots());
            resp.setPreferredTimeSlots(timeSlots);
        }

        // 处理审核信息
        resp.setApprovedTeacherId(application.getApprovedTeacherId());
        resp.setApprovalTime(application.getApprovalTime());
        resp.setApprovalBy(application.getApprovalBy());
        resp.setRejectionReason(application.getRejectionReason());
        resp.setCourseHoursPackageId(application.getCourseHoursPackageId());

        // 设置审核教师信息（从缓存获取）
        if (StrUtil.isNotEmpty(application.getApprovedTeacherId())) {
            TeacherProfile approvedTeacher = teacherMap.get(application.getApprovedTeacherId());
            if (approvedTeacher != null) {
                resp.setApprovedTeacherName(approvedTeacher.getRealName());
            }
        }

        // 设置审核人信息（从缓存获取）
        if (StrUtil.isNotEmpty(application.getApprovalBy())) {
            TeacherProfile approvalBy = teacherMap.get(application.getApprovalBy());
            if (approvalBy != null) {
                resp.setApprovalByName(approvalBy.getRealName());
            }
        }

        // 设置审核权限：只有待审核状态且用户有教学组长权限才能审核
        resp.setCanReview(
                CourseBookingApplication.Status.PENDING.getCode().equals(application.getStatus())
                        && systemDataQueryUtil.isTeacherGroupManager()
        );

        return resp;
    }

    /**
     * 使用缓存数据解析首选教师信息
     */
    private List<CourseBookingDto.PreferredTeacherInfo> parsePreferredTeachersWithCache(
            List<String> preferredTeachers,
            Map<String, TeacherProfile> teacherMap,
            Map<String, String> teacherGroupMap) {

        if (CollUtil.isEmpty(preferredTeachers)) {
            return new ArrayList<>();
        }

        return preferredTeachers.stream()
                .map(teacherId -> {
                    CourseBookingDto.PreferredTeacherInfo info = new CourseBookingDto.PreferredTeacherInfo();
                    info.setTeacherId(teacherId);

                    TeacherProfile teacher = teacherMap.get(teacherId);
                    if (teacher != null) {
                        String groupName = teacherGroupMap.getOrDefault(teacherId, "");

                        // 设置教师名称，格式：张山【教学一组】
                        if (StrUtil.isNotEmpty(groupName)) {
                            info.setTeacherName(teacher.getRealName() + "【" + groupName + "】");
                            info.setGroupName(groupName);
                        } else {
                            info.setTeacherName(teacher.getRealName());
                            info.setGroupName("");
                        }

                        info.setTeacherPhone(teacher.getPhonenumber());
                    } else {
                        info.setTeacherName("教师信息获取失败");
                        info.setTeacherPhone("");
                        info.setGroupName("");
                    }

                    return info;
                })
                .collect(Collectors.toList());
    }

    /**
     * 处理审核通过
     */
    private boolean handleApprovalReview(TeachingGroupLeaderDto.ReviewApplicationReq req,
                                       CourseBookingApplication application,
                                       CourseBookingApplicationReview review) {
        try {
            log.info("处理审核通过: applicationId={}, teachingGroupId={}", req.getApplicationId(), review.getTeachingGroupId());

            if (req.getAssignedTeacherId() == null) {
                throw new RuntimeException("审核通过时必须指定分配的教师");
            }

            if (req.getAssignedTrialTimeSlot() == null) {
                throw new RuntimeException("审核通过时必须指定试听课时间");
            }

            // 设置审核记录的分配教师信息
            review.setAssignedTeacherId(req.getAssignedTeacherId());
            if (req.getAssignedTimeSlot() != null) {
                review.setAssignedTimeSlot(cn.hutool.json.JSONUtil.toJsonStr(req.getAssignedTimeSlot()));
            }
            // 支持试听课时间段
            if (req.getAssignedTrialTimeSlot() != null) {
                review.setAssignedTimeSlot(cn.hutool.json.JSONUtil.toJsonStr(req.getAssignedTrialTimeSlot()));
            }

            // 保存审核记录
            boolean reviewSaved = courseBookingApplicationReviewService.save(review);
            if (!reviewSaved) {
                throw new RuntimeException("保存审核记录失败");
            }

            // 调用统一的自动化处理流程（师生关系、课时包分配、状态更新、微信通知）
            CourseBookingApplication.PreferredTimeSlot timeSlot = null;

            // 优先使用试听课时间段，如果没有则使用学生偏好时间段
            if (req.getAutoSchedule() != null && req.getAutoSchedule()) {
                if (req.getAssignedTrialTimeSlot() != null) {
                    // 使用试听课时间段
                    timeSlot = new CourseBookingApplication.PreferredTimeSlot();
                    timeSlot.setStartTime(req.getAssignedTrialTimeSlot().getStartTime());
                    timeSlot.setEndTime(req.getAssignedTrialTimeSlot().getEndTime());

                    // 根据试听课日期计算星期几
                    try {
                        java.time.LocalDate trialDate = java.time.LocalDate.parse(req.getAssignedTrialTimeSlot().getDate());
                        int weekday = trialDate.getDayOfWeek().getValue(); // 1=周一, 7=周日
                        timeSlot.setWeekday(weekday);
                    } catch (Exception e) {
                        log.warn("解析试听课日期失败: {}", req.getAssignedTrialTimeSlot().getDate(), e);
                        timeSlot.setWeekday(1); // 默认周一
                    }
                } else if (req.getAssignedTimeSlot() != null) {
                    // 使用学生偏好时间段
                    timeSlot = new CourseBookingApplication.PreferredTimeSlot();
                    timeSlot.setStartTime(req.getAssignedTimeSlot().getStartTime());
                    timeSlot.setEndTime(req.getAssignedTimeSlot().getEndTime());
                    timeSlot.setWeekday(req.getAssignedTimeSlot().getWeekday());
                }
            }

            boolean success = courseBookingAutoProcessService.processConfirmation(
                    application,
                    req.getAssignedTeacherId(),
                    timeSlot
            );

            if (!success) {
                throw new RuntimeException("审核处理失败");
            }

            log.info("审核通过处理成功: applicationId={}", req.getApplicationId());
            return true;

        } catch (Exception e) {
            log.error("处理审核通过失败: applicationId={}", req.getApplicationId(), e);
            throw new RuntimeException("处理审核通过失败: " + e.getMessage());
        }
    }

    /**
     * 处理审核拒绝
     */
    private boolean handleRejectionReview(TeachingGroupLeaderDto.ReviewApplicationReq req,
                                        CourseBookingApplication application,
                                        CourseBookingApplicationReview review) {
        try {
            log.info("处理审核拒绝: applicationId={}, teachingGroupId={}", req.getApplicationId(), review.getTeachingGroupId());

            // 保存审核记录
            boolean reviewSaved = courseBookingApplicationReviewService.save(review);
            if (!reviewSaved) {
                throw new RuntimeException("保存审核记录失败");
            }

            // 检查是否所有相关教学组都已拒绝
            boolean shouldRejectApplication = checkIfAllGroupsRejected(req.getApplicationId(), application.getPreferredTeachers());

            if (shouldRejectApplication) {
                // 所有相关教学组都拒绝，更新申请状态为已拒绝
                application.setStatus(CourseBookingApplication.Status.REJECTED.getCode());
                application.setApprovalBy(review.getReviewerId());
                application.setApprovalTime(WssContext.now());
                application.setRejectionReason(req.getRejectionReason());

                boolean success = courseBookingApplicationService.updateById(application);
                if (success) {
                    log.info("申请状态更新为已拒绝: applicationId={}", req.getApplicationId());
                    wechatNotificationService.sendCourseBookingNotification(application.getId(), "REJECTED_TO_SALES");
                } else {
                    log.warn("更新申请状态失败: applicationId={}", req.getApplicationId());
                }
                return success;
            } else {
                // 还有其他教学组未审核，保持待审核状态
                log.info("还有其他教学组未审核，保持待审核状态: applicationId={}", req.getApplicationId());
                return true;
            }

        } catch (Exception e) {
            log.error("处理审核拒绝失败: applicationId={}", req.getApplicationId(), e);
            throw new RuntimeException("处理审核拒绝失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否所有相关教学组都已拒绝
     */
    private boolean checkIfAllGroupsRejected(String applicationId, List<String> preferredTeachers) {
        try {
            log.info("检查所有相关教学组审核状态: applicationId={}", applicationId);

            if (CollUtil.isEmpty(preferredTeachers)) {
                log.warn("申请没有候选老师: applicationId={}", applicationId);
                return true; // 没有候选老师，可以直接拒绝
            }

            // 获取所有候选老师所属的教学组
            Set<String> relatedGroupIds = getRelatedTeachingGroups(preferredTeachers);
            if (relatedGroupIds.isEmpty()) {
                log.warn("候选老师没有关联的教学组: applicationId={}", applicationId);
                return true; // 没有关联教学组，可以直接拒绝
            }

            // 获取已审核的记录
            List<CourseBookingApplicationReview> reviews = courseBookingApplicationReviewService.getReviewsByApplicationId(applicationId);
            Set<String> reviewedGroupIds = reviews.stream()
                    .map(CourseBookingApplicationReview::getTeachingGroupId)
                    .collect(Collectors.toSet());

            // 检查是否有通过的审核
            boolean hasApproved = reviews.stream()
                    .anyMatch(review -> CourseBookingApplicationReview.ReviewResult.APPROVED.getCode().equals(review.getReviewResult()));

            if (hasApproved) {
                log.info("已有教学组通过审核，不应拒绝申请: applicationId={}", applicationId);
                return false; // 有通过的，不应该拒绝
            }

            // 检查是否所有相关教学组都已拒绝
            Set<String> rejectedGroupIds = reviews.stream()
                    .filter(review -> CourseBookingApplicationReview.ReviewResult.REJECTED.getCode().equals(review.getReviewResult()))
                    .map(CourseBookingApplicationReview::getTeachingGroupId)
                    .collect(Collectors.toSet());

            boolean allGroupsRejected = rejectedGroupIds.containsAll(relatedGroupIds);
            log.info("教学组审核状态检查结果: applicationId={}, relatedGroups={}, rejectedGroups={}, allRejected={}",
                    applicationId, relatedGroupIds.size(), rejectedGroupIds.size(), allGroupsRejected);

            return allGroupsRejected;

        } catch (Exception e) {
            log.error("检查教学组审核状态失败: applicationId={}", applicationId, e);
            return false; // 出错时保守处理，不拒绝申请
        }
    }

    /**
     * 获取候选老师相关的教学组ID集合
     */
    private Set<String> getRelatedTeachingGroups(List<String> preferredTeachers) {
        try {
            List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                    .in(TeachingGroupMember::getTeacherId, preferredTeachers)
                    .eq(TeachingGroupMember::getDeleted, false)
                    .eq(TeachingGroupMember::getStatus, "active")
                    .list();

            return members.stream()
                    .map(TeachingGroupMember::getGroupId)
                    .collect(Collectors.toSet());

        } catch (Exception e) {
            log.error("获取相关教学组失败: preferredTeachers={}", preferredTeachers, e);
            return new HashSet<>();
        }
    }

    /**
     * 应用教学组特定的状态过滤
     */
    private void applyGroupSpecificStatusFilter(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper<CourseBookingApplication> wrapper,
                                              TeachingGroupLeaderDto.GetPendingApplicationsReq req,
                                              String teachingGroupId) {
        try {
            log.info("应用教学组特定状态过滤: groupId={}, statusList={}", teachingGroupId, req.getStatusList());

            // 如果查询条件中包含"待审核"状态，需要特殊处理
            if (req.getStatusList() != null && req.getStatusList().contains("待审核")) {
                // 构建复合查询条件：
                // 1. 申请状态为"待审核" 且 该教学组未审核过
                // 2. 或者查询其他指定状态

                List<String> otherStatuses = req.getStatusList().stream()
                        .filter(status -> !"待审核".equals(status))
                        .collect(Collectors.toList());

                wrapper.and(w -> {
                    // 条件1：申请状态为"待审核" 且 该教学组未审核过
                    w.or(subW -> {
                        subW.eq(CourseBookingApplication::getStatus, "待审核");
                        // 添加子查询：该教学组未审核过此申请
                        subW.notExists("SELECT 1 FROM course_booking_application_review r " +
                                      "WHERE r.application_id = course_booking_application.id " +
                                      "AND r.teaching_group_id = '" + teachingGroupId + "' " +
                                      "AND r.deleted = false");
                    });

                    // 条件2：其他指定状态
                    if (!otherStatuses.isEmpty()) {
                        w.or(subW -> subW.in(CourseBookingApplication::getStatus, otherStatuses));
                    }
                });
            } else if (req.getStatusList() != null && !req.getStatusList().isEmpty()) {
                // 如果没有"待审核"状态，直接按指定状态查询
                wrapper.in(CourseBookingApplication::getStatus, req.getStatusList());
            } else if (StrUtil.isNotEmpty(req.getStatus())) {
                // 兼容单个状态查询
                if ("待审核".equals(req.getStatus())) {
                    wrapper.eq(CourseBookingApplication::getStatus, "待审核")
                           .notExists("SELECT 1 FROM course_booking_application_review r " +
                                     "WHERE r.application_id = course_booking_application.id " +
                                     "AND r.teaching_group_id = '" + teachingGroupId + "' " +
                                     "AND r.deleted = false");
                } else {
                    wrapper.eq(CourseBookingApplication::getStatus, req.getStatus());
                }
            } else {
                // 默认查询待审核状态（该教学组未审核过的）
                wrapper.eq(CourseBookingApplication::getStatus, "待审核")
                       .notExists("SELECT 1 FROM course_booking_application_review r " +
                                 "WHERE r.application_id = course_booking_application.id " +
                                 "AND r.teaching_group_id = '" + teachingGroupId + "' " +
                                 "AND r.deleted = false");
            }

            log.info("教学组特定状态过滤应用成功: groupId={}", teachingGroupId);

        } catch (Exception e) {
            log.error("应用教学组特定状态过滤失败: groupId={}", teachingGroupId, e);
            // 出错时使用原有逻辑
            if (req.getStatusList() != null && !req.getStatusList().isEmpty()) {
                wrapper.in(CourseBookingApplication::getStatus, req.getStatusList());
            } else if (StrUtil.isNotEmpty(req.getStatus())) {
                wrapper.eq(CourseBookingApplication::getStatus, req.getStatus());
            }
        }
    }

    /**
     * 转换为基础响应DTO（带教学组状态）
     */
    private CourseBookingDto.BasicResp convertToBasicRespWithGroupStatus(CourseBookingApplication application, String teachingGroupId) {
        // 先使用原有方法转换基础信息
        CourseBookingDto.BasicResp resp = convertToBasicResp(application);

        try {
            // 获取该教学组对此申请的审核记录
            CourseBookingApplicationReview groupReview = courseBookingApplicationReviewService
                    .getReviewByApplicationAndGroup(application.getId(), teachingGroupId);

            if (groupReview != null) {
                // 如果该教学组已审核，显示教学组的审核状态
                resp.setStatus(groupReview.getReviewResult());
                resp.setStatusText(getStatusText(groupReview.getReviewResult()));

                // 设置审核相关信息
                resp.setApprovalBy(groupReview.getReviewerId());
                resp.setApprovalTime(groupReview.getReviewTime());
                resp.setRejectionReason(groupReview.getRejectionReason());

                if (StrUtil.isNotEmpty(groupReview.getAssignedTeacherId())) {
                    resp.setApprovedTeacherId(groupReview.getAssignedTeacherId());
                    // 查询分配的教师姓名
                    TeacherProfile assignedTeacher = teacherProfileService.lambdaQuery()
                            .eq(TeacherProfile::getTeacherId, groupReview.getAssignedTeacherId())
                            .eq(TeacherProfile::getDeleted, false)
                            .one();
                    if (assignedTeacher != null) {
                        resp.setApprovedTeacherName(assignedTeacher.getRealName());
                    }
                }

                // 查询审核人姓名
                if (StrUtil.isNotEmpty(groupReview.getReviewerId())) {
                    TeacherProfile reviewer = teacherProfileService.lambdaQuery()
                            .eq(TeacherProfile::getTeacherId, groupReview.getReviewerId())
                            .eq(TeacherProfile::getDeleted, false)
                            .one();
                    if (reviewer != null) {
                        resp.setApprovalByName(reviewer.getRealName());
                    }
                }

                // 该教学组已审核，不能再次审核
                resp.setCanReview(false);
            } else {
                // 该教学组未审核，检查是否可以审核
                boolean canReview = CourseBookingApplication.Status.PENDING.getCode().equals(application.getStatus())
                        && systemDataQueryUtil.isTeacherGroupManager();
                resp.setCanReview(canReview);
            }

        } catch (Exception e) {
            log.error("获取教学组审核状态失败: applicationId={}, groupId={}", application.getId(), teachingGroupId, e);
            // 出错时使用原有逻辑
        }

        return resp;
    }
}
