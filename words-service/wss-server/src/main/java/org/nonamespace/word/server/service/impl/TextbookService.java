package org.nonamespace.word.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.common.util.ConvertPageUtils;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.common.utils.OssService;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.*;
import org.nonamespace.word.server.enums.GradeEnum;
import org.nonamespace.word.server.enums.StudentTextbookProgressStatusEnum;
import org.nonamespace.word.server.enums.TextBookNodeEnum;
import org.nonamespace.word.server.enums.TextBookTypeEnum;
import org.nonamespace.word.server.facade.TextbookManagerService;
import org.nonamespace.word.server.mapper.TextbookMapper;
import org.nonamespace.word.server.service.IStudentTextbookProgressService;
import org.nonamespace.word.server.service.IStudentWordProgressService;
import org.nonamespace.word.server.service.UserStudentExtService;
import org.nonamespace.word.server.service.base.BaseService;
import org.nonamespace.word.server.vo.TextbookTreeVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 词定义 (统一教材与词): 定义各种词Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Slf4j
@Service
public class TextbookService extends BaseService<TextbookMapper, Textbook> {

    @Autowired
    private WordServiceImpl wordService;

    @Autowired
    private TextbookItemService textbookItemService;

    @Autowired
    @Lazy
    private TextbookManagerService textbookManagerService;

    @Autowired
    private OssService ossService;

    @Autowired
    private HistoryTextbookServiceImpl historyTextbookService;

    @Autowired
    private IStudentTextbookProgressService studentTextbookProgressService;

    @Autowired
    private IStudentWordProgressService studentWordProgressService;

    @Autowired
    private UserStudentExtService userStudentExtService;

    private final String COURSE_TYPE = "上课";


    public Page<TextbookPageDto.Resp> page(TextbookPageDto.Req req) {
        Page<Textbook> textbookPage = baseMapper.selectPage(new Page<Textbook>(req.getPageNum(), req.getPageSize()), new QueryWrapper<Textbook>().lambda()
                .like(StringUtils.isNotBlank(req.getSearchName()), Textbook::getName, req.getSearchName())
                .in(CollectionUtil.isNotEmpty(req.getTextBookTypeList()), Textbook::getType, req.getTextBookTypeList())
                .orderByAsc(Textbook::getName));

        return ConvertPageUtils.convertToPage(textbookPage, item -> {
            TextbookPageDto.Resp result = new TextbookPageDto.Resp();
            BeanUtils.copyProperties(item, result);
            result.setTypeName(TextBookTypeEnum.getByValue(item.getType()).getText());
            return result;
        });
    }

    public AjaxResult insertOrUpdateTextbook(TextbookDto textbookDto) {
        Textbook textbook = new Textbook();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 验证是否为空
        if (StringUtils.isNotEmpty(textbookDto.getId())) {
            textbook = getById(textbookDto.getId());
            if (Objects.isNull(textbook) || Boolean.TRUE.equals(textbook.getDeleted())) {
                return AjaxResult.error("该词表不存在，请重新确认");
            }
        } else {
            textbook.setOwnerId(String.valueOf(loginUser.getUserId()));
        }

        BeanUtils.copyProperties(textbookDto, textbook);

        if (Objects.nonNull(textbookDto.getCoverFile())) {
            //封面上传oss
            String coverUrl = ossService.uploadFile(textbookDto.getCoverFile(), textbookDto.getCoverName(textbook.getName()));
            textbook.setCover(coverUrl);
        }

        if(StrUtil.isNotEmpty(textbookDto.getStudentId())){

            UserStudentExt use = userStudentExtService.lambdaQuery().eq(UserStudentExt::getStudentId, textbookDto.getStudentId())
                    .list().stream().findFirst().orElseThrow(()->new IllegalArgumentException("学生不存在"));

            int grade = Integer.parseInt(use.getGrade());
            String stage = GradeEnum.toStage(grade);
            if(textbookDto.getGrade() != null){
                textbook.setGrade(textbookDto.getGrade());
            }else {
                textbook.setGrade(grade);
            }
            if(textbookDto.getSemester() != null) {
                textbook.setSemester(textbookDto.getSemester());
            }else {
                textbook.setSemester(use.getSemester());
            }
            if(StrUtil.isNotEmpty(textbookDto.getStage())) {
                textbook.setStage(stage);
            }else {
                textbook.setStage(stage);
            }
        }

        String textbookId = textbookManagerService.storeTextbook(textbook);
        if (StringUtils.isNotEmpty(textbookDto.getStudentId())) {
            Textbook textbook1 = this.getById(textbookId);
            StudentTextbookProgress progress = Optional.ofNullable(studentTextbookProgressService.getOne(new QueryWrapper<StudentTextbookProgress>().lambda()
                    .eq(StudentTextbookProgress::getStudentId, textbookDto.getStudentId())
                    .eq(StudentTextbookProgress::getTextbookId, textbookId)
                    .last("limit 1"))).orElse(new StudentTextbookProgress());
            String status = StringUtils.isBlank(progress.getId()) ? StudentTextbookProgressStatusEnum.WAIT_START.getValue() : progress.getStatus();
            progress.setTextbookId(textbookId)
                    .setStudentId(textbookDto.getStudentId())
                    .setStatus(status)
                    .setStatWordCnt(textbook1.getStatWordCnt());

            studentTextbookProgressService.saveOrUpdate(progress);

        }

        return AjaxResult.success(textbookId);
    }

    public List<TextbookTreeVo> search(TextbookItemTreeQueryDto searchDto) {
        if (StringUtils.isNotBlank(searchDto.getStudentId()) && Objects.equals(searchDto.getNodeType(), TextBookNodeEnum.TEXT_BOOK.getValue())) {
            LastWordDto lastWordDto = getProgressByStudentIdAndBookId(searchDto.getStudentId(), searchDto.getNodeId());
            return lastWordDto.getTextbookTreeVos();
        }

        return treeSearch(searchDto);
    }

    public List<TextbookTreeVo> treeSearch(TextbookItemTreeQueryDto searchDto) {
        Long first = System.currentTimeMillis();
        TextBookNodeEnum textBookNodeEnum = TextBookNodeEnum.getByValue(searchDto.getNodeType());

        List<TextbookTreeVo> resultList = new LinkedList<>();

        //初始化加载页面
        if(textBookNodeEnum == TextBookNodeEnum.NULL){
            List<Textbook> textbookList = this.lambdaQuery()
                    .in(CollectionUtil.isNotEmpty(searchDto.getTags()),Textbook::getName,searchDto.getTags())
                    .in(CollectionUtil.isNotEmpty(searchDto.getTypes()),Textbook::getType,searchDto.getTypes())
                    .orderByAsc(Textbook::getName)
                    .list();
            textbookList.forEach(item -> {
                resultList.add(new TextbookTreeVo()
                        .setNodeId(item.getId())
                        .setNodeName(item.getName())
                        .setNodeType(TextBookNodeEnum.TEXT_BOOK.getValue())
                        .setTextbookType(item.getType())
                        .setParentNodeId("")
                        .setTextbookId(item.getId())
                        .setTextbookName(item.getName()));
            });
        }else if (textBookNodeEnum == TextBookNodeEnum.TEXT_BOOK) {
            List<TextbookItemTreeoDto> textbookItemInfoDtos = textbookItemService.getTreeItemList(searchDto.getNodeId(),searchDto.getStudentId());
//            Textbook textbook = this.getById(searchDto.getNodeId());
//            //点击树节点,获取学生单词学习进度
//            List<TextbookItem> textbookItems = textbookItemService.lambdaQuery()
//                    .eq(TextbookItem::getTextbookId,searchDto.getNodeId())
//                    .list();
//
            List<TextbookItemTreeoDto> targetItemList = textbookItemInfoDtos.stream().filter(item -> item.getNodeType() == TextBookNodeEnum.UNIT.getValue()).sorted(Comparator.comparing(TextbookItemTreeoDto::getDisplayOrder)).toList();

            //只有单词
            if(CollectionUtil.isEmpty(targetItemList)){
                targetItemList = textbookItemInfoDtos.stream().filter(item -> item.getNodeType() == TextBookNodeEnum.WORD.getValue()).sorted(Comparator.comparing(TextbookItemTreeoDto::getDisplayOrder)).toList();
//                Map<String, StudentWordProgress> student2WordMap = getStudent2Map(targetItemList, searchDto.getStudentId());
//                Map<String,Word> wordMap = wordService.lambdaQuery().select(Word::getId,Word::getWord).in(Word::getId,targetItemList.stream().map(TextbookItem::getWordId).toList()).list().stream().collect(Collectors.toMap(Word::getId, Function.identity()));
                targetItemList.forEach(item -> {
//                    StudentWordProgress studentWordProgress = student2WordMap.get(item.getId());
                    resultList.add(new TextbookTreeVo()
                            .setNodeId(item.getId())
                            .setNodeName(item.getWord())
                            .setNodeType(TextBookNodeEnum.WORD.getValue())
                            .setTextbookType(item.getBookType())
                            .setParentNodeId(item.getPid())
                            .setTextbookId(item.getTextbookId())
                            .setDisplayOrder(item.getDisplayOrder())
                            .setWordId(item.getWordId())
                            .setLearnStatus(StringUtils.isNotBlank(item.getStudentWordItemId()))
                            .setMistakes(item.getProgressStatus()));
                });
            }else{
                List<TextbookItemTreeoDto> wordItemList = textbookItemInfoDtos.stream().filter(item -> item.getNodeType() == TextBookNodeEnum.WORD.getValue()).sorted(Comparator.comparing(TextbookItemTreeoDto::getDisplayOrder)).toList();
                Map<String,Long> unitCntMap = wordItemList.stream().collect(Collectors.groupingBy(TextbookItemTreeoDto::getPid,Collectors.counting()));

                //有单元
                targetItemList.forEach(item -> {
                    long wordNum = 0;
                    if (COURSE_TYPE.equals(searchDto.getSearchType())) {
//                        wordNum = textbookItemService.count(new QueryWrapper<TextbookItem>().lambda().eq(TextbookItem::getPid, item.getId()));
                        wordNum = unitCntMap.get(item.getId());
                    }
                    resultList.add(new TextbookTreeVo()
                            .setNodeId(item.getId())
                            .setNodeName(item.getUnitName())
                            .setNodeType(TextBookNodeEnum.UNIT.getValue())
                            .setTextbookType(item.getBookType())
                            .setParentNodeId(item.getTextbookId())
                            .setTextbookId(item.getTextbookId())
                            .setDisplayOrder(item.getDisplayOrder())
                            .setWordNum(wordNum));
                });
            }

            return resultList;
        } else if (textBookNodeEnum == TextBookNodeEnum.UNIT) {
            List<TextbookItemTreeoDto> textbookItemInfoDtos = textbookItemService.getTreeItemListByUnitId(searchDto.getNodeId(),searchDto.getStudentId());

            //点击单元节点,获取学生单词学习进度
//            List<TextbookItem> textbookItems = textbookItemService.lambdaQuery()
//                    .eq(TextbookItem::getPid,searchDto.getNodeId())
//                    .orderByAsc(TextbookItem::getDisplayOrder)
//                    .list();

//            Textbook textbook = this.getById(textbookItems.getFirst().getTextbookId());

//            Map<String,Word> wordMap = wordService.lambdaQuery().in(Word::getId,textbookItems.stream().map(TextbookItem::getWordId).toList()).list().stream().collect(Collectors.toMap(Word::getId, Function.identity()));
            List<TextbookItemTreeoDto> targetItemList = textbookItemInfoDtos.stream().filter(item -> item.getNodeType() == TextBookNodeEnum.WORD.getValue()).sorted(Comparator.comparing(TextbookItemTreeoDto::getDisplayOrder)).toList();
//                Map<String, StudentWordProgress> student2WordMap = getStudent2Map(targetItemList, searchDto.getStudentId());
//                Map<String,Word> wordMap = wordService.lambdaQuery().select(Word::getId,Word::getWord).in(Word::getId,targetItemList.stream().map(TextbookItem::getWordId).toList()).list().stream().collect(Collectors.toMap(Word::getId, Function.identity()));
            targetItemList.forEach(item -> {
//                    StudentWordProgress studentWordProgress = student2WordMap.get(item.getId());
                resultList.add(new TextbookTreeVo()
                        .setNodeId(item.getId())
                        .setNodeName(item.getWord())
                        .setNodeType(TextBookNodeEnum.WORD.getValue())
                        .setTextbookType(item.getBookType())
                        .setParentNodeId(item.getPid())
                        .setTextbookId(item.getTextbookId())
                        .setDisplayOrder(item.getDisplayOrder())
                        .setWordId(item.getWordId())
                        .setLearnStatus(StringUtils.isNotBlank(item.getStudentWordItemId()))
                        .setMistakes(item.getProgressStatus()));
            });
            return resultList;
        }
        Long second = System.currentTimeMillis();

        //上课页面，查询学生最后学过的单词
//        if (StringUtils.isNotBlank(searchDto.getStudentId())) {
//            LastWordDto lastWordDto = getProgressByStudentIdAndBookId(searchDto.getStudentId(), null);
//            if (StringUtils.isNotBlank(lastWordDto.getWordItemId())) {
//                Optional<TextbookTreeVo> optional = resultList.stream().filter(item -> item.getTextbookId().equals(lastWordDto.getTextbookId())).findFirst();
//                optional.ifPresent(item -> {
//                    item.setChildNodeList(lastWordDto.getTextbookTreeVos());
//                    item.setLastUnitItemId(lastWordDto.getUnitItemId());
//                    item.setLastWordItemId(lastWordDto.getWordItemId());
//                });
//            }
//        }

        Long third = System.currentTimeMillis();

        log.info("查询教材树耗时:{}，查询最后学习单词耗时： {}",(second - first),(third - second));

        return resultList;
    }

    private Map<String, StudentWordProgress> getStudent2Map(List<TextbookItem> textbookItemList, String studentId) {
        if (StringUtils.isNotBlank(studentId)) {
            List<String> textbookItemIds = textbookItemList.stream().filter(item -> item.getNodeType() == TextBookNodeEnum.WORD.getValue()).map(TextbookItem::getId).toList();
            List<StudentWordProgress> studiedWordList = studentWordProgressService.list(new QueryWrapper<StudentWordProgress>().lambda()
                    .in(StudentWordProgress::getTextbookItemId, textbookItemIds)
                    .eq(StudentWordProgress::getStudentId, studentId));
            return studiedWordList.stream().collect(Collectors.toMap(StudentWordProgress::getTextbookItemId, Function.identity()));
        }
        return new HashMap<>();
    }

    public List<Textbook> getTextbookList() {
        return list(new QueryWrapper<Textbook>().lambda()
                .eq(Textbook::getDeleted, false));
    }

    /**
     * 删除词典
     * - 删除textbook表数据（逻辑删除）
     * - 添加history_textbook表数据
     * - 删除textbook_item表数据（逻辑删除）
     *
     * @param ids
     */
        public void removeTextbooks(List<String> ids) {
            log.info("删除词典：{}", ids);
            // 获取到这些词典数据
            List<Textbook> textbooks = list(new QueryWrapper<Textbook>().lambda()
                    .in(Textbook::getId, ids)
                    .eq(Textbook::getDeleted, false)
            );

            if (CollUtil.isEmpty(textbooks)) {
                return;
            }

            // 组装历史数据
            List<HistoryTextbook> historyTextbooks = textbooks.stream().map(textbook -> {
                HistoryTextbook historyTextbook = new HistoryTextbook();
                historyTextbook.setTextbookId(textbook.getId());
                historyTextbook.setTextbookObj(JSONUtil.toJsonStr(textbook));
                historyTextbook.setTextbookItemsObj(JSONUtil.toJsonStr(textbookItemService.list(new QueryWrapper<TextbookItem>().lambda()
                        .eq(TextbookItem::getTextbookId, textbook.getId())
                        .eq(TextbookItem::getDeleted, false)
                )));
                historyTextbook.setVersion(textbook.getVersion());
                historyTextbook.setUpdateTime(new Date());
                historyTextbook.setChangeDescription("删除词典");
                return historyTextbook;
            }).toList();

            //
            historyTextbookService.saveBatch(historyTextbooks);
            // 删除textbook表数据
            this.removeBatchByIds(ids);
            // 删除textbook_item表数据
//            textbookItemService.remove(new QueryWrapper<TextbookItem>().lambda()
//                    .in(TextbookItem::getTextbookId, ids)
//                    .eq(TextbookItem::getDeleted, false)
//            );

            textbookItemService.lambdaUpdate()
                    .set(TextbookItem::getDeleted, true)
                    .set(TextbookItem::getUpdateBy, WssContext.userId())
                    .set(TextbookItem::getUpdateTime, WssContext.now())
                    .eq(TextbookItem::getDeleted, false)
                    .in(TextbookItem::getTextbookId, ids);
        }

    /**
     * 查询学生某本教材的最后学习单词
     *
     * @param studentId
     * @param textbookId
     * @return
     */
    public LastWordDto getProgressByStudentIdAndBookId(String studentId, String textbookId) {
        LastWordDto lastWordDto = new LastWordDto();
        StudentWordProgress studentWordProgresses = studentWordProgressService.getByNotDelBook(studentId,textbookId);
//        if(textbookId == null){
//            studentWordProgresses = studentWordProgressService.getByNotDelBook(studentId,textbookId);
//        }else{
//            studentWordProgresses = studentWordProgressService.getOne(new QueryWrapper<StudentWordProgress>().lambda()
//                    .eq(StringUtils.isNotBlank(studentId), StudentWordProgress::getStudentId, studentId)
//                    .eq(StringUtils.isNotBlank(textbookId), StudentWordProgress::getTextbookId, textbookId)
//                    .orderByDesc(StudentWordProgress::getCreateTime)
//                    .last("limit 1"));
//        }

        if (Objects.isNull(studentWordProgresses)) {
            if (StringUtils.isNotBlank(textbookId)) {
                List<TextbookTreeVo> unitTreeVos1 = treeSearch(new TextbookItemTreeQueryDto().setNodeId(textbookId).setNodeType(TextBookNodeEnum.TEXT_BOOK.getValue()).setStudentId((studentId)).setSearchType("上课"));
                lastWordDto.setTextbookTreeVos(unitTreeVos1)
                        .setTextbookId(textbookId);
            }
            return lastWordDto;
        }

        if (textbookId == null) {
            textbookId = studentWordProgresses.getTextbookId();
        }

        List<TextbookTreeVo> unitTreeVos = treeSearch(new TextbookItemTreeQueryDto().setNodeId(textbookId).setNodeType(TextBookNodeEnum.TEXT_BOOK.getValue()).setStudentId((studentId)).setSearchType("上课"));


        //取已学单词的单元下的所有同级单词
        lastWordDto.setWordItemId(studentWordProgresses.getTextbookItemId());
        TextbookItem textbookItem = textbookItemService.getById(studentWordProgresses.getTextbookItemId());

        //取单元下的单词
        if (!Objects.equals(textbookItem.getPid(), "0")) {
            List<TextbookTreeVo> wordTreeVos = treeSearch(new TextbookItemTreeQueryDto()
                    .setNodeId(textbookItem.getPid())
                    .setNodeType(TextBookNodeEnum.UNIT.getValue())
                    .setSearchType("上课")
                    .setStudentId(studentId));
            Optional<TextbookTreeVo> unitTreeVoOption = unitTreeVos.stream().filter(item -> textbookItem.getPid().equals(item.getNodeId())).findFirst();
            if (unitTreeVoOption.isPresent()) {
                TextbookTreeVo unitTreeVo = unitTreeVoOption.get();
                unitTreeVo.getChildNodeList().addAll(wordTreeVos);

                lastWordDto.setUnitItemId(unitTreeVoOption.get().getNodeId());
            }
        }

        lastWordDto.setTextbookTreeVos(unitTreeVos)
                .setTextbookId(textbookId);

        return lastWordDto;
    }

    public List<Textbook> listAll() {
        return lambdaQuery().select(Textbook::getId, Textbook::getName, Textbook::getStatWordCnt).orderByAsc(Textbook::getName).list();
    }
}
