package org.nonamespace.word.server.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.constants.OrderConstants;
import org.nonamespace.word.server.domain.Product;
import org.nonamespace.word.server.domain.StudentCourseHours;
import org.nonamespace.word.server.domain.StudentCourseHoursRelease;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.domain.order.OrdersTrx;
import org.nonamespace.word.server.dto.course.CourseHoursReleaseResult;
import org.nonamespace.word.server.dto.course.CourseHoursRefundCalculationResult;
import org.nonamespace.word.server.exception.CourseHoursReleaseException;
import org.nonamespace.word.server.mapper.StudentCourseHoursReleaseMapper;
import org.nonamespace.word.server.service.ICourseHoursReleaseService;
import org.nonamespace.word.server.service.IStudentCourseHoursService;
import org.nonamespace.word.server.service.IStudentCourseHoursAdjustmentService;
import org.nonamespace.word.server.service.order.IOrdersService;
import org.nonamespace.word.server.service.order.IOrdersTrxService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 学生课时释放服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CourseHoursReleaseServiceImpl extends ServiceImpl<StudentCourseHoursReleaseMapper, StudentCourseHoursRelease> 
        implements ICourseHoursReleaseService {

    private final IOrdersService ordersService;
    private final IOrdersTrxService ordersTrxService;
    private final IStudentCourseHoursService studentCourseHoursService;
    private final IStudentCourseHoursAdjustmentService adjustmentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CourseHoursReleaseResult refreshCourseHoursRelease(String orderNo) {
        log.info("[课时释放] 开始处理订单课时释放, 订单号: {}", orderNo);
        
        try {
            // 1. 查询订单信息
            Orders order = ordersService.getByOrderNo(orderNo);
            if (order == null) {
                log.warn("[课时释放] 订单不存在, 订单号: {}", orderNo);
                return CourseHoursReleaseResult.failure("订单不存在");
            }

            // 2. 查询产品信息
            Product product = order.getProducts();
            if (product == null) {
                log.warn("[课时释放] 产品信息不存在, 订单号: {}", orderNo);
                return CourseHoursReleaseResult.failure("产品信息不存在");
            }

            // 3. 查询已支付的流水
            List<OrdersTrx> paidTrxList = ordersTrxService.lambdaQuery()
                    .eq(OrdersTrx::getOrderId, order.getId())
                    .eq(OrdersTrx::getTrxStatus, OrderConstants.OrderStatus.PAID)
                    .orderByAsc(OrdersTrx::getTrxIdx)
                    .list();

            if (paidTrxList.isEmpty()) {
                log.info("[课时释放] 订单暂无已支付流水, 订单号: {}", orderNo);
                return CourseHoursReleaseResult.noNeedProcess("订单暂无已支付流水");
            }

            // 4. 查询或创建课时包（失败时会抛出异常，自动回滚事务）
            StudentCourseHours courseHours = getOrCreateCourseHours(order, product);

            // 5. 查询已有的释放记录
            List<StudentCourseHoursRelease> existingReleases = this.lambdaQuery()
                    .eq(StudentCourseHoursRelease::getOrderId, order.getId())
                    .list();

            // 6. 处理课时释放
            return processReleases(order, product, courseHours, paidTrxList, existingReleases);

        } catch (Exception e) {
            log.error("[课时释放] 处理异常, 订单号: {}", orderNo, e);
            return CourseHoursReleaseResult.failure("课时释放处理异常: " + e.getMessage());
        }
    }

    @Override
    public boolean needsReleaseCheck(String orderNo) {
        Orders order = ordersService.getByOrderNo(orderNo);
        if (order == null) {
            return false;
        }

        // 检查是否有已支付但未释放的流水
        List<OrdersTrx> paidTrxList = ordersTrxService.lambdaQuery()
                .eq(OrdersTrx::getOrderId, order.getId())
                .eq(OrdersTrx::getTrxStatus, OrderConstants.OrderStatus.PAID)
                .list();

        if (paidTrxList.isEmpty()) {
            return false;
        }

        List<StudentCourseHoursRelease> existingReleases = this.lambdaQuery()
                .eq(StudentCourseHoursRelease::getOrderId, order.getId())
                .list();

        Set<String> processedTrxIds = existingReleases.stream()
                .map(StudentCourseHoursRelease::getOrderTrxId)
                .collect(Collectors.toSet());

        return paidTrxList.stream().anyMatch(trx -> !processedTrxIds.contains(trx.getId()));
    }

    @Override
    public CourseHoursRefundCalculationResult calculateRefundData(String orderNo) {
        log.info("[退款计算] 开始计算订单退款数据, 订单号: {}", orderNo);

        try {
            // 1. 查询订单信息
            Orders order = ordersService.getByOrderNo(orderNo);
            if (order == null) {
                log.warn("[退款计算] 订单不存在, 订单号: {}", orderNo);
                return CourseHoursRefundCalculationResult.failure("订单不存在");
            }

            // 2. 查询课时包
            StudentCourseHours courseHours = studentCourseHoursService.lambdaQuery()
                    .eq(StudentCourseHours::getOrderId, order.getId())
                    .one();

            if (courseHours == null) {
                log.info("[退款计算] 订单暂无课时包, 订单号: {}", orderNo);
                return CourseHoursRefundCalculationResult.noCourseHours(orderNo);
            }

            // 3. 查询已支付的流水
            List<OrdersTrx> paidTrxList = ordersTrxService.lambdaQuery()
                    .eq(OrdersTrx::getOrderId, order.getId())
                    .eq(OrdersTrx::getTrxStatus, OrderConstants.OrderStatus.PAID)
                    .orderByAsc(OrdersTrx::getTrxIdx)
                    .list();

            // 4. 查询课时释放记录
            List<StudentCourseHoursRelease> releaseRecords = this.lambdaQuery()
                    .eq(StudentCourseHoursRelease::getOrderId, order.getId())
                    .orderByAsc(StudentCourseHoursRelease::getCreateTime)
                    .list();

            // 5. 计算退款数据
            return buildRefundCalculationResult(order, courseHours, paidTrxList, releaseRecords);

        } catch (Exception e) {
            log.error("[退款计算] 计算异常, 订单号: {}", orderNo, e);
            return CourseHoursRefundCalculationResult.failure("退款数据计算异常: " + e.getMessage());
        }
    }

    /**
     * 获取或创建课时包
     */
    private StudentCourseHours getOrCreateCourseHours(Orders order, Product product) {
        // 先查询是否已存在课时包
        StudentCourseHours existing = studentCourseHoursService.lambdaQuery()
                .eq(StudentCourseHours::getOrderId, order.getId())
                .one();

        if (existing != null) {
            return existing;
        }

        // 创建新的课时包（冻结状态）
        return createFrozenCourseHours(order, product);
    }

    /**
     * 创建冻结状态的课时包
     */
    private StudentCourseHours createFrozenCourseHours(Orders order, Product product) {
        log.info("[课时释放] 创建冻结课时包, 订单号: {}, 产品: {}", order.getNo(), product.getName());
        
        StudentCourseHours courseHours = new StudentCourseHours();
        courseHours.setId(IdUtil.getSnowflakeNextIdStr());
        courseHours.setStudentId(order.getStudentId());
        courseHours.setSubject(product.getSubject());
        courseHours.setSpecification(product.getCourseType());
        courseHours.setNature("正式课");
        courseHours.setBatchNo("ORDER_" + order.getNo());
        courseHours.setUnitPrice(new BigDecimal(product.getUnitPrice()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        courseHours.setOrderTime(WssContext.now());
        
        // 设置课时数量
        BigDecimal purchasedHours = new BigDecimal(product.getQuantity());
        BigDecimal giftHours = product.getHasBonusHours() && product.getBonusHoursQuantity() > 0 
                ? new BigDecimal(product.getBonusHoursQuantity()) : BigDecimal.ZERO;
        
        courseHours.setPurchasedHours(purchasedHours);
        courseHours.setGiftHours(giftHours);
        courseHours.setTotalHours(purchasedHours.add(giftHours));
        
        // 初始状态：所有课时冻结，剩余课时为0
        courseHours.setFrozenPurchasedHours(purchasedHours);
        courseHours.setFrozenGiftHours(giftHours);
        courseHours.setRemainingHours(BigDecimal.ZERO);
        
        // 设置消耗课时为0
        courseHours.setConsumedTotalHours(BigDecimal.ZERO);
        courseHours.setConsumedPurchasedHours(BigDecimal.ZERO);
        courseHours.setConsumedGiftHours(BigDecimal.ZERO);
        
        // 设置订单关联和来源
        courseHours.setOrderId(order.getId());
        courseHours.setSourceType("订单");
        courseHours.setStatus("active");
        
        courseHours.setCreateTime(WssContext.now());
        courseHours.setUpdateTime(WssContext.now());
        courseHours.setDeleted(false);

        boolean saved = studentCourseHoursService.save(courseHours);
        if (!saved) {
            String errorMsg = String.format("课时包保存失败, 订单号: %s", order.getNo());
            log.error("[课时释放] {}", errorMsg);
            throw new CourseHoursReleaseException(errorMsg);
        }

        log.info("[课时释放] 课时包创建成功, 课时包ID: {}, 购买课时: {}, 赠送课时: {}",
                courseHours.getId(), purchasedHours, giftHours);
        return courseHours;
    }

    /**
     * 处理课时释放
     */
    private CourseHoursReleaseResult processReleases(Orders order, Product product, StudentCourseHours courseHours,
                                                     List<OrdersTrx> paidTrxList, List<StudentCourseHoursRelease> existingReleases) {

        Set<String> processedTrxIds = existingReleases.stream()
                .map(StudentCourseHoursRelease::getOrderTrxId)
                .collect(Collectors.toSet());

        List<CourseHoursReleaseResult.ReleaseDetail> details = new ArrayList<>();
        BigDecimal totalReleasedPurchased = BigDecimal.ZERO;
        BigDecimal totalReleasedGift = BigDecimal.ZERO;

        // 计算总支付金额
        long totalPaidAmount = paidTrxList.stream()
                .mapToLong(OrdersTrx::getTrxAmt)
                .sum();

        boolean isFullyPaid = totalPaidAmount >= order.getTotalAmt();

        for (OrdersTrx trx : paidTrxList) {
            if (processedTrxIds.contains(trx.getId())) {
                continue; // 已处理过，跳过
            }

            // 计算当前支付的课时释放量
            ReleaseCalculation calculation = calculateRelease(order, product, trx, isFullyPaid);

            // 释放课时（失败时会抛出异常，自动回滚事务）
            releaseCourseHours(courseHours, calculation);

            // 记录释放历史（失败时会抛出异常，自动回滚事务）
            StudentCourseHoursRelease releaseRecord = recordRelease(order, trx, courseHours, calculation);

            CourseHoursReleaseResult.ReleaseDetail detail = CourseHoursReleaseResult.ReleaseDetail.builder()
                    .courseHoursId(courseHours.getId())
                    .orderTrxId(trx.getId())
                    .releasedPurchasedHours(calculation.getReleasedPurchasedHours())
                    .releasedGiftHours(calculation.getReleasedGiftHours())
                    .paymentRatio(calculation.getPaymentRatio())
                    .releaseType(calculation.getReleaseType())
                    .build();

            details.add(detail);
            totalReleasedPurchased = totalReleasedPurchased.add(calculation.getReleasedPurchasedHours());
            totalReleasedGift = totalReleasedGift.add(calculation.getReleasedGiftHours());
        }

        if (details.isEmpty()) {
            return CourseHoursReleaseResult.noNeedProcess("所有支付流水已处理完成");
        }

        log.info("[课时释放] 处理完成, 订单号: {}, 释放购买课时: {}, 释放赠送课时: {}",
                order.getNo(), totalReleasedPurchased, totalReleasedGift);

        return CourseHoursReleaseResult.success(details, totalReleasedPurchased, totalReleasedGift);
    }

    /**
     * 计算课时释放量
     */
    private ReleaseCalculation calculateRelease(Orders order, Product product, OrdersTrx trx, boolean isFullyPaid) {
        BigDecimal paymentRatio = new BigDecimal(trx.getTrxAmt())
                .divide(new BigDecimal(order.getTotalAmt()), 4, RoundingMode.HALF_UP);

        // 购买课时按支付比例释放
        BigDecimal totalPurchasedHours = new BigDecimal(product.getQuantity());
        BigDecimal releasedPurchasedHours = totalPurchasedHours
                .multiply(paymentRatio)
                .setScale(2, RoundingMode.HALF_UP);

        // 赠送课时只在全额支付时释放
        BigDecimal releasedGiftHours = BigDecimal.ZERO;
        String releaseType = "部分释放";

        if (isFullyPaid && product.getHasBonusHours() && product.getBonusHoursQuantity() > 0) {
            releasedGiftHours = new BigDecimal(product.getBonusHoursQuantity());
            releaseType = "最终释放";
        }

        return ReleaseCalculation.builder()
                .releasedPurchasedHours(releasedPurchasedHours)
                .releasedGiftHours(releasedGiftHours)
                .paymentRatio(paymentRatio)
                .releaseType(releaseType)
                .build();
    }

    /**
     * 释放课时
     */
    private void releaseCourseHours(StudentCourseHours courseHours, ReleaseCalculation calculation) {
        // 检查冻结课时是否足够
        if (courseHours.getFrozenPurchasedHours().compareTo(calculation.getReleasedPurchasedHours()) < 0) {
            String errorMsg = String.format("冻结购买课时不足, 课时包ID: %s, 冻结: %s, 需要释放: %s",
                    courseHours.getId(), courseHours.getFrozenPurchasedHours(), calculation.getReleasedPurchasedHours());
            log.error("[课时释放] {}", errorMsg);
            throw new CourseHoursReleaseException(errorMsg);
        }

        if (courseHours.getFrozenGiftHours().compareTo(calculation.getReleasedGiftHours()) < 0) {
            String errorMsg = String.format("冻结赠送课时不足, 课时包ID: %s, 冻结: %s, 需要释放: %s",
                    courseHours.getId(), courseHours.getFrozenGiftHours(), calculation.getReleasedGiftHours());
            log.error("[课时释放] {}", errorMsg);
            throw new CourseHoursReleaseException(errorMsg);
        }

        // 记录调整前的状态
        BigDecimal beforeFrozenPurchased = courseHours.getFrozenPurchasedHours();
        BigDecimal beforeFrozenGift = courseHours.getFrozenGiftHours();
        BigDecimal beforeRemaining = courseHours.getRemainingHours();

        // 更新课时包状态
        courseHours.setFrozenPurchasedHours(courseHours.getFrozenPurchasedHours().subtract(calculation.getReleasedPurchasedHours()));
        courseHours.setFrozenGiftHours(courseHours.getFrozenGiftHours().subtract(calculation.getReleasedGiftHours()));

        BigDecimal totalReleased = calculation.getReleasedPurchasedHours().add(calculation.getReleasedGiftHours());
        courseHours.setRemainingHours(courseHours.getRemainingHours().add(totalReleased));

        courseHours.setUpdateTime(WssContext.now());

        boolean updated = studentCourseHoursService.updateById(courseHours);
        if (!updated) {
            String errorMsg = String.format("课时包更新失败, 课时包ID: %s", courseHours.getId());
            log.error("[课时释放] {}", errorMsg);
            throw new CourseHoursReleaseException(errorMsg);
        }

        // 记录课时包调整历史
        recordCourseHoursAdjustment(courseHours, calculation, beforeFrozenPurchased, beforeFrozenGift, beforeRemaining);

        log.info("[课时释放] 课时释放成功, 课时包ID: {}, 释放购买课时: {}, 释放赠送课时: {}, 剩余课时: {}",
                courseHours.getId(), calculation.getReleasedPurchasedHours(), calculation.getReleasedGiftHours(), courseHours.getRemainingHours());
    }

    /**
     * 记录释放历史
     */
    private StudentCourseHoursRelease recordRelease(Orders order, OrdersTrx trx, StudentCourseHours courseHours, ReleaseCalculation calculation) {
        StudentCourseHoursRelease releaseRecord = new StudentCourseHoursRelease();
        releaseRecord.setId(IdUtil.getSnowflakeNextIdStr());
        releaseRecord.setCourseHoursId(courseHours.getId());
        releaseRecord.setOrderId(order.getId());
        releaseRecord.setOrderTrxId(trx.getId());
        releaseRecord.setStudentId(order.getStudentId());
        releaseRecord.setReleasedPurchasedHours(calculation.getReleasedPurchasedHours());
        releaseRecord.setReleasedGiftHours(calculation.getReleasedGiftHours());
        releaseRecord.setPaymentRatio(calculation.getPaymentRatio());
        releaseRecord.setPaymentAmount(trx.getTrxAmt());
        releaseRecord.setTotalOrderAmount(order.getTotalAmt());
        releaseRecord.setReleaseTime(WssContext.now());
        releaseRecord.setReleaseType(calculation.getReleaseType());
        releaseRecord.setRemark(String.format("订单%s第%d期支付释放课时", order.getNo(), trx.getTrxIdx()));

        releaseRecord.setCreateTime(WssContext.now());
        releaseRecord.setUpdateTime(WssContext.now());
        releaseRecord.setDeleted(false);

        boolean saved = this.save(releaseRecord);
        if (!saved) {
            String errorMsg = String.format("释放记录保存失败, 订单号: %s, 流水ID: %s", order.getNo(), trx.getId());
            log.error("[课时释放] {}", errorMsg);
            throw new CourseHoursReleaseException(errorMsg);
        }

        return releaseRecord;
    }

    /**
     * 记录课时包调整历史
     */
    private void recordCourseHoursAdjustment(StudentCourseHours courseHours, ReleaseCalculation calculation,
                                           BigDecimal beforeFrozenPurchased, BigDecimal beforeFrozenGift, BigDecimal beforeRemaining) {
        try {
            // 计算调整后的状态
            BigDecimal afterFrozenPurchased = courseHours.getFrozenPurchasedHours();
            BigDecimal afterFrozenGift = courseHours.getFrozenGiftHours();
            BigDecimal afterRemaining = courseHours.getRemainingHours();

            // 调整原因
            String adjustmentReason = String.format("订单支付释放课时 - %s，释放购买课时%.2f，释放赠送课时%.2f",
                    calculation.getReleaseType(),
                    calculation.getReleasedPurchasedHours(),
                    calculation.getReleasedGiftHours());

            // 记录调整历史 - 这里记录的是冻结课时的变化
            adjustmentService.recordAdjustment(
                    courseHours.getStudentId(),
                    courseHours.getSubject(),
                    courseHours.getSpecification(),
                    courseHours.getNature(),
                    "decrease", // 冻结课时减少
                    calculation.getReleasedPurchasedHours().add(calculation.getReleasedGiftHours()), // 总调整课时
                    calculation.getReleasedPurchasedHours().negate(), // 购买课时调整（冻结减少，所以是负数）
                    calculation.getReleasedGiftHours().negate(), // 赠送课时调整（冻结减少，所以是负数）
                    courseHours.getTotalHours(), // 调整前总课时（总课时不变）
                    courseHours.getTotalHours(), // 调整后总课时（总课时不变）
                    beforeRemaining, // 调整前剩余课时
                    afterRemaining, // 调整后剩余课时
                    courseHours.getPurchasedHours(), // 调整前购买课时（购买课时总数不变）
                    courseHours.getPurchasedHours(), // 调整后购买课时（购买课时总数不变）
                    courseHours.getGiftHours(), // 调整前赠送课时（赠送课时总数不变）
                    courseHours.getGiftHours(), // 调整后赠送课时（赠送课时总数不变）
                    adjustmentReason,
                    "system", // 系统操作
                    "课时释放服务"
            );

            log.info("[课时释放] 课时包调整历史记录成功, 课时包ID: {}, 释放购买课时: {}, 释放赠送课时: {}",
                    courseHours.getId(), calculation.getReleasedPurchasedHours(), calculation.getReleasedGiftHours());

        } catch (Exception e) {
            log.error("[课时释放] 记录课时包调整历史失败, 课时包ID: {}", courseHours.getId(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 释放计算结果
     */
    @lombok.Data
    @lombok.Builder
    private static class ReleaseCalculation {
        private BigDecimal releasedPurchasedHours;
        private BigDecimal releasedGiftHours;
        private BigDecimal paymentRatio;
        private String releaseType;
    }
}
