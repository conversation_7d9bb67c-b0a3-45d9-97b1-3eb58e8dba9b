package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.StudentCourseHoursRelease;
import org.nonamespace.word.server.dto.course.CourseHoursReleaseResult;

/**
 * 学生课时释放服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
public interface ICourseHoursReleaseService extends IService<StudentCourseHoursRelease> {

    /**
     * 刷新订单课时释放状态
     * @param orderNo 订单号
     * @return 释放结果
     */
    CourseHoursReleaseResult refreshCourseHoursRelease(String orderNo);

    /**
     * 检查订单是否需要释放课时
     * @param orderNo 订单号
     * @return 是否需要释放
     */
    boolean needsReleaseCheck(String orderNo);
}
