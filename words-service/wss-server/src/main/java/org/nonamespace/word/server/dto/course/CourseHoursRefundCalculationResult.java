package org.nonamespace.word.server.dto.course;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课时退款计算结果DTO
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@Builder
public class CourseHoursRefundCalculationResult {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 消息
     */
    private String message;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 课时包ID
     */
    private String courseHoursId;

    /**
     * 订单总金额(分)
     */
    private Long totalOrderAmount;

    /**
     * 已支付总金额(分)
     */
    private Long totalPaidAmount;

    /**
     * 课时包总课时数
     */
    private BigDecimal totalCourseHours;

    /**
     * 已释放总课时数
     */
    private BigDecimal totalReleasedHours;

    /**
     * 剩余可用课时数
     */
    private BigDecimal remainingHours;

    /**
     * 已消耗课时数
     */
    private BigDecimal consumedHours;

    /**
     * 可退款课时数（剩余课时 - 已消耗课时）
     */
    private BigDecimal refundableHours;

    /**
     * 支付流水释放详情列表
     */
    private List<PaymentReleaseDetail> paymentReleaseDetails;

    /**
     * 支付流水释放详情
     */
    @Data
    @Builder
    public static class PaymentReleaseDetail {
        /**
         * 支付流水ID
         */
        private String orderTrxId;

        /**
         * 支付流水序号
         */
        private Integer trxIdx;

        /**
         * 支付金额(分)
         */
        private Long paymentAmount;

        /**
         * 支付比例
         */
        private BigDecimal paymentRatio;

        /**
         * 释放的购买课时数
         */
        private BigDecimal releasedPurchasedHours;

        /**
         * 释放的赠送课时数
         */
        private BigDecimal releasedGiftHours;

        /**
         * 释放的总课时数
         */
        private BigDecimal totalReleasedHours;

        /**
         * 释放类型
         */
        private String releaseType;

        /**
         * 释放时间
         */
        private String releaseTime;

        /**
         * 是否可退款（该笔支付释放的课时是否可以退款）
         */
        private boolean refundable;

        /**
         * 备注
         */
        private String remark;
    }

    /**
     * 创建成功结果
     */
    public static CourseHoursRefundCalculationResult success(String orderNo, String orderId, String courseHoursId,
                                                           Long totalOrderAmount, Long totalPaidAmount,
                                                           BigDecimal totalCourseHours, BigDecimal totalReleasedHours,
                                                           BigDecimal remainingHours, BigDecimal consumedHours,
                                                           List<PaymentReleaseDetail> details) {
        BigDecimal refundableHours = remainingHours.subtract(consumedHours).max(BigDecimal.ZERO);
        
        return CourseHoursRefundCalculationResult.builder()
                .success(true)
                .message("退款数据计算成功")
                .orderNo(orderNo)
                .orderId(orderId)
                .courseHoursId(courseHoursId)
                .totalOrderAmount(totalOrderAmount)
                .totalPaidAmount(totalPaidAmount)
                .totalCourseHours(totalCourseHours)
                .totalReleasedHours(totalReleasedHours)
                .remainingHours(remainingHours)
                .consumedHours(consumedHours)
                .refundableHours(refundableHours)
                .paymentReleaseDetails(details)
                .build();
    }

    /**
     * 创建失败结果
     */
    public static CourseHoursRefundCalculationResult failure(String message) {
        return CourseHoursRefundCalculationResult.builder()
                .success(false)
                .message(message)
                .build();
    }

    /**
     * 创建无课时包结果
     */
    public static CourseHoursRefundCalculationResult noCourseHours(String orderNo) {
        return CourseHoursRefundCalculationResult.builder()
                .success(true)
                .message("订单暂无课时包数据")
                .orderNo(orderNo)
                .build();
    }
}
