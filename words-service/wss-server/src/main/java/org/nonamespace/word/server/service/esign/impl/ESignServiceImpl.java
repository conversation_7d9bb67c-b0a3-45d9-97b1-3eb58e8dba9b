package org.nonamespace.word.server.service.esign.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.utils.AmountConverterUtil;
import org.nonamespace.word.server.constants.OrderConstants;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.model.ESignDocTemplateStructureV1;
import org.nonamespace.word.server.service.IUserService;
import org.nonamespace.word.server.service.esign.IESignService;
import org.nonamespace.word.server.service.order.IOrdersService;
import org.nonamespace.word.thirdpart.esign.config.ESignConfig;
import org.nonamespace.word.thirdpart.esign.domain.EsignTemplateComponents;
import org.nonamespace.word.thirdpart.esign.exception.ESignException;
import org.nonamespace.word.thirdpart.esign.model.*;
import org.nonamespace.word.thirdpart.esign.model.inner.SignFieldPosition;
import org.nonamespace.word.thirdpart.esign.service.IESignContractsFileService;
import org.nonamespace.word.thirdpart.esign.service.IESignFlowService;
import org.nonamespace.word.thirdpart.esign.service.IESignTemplateService;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.text.DecimalFormat;
import java.util.*;


@Slf4j
@Service
@RequiredArgsConstructor
public class ESignServiceImpl implements IESignService {

    private final IESignTemplateService eSignTemplateService;
    private final IESignContractsFileService eSignContractsFileService;
    private final IESignFlowService eSignFlowService;
    private final IOrdersService ordersService;
    private final ESignConfig eSignConfig;
    private final IUserService userService;

    @Override
    public void buildContractsFile(String orderId) throws ESignException {
        // todo... 获取订单信息
        Orders orders = ordersService.getById(orderId);
        Objects.requireNonNull(orders, "该订单不存在，请重新确认订单编号是否有误");

        checkOrderSignStatus(orders);

        // 获取学生信息
        SysUser sysUser = userService.getById(orders.getStudentId());

        //
        // 填充模板文件
        ESignDocTemplateStructureV1 structureV1 = new ESignDocTemplateStructureV1();
        structureV1.setSignDate(DateUtil.format(DateUtil.date(), "yyyy年MM月dd日"));
        structureV1.setStudentName(sysUser.getUserName());
        structureV1.setParentName(sysUser.getUserName());
        structureV1.setParentPhone(sysUser.getPhonenumber());
        structureV1.setCourseName(orders.getBody());
        structureV1.setUpperMoney(AmountConverterUtil.convertToUppercase(orders.getTotalAmt()));
        structureV1.setLowerMoney(AmountConverterUtil.convertToLowercase(orders.getTotalAmt()));
        structureV1.setClassHours(String.valueOf(orders.getProducts().getQuantity()));
        structureV1.setGiftClassHours(String.valueOf(orders.getProducts().getBonusHoursQuantity()));

        // 必须要保留2位小数，不然天王老子来了都会报错
        String formattedPrice = new DecimalFormat("0.00").format(
                Double.parseDouble(String.valueOf(orders.getProducts().getUnitPrice())) / 100
        );
        structureV1.setUnitPrice(formattedPrice);

        structureV1.setPersonalSign(sysUser.getUserName());


        // todo...
        CreateFileByTemplateInput fileByTemplateInput = this.buildContractFileByTemplate(structureV1);
        CreateFileByTemplateOutput output =  eSignContractsFileService.createFileByTemplate(fileByTemplateInput);
        String fileId = output.getFileId();

        // 发起签署
        CreateSignFlowByFileInput.DocInfo docInfo = new CreateSignFlowByFileInput.DocInfo(fileId, "北大军哥一对一陪练协议.pdf");
        CreateSignFlowByFileInput.SignFlowConfig signFlowConfig = new CreateSignFlowByFileInput.SignFlowConfig();
        signFlowConfig.setSignFlowTitle("北大军哥一对一陪练协议合同签署");

        CreateSignFlowByFileInput.SignFlowConfig.RedirectConfig redirectConfig = new CreateSignFlowByFileInput.SignFlowConfig.RedirectConfig();
        redirectConfig.setRedirectUrl("http://xxxxxxxxxxxx");
        signFlowConfig.setRedirectConfig(redirectConfig);

        // 设置签署人信息
        CreateSignFlowByFileInput.SignerInfo signerInfo = new CreateSignFlowByFileInput.SignerInfo();
        signerInfo.setSignerType(0);

        // 设置个人签署方信息
        CreateSignFlowByFileInput.PsnSignerInfo psnSignerInfo = new CreateSignFlowByFileInput.PsnSignerInfo();
//        psnSignerInfo.setPsnAccount("<EMAIL>"); // 个人账号标识
        psnSignerInfo.setPsnAccount("<EMAIL>");

        // 设置个人身份信息
        CreateSignFlowByFileInput.PsnSignerInfo.PsnInfo psnInfo = new CreateSignFlowByFileInput.PsnSignerInfo.PsnInfo();
        psnInfo.setPsnName(sysUser.getUserName()); // 个人姓名
        psnInfo.setPsnIDCardNum("110101199001011234"); // 身份证号
        psnInfo.setPsnIDCardType("CRED_PSN_CH_IDCARD"); // 身份证类型
        psnInfo.setPsnMobile(sysUser.getPhonenumber()); // 手机号
        psnSignerInfo.setPsnInfo(psnInfo);

        signerInfo.setPsnSignerInfo(psnSignerInfo);

        // 设置签署区信息
        List<CreateSignFlowByFileInput.SignFieldInfo> signFields = new ArrayList<>();
        this.setSignFields(signFields, fileId, fileByTemplateInput.getComponents());

        signerInfo.setSignFields(signFields);

        List<CreateSignFlowByFileInput.SignerInfo> signers = new ArrayList<>();
        signers.add(signerInfo);


        CreateSignFlowByFileInput input = new CreateSignFlowByFileInput();
        input.setSigners(signers);
        input.setDocs(List.of(docInfo));
        input.setSignFlowConfig(signFlowConfig);

        CreateSignFlowByFileOutput signFlowByFile = eSignFlowService.createSignFlowByFile(input);
        String signFlowId = signFlowByFile.getData().getSignFlowId();


        // 获取签署连接
        GetSignUrlInput signUrlInput = new GetSignUrlInput();
        signUrlInput.setSignFlowId(signFlowId);

        GetSignUrlInput.Operator operator = new GetSignUrlInput.Operator();
        operator.setPsnAccount("<EMAIL>");
        signUrlInput.setOperator(operator);

        GetSignUrlOutput getSignUrl = eSignFlowService.getSignUrl(signUrlInput);
        String signUrl = getSignUrl.getData().getUrl();
        log.info("签署连接: {}", signUrl);
    }


    /**
     * 设置签章区配置信息
     */
    private void setSignFields(List<CreateSignFlowByFileInput.SignFieldInfo> signFields, String fileId, List<CreateFileByTemplateInput.ComponentInfo> components) {
        // 设置骑缝章， 有两个
        components.stream().filter(component -> component.getComponentType().equals(6))
                .forEach(component -> {
                    CreateSignFlowByFileInput.SignFieldInfo signField = buildCommonSignField(fileId);
                    // 设置签章区配置
                    CreateSignFlowByFileInput.NormalSignFieldConfig fieldConfig = new CreateSignFlowByFileInput.NormalSignFieldConfig();
                    fieldConfig.setFreeMode(false); // 自由模式，签署人可自由选择签署位置
                    fieldConfig.setAutoSign(true); // 不自动签署
                    fieldConfig.setMovableSignField(false); // 允许移动签署区
//        fieldConfig.setAssignedSealId(""); // 指定印章ID（可选）

                    // 签章区位置信息
                    SignFieldPosition signFieldPosition = new SignFieldPosition(null,
                            String.valueOf(component.getPosition().getComponentPageNum()),
                            component.getPosition().getComponentPositionX(), component.getPosition().getComponentPositionY());
                    fieldConfig.setSignFieldPosition(signFieldPosition);

                    if(component.getComponentName().contains("骑缝章")) {
                        fieldConfig.setSignFieldStyle(2);
                        signFieldPosition.setAcrossPageMode("ALL");
                    } else {
                        // 个人章或签名
                        fieldConfig.setSignFieldStyle(1);
                        fieldConfig.setAutoSign(false); // 个人不支持自动签署
                    }
                    fieldConfig.setSignFieldPosition(signFieldPosition);
                    signField.setNormalSignFieldConfig(fieldConfig);
                    signField.setSignFieldType(0);
                    signFields.add(signField);
                });
    }

    private CreateSignFlowByFileInput.SignFieldInfo buildCommonSignField(String fileId) {
        CreateSignFlowByFileInput.SignFieldInfo signField = new CreateSignFlowByFileInput.SignFieldInfo();
        signField.setFileId(fileId); // 对应文档的文件ID
        signField.setSignFieldType(0); // 签章区
        return signField;
    }

    private void checkOrderSignStatus(Orders order) {
        if(!(order.getOrderStatus().equalsIgnoreCase(OrderConstants.OrderStatus.PART_PAID)
                || order.getOrderStatus().equalsIgnoreCase(OrderConstants.OrderStatus.FULL_PAID))) {
            throw new IllegalStateException("该订单未支付，暂时无法发起合同签署");
        }

        if(!order.getSignStatus().equalsIgnoreCase(OrderConstants.SignStatus.UN_SIGN)) {
            throw new IllegalStateException("该订单无需签署，或已经完成合同签署");
        }
    }


    /**
     * 填充模板控件
     * @param structureV1
     * @return
     */
    private CreateFileByTemplateInput buildContractFileByTemplate(ESignDocTemplateStructureV1 structureV1) {

        /**
         * 获取控件列表
         */
        List<EsignTemplateComponents> componentsList = eSignTemplateService.lambdaQuery()
                .eq(EsignTemplateComponents::getDocTemplateId, eSignConfig.getDocTemplateId()).list();
        if(structureV1 == null || CollUtil.isEmpty(componentsList)) {
            throw new IllegalArgumentException("填充合同模板文件有误，请确认");
        }

        List< CreateFileByTemplateInput.ComponentInfo> componentInfos = new ArrayList<>();
        Field[] fields = ReflectUtil.getFields(ESignDocTemplateStructureV1.class);
        componentsList.forEach(component -> {
            Optional<Field> fieldOpt = Arrays.stream(fields).filter(f -> f.getName().equals(component.getReflectPropertyId())).findFirst();
            Field field = fieldOpt.orElse( null);
            try {
                CreateFileByTemplateInput.ComponentInfo componentInfo = getComponentInfo(structureV1, component, field);
                componentInfos.add(componentInfo);

            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        });
        return CreateFileByTemplateInput.builder().docTemplateId(eSignConfig.getDocTemplateId())
                .components(componentInfos)
                .fileName("北大军哥一对一陪练协议.docx")
                .build();
    }

    private static CreateFileByTemplateInput.ComponentInfo getComponentInfo(ESignDocTemplateStructureV1 structureV1, EsignTemplateComponents component, Field field) throws IllegalAccessException {
        Object value = null;
        if(field != null) {
            field.setAccessible(true);
            value = field.get(structureV1);
        }
        if(component.getRequired().equals(Boolean.TRUE)) {  // 必填
            if(value == null) {
                throw new IllegalArgumentException("合同模板文件有必填项[ " + component.getComponentName() + " ]未填写，请确认");
            }
        }

        CreateFileByTemplateInput.ComponentInfo info  = new CreateFileByTemplateInput.ComponentInfo(
                component.getComponentId(),
                value != null ? value.toString() : null,
                component.getComponentPosition()
        );
        info.setComponentType(component.getComponentType());
        info.setComponentName(component.getComponentName());
        return info;
    }
}
