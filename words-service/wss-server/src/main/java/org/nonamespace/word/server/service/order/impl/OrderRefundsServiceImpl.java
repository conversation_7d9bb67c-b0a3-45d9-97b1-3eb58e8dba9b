package org.nonamespace.word.server.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.constants.OrderConstants;
import org.nonamespace.word.server.domain.order.OrderRefunds;
import org.nonamespace.word.server.domain.order.OrderRefundsTrx;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.domain.order.OrdersTrx;
import org.nonamespace.word.server.dto.order.OrderRefundDto;
import org.nonamespace.word.server.dto.order.RefundRecordDto;
import org.nonamespace.word.server.mapper.order.OrderRefundsMapper;
import org.nonamespace.word.server.service.base.BaseService;
import org.nonamespace.word.server.service.order.IOrderRefundsService;
import org.nonamespace.word.server.service.order.IOrderRefundsTrxService;
import org.nonamespace.word.server.service.order.IOrdersService;
import org.nonamespace.word.server.service.order.IOrdersTrxService;
import org.nonamespace.word.server.util.OrderTrxCodeUtil;
import org.nonamespace.word.thirdpart.allinpay.dto.RefundDto;
import org.nonamespace.word.thirdpart.allinpay.exception.AllinPayException;
import org.nonamespace.word.thirdpart.allinpay.model.RefundOutput;
import org.nonamespace.word.thirdpart.allinpay.service.IAllinPayService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 退款记录Service实现类
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderRefundsServiceImpl extends BaseService<OrderRefundsMapper, OrderRefunds>
        implements IOrderRefundsService {

    private final OrderTrxCodeUtil orderTrxCodeUtil;
    private final IOrdersService ordersService;
    private final IOrdersTrxService ordersTrxService;
    private final IOrderRefundsTrxService orderRefundsTrxService;
    private final IAllinPayService allinPayService;


    @Override
    public OrderRefundDto.RefundResp refundApply(String orderId, OrderRefundDto.RefundReq refundReq) {
        Orders orders = ordersService.checkOrderIfExists(orderId);

        // 检查订单状态
        if(!List.of(OrderConstants.OrderStatus.PAID, OrderConstants.OrderStatus.FULL_PAID, OrderConstants.OrderStatus.PART_PAID)
                .contains(orders.getOrderStatus())) {
            throw new IllegalArgumentException("当前订单状态不支持退款操作");
        }

        // 获取剩余课时，需要退款的金额
        Long refundAmount = 1L;

        if(refundAmount <= 0 || refundAmount > orders.getAmtPaid()) {
            throw new IllegalArgumentException("退款金额有误，请重新确认");
        }

        // 获取已支付的交易流水
        List<OrdersTrx> paidTrxes = ordersTrxService.lambdaQuery()
                .eq(OrdersTrx::getOrderId, orderId)
                .eq(OrdersTrx::getTrxType, OrderConstants.TrxType.PAY)
                .eq(OrdersTrx::getTrxStatus, OrderConstants.TrxStatus.PAID)
                .list();

        if(paidTrxes.isEmpty()) {
            throw new IllegalArgumentException("未找到可退款的支付记录");
        }

        // 已付款的交易流水总金额。是否大于等于退款金额
        long totalPaidAmt = paidTrxes.stream().mapToLong(OrdersTrx::getTrxAmt).sum();
        if(totalPaidAmt < refundAmount) {
            throw new IllegalArgumentException("退款金额不能大于付款总金额，请重新确认");
        }

        // 创建退款订单
        OrderRefunds orderRefunds = this.createRefundRecord(orders, OrderConstants.RefundType.FULL, refundAmount, refundReq.getRefundReason());

        /**
         * 这里计算出来退款哪些交易流水
         * 比如第一期付500 第二期2000. 这个时候退款退2300。 得算出来第二笔交易全额退 第一笔退300
         */
        AtomicLong remainingRefundAmt = new AtomicLong(refundAmount);
        List<OrderRefundsTrx> orderRefundsList = new CopyOnWriteArrayList<>();
        paidTrxes.forEach(paidTrx -> {
            if(paidTrx.getTrxAmt() <= remainingRefundAmt.get()) {
                // 这笔流水要全额退
                OrderRefundsTrx refundsTrx = OrderRefundsTrx.builder().refundId(orderRefunds.getId())
                        .originalTrxId(paidTrx.getCusTrxSeq())
                        .refundTrxId(orderTrxCodeUtil.generalTrxSeq())
                        .refundType(OrderConstants.RefundType.FULL).refundAmount(paidTrx.getTrxAmt())
                        .refundStatus(OrderConstants.RefundStatus.WAIT_APPROVE)
                        .ordersTrxs(paidTrx)
                        .refundMethod(OrderConstants.RefundMethod.ORIGINAL)
                        .orderId(paidTrx.getOrderId()).build();

                remainingRefundAmt.getAndAdd(-paidTrx.getTrxAmt());
                orderRefundsList.add(refundsTrx);
            } else {
                OrderRefundsTrx refundsTrx = OrderRefundsTrx.builder().refundId(orderRefunds.getId())
                        .originalTrxId(paidTrx.getCusTrxSeq())
                        .refundTrxId(orderTrxCodeUtil.generalTrxSeq())
                        .refundType(OrderConstants.RefundType.PARTIAL).refundAmount(remainingRefundAmt.get())
                        .refundStatus(OrderConstants.RefundStatus.WAIT_APPROVE)
                        .ordersTrxs(paidTrx)
                        .refundMethod(OrderConstants.RefundMethod.ORIGINAL)
                        .orderId(paidTrx.getOrderId()).build();
                orderRefundsList.add(refundsTrx);
            }
        });

        if(CollUtil.isEmpty(orderRefundsList)) {
            throw new IllegalCallerException("退款交易流水为空，无法退款");
        }
        orderRefundsTrxService.saveBatch(orderRefundsList);

        //
        ordersService.lambdaUpdate().set(Orders::getOrderStatus, OrderConstants.OrderStatus.REFUND_APPROVE)
                .eq(Orders::getId, orders.getId())
                .update();

        //
        ordersTrxService.lambdaUpdate().set(OrdersTrx::getTrxStatus, OrderConstants.TrxStatus.REFUND_APPROVE)
                .eq(OrdersTrx::getOrderId, orders.getId())
                .update();

        OrderRefundDto.RefundResp resp = new OrderRefundDto.RefundResp();
        resp.setOrderId(orderId);
        resp.setRefundAmount(refundAmount);
        resp.setRefundReason(refundReq.getRefundReason());
        resp.setMessage("订单退款成功");
        return resp;
    }




    @Override
    public OrderRefunds createRefundRecord(Orders orders, String refundType, Long refundAmount, String refundReason) {
        
        log.info("创建退款记录: orderId={}, refundType={}, refundAmount={}", 
                orders.getId(), refundType, refundAmount);
        
        // 构建退款记录
        OrderRefunds refundRecord = OrderRefunds.builder()
                .orderId(orders.getId())
                .refundNo(orderTrxCodeUtil.generalOrderCode(OrderConstants.TrxType.REFUND))
                .refundType(refundType)
                .refundAmount(refundAmount)
                .refundReason(refundReason)
                .refundStatus(OrderConstants.RefundStatus.WAIT_APPROVE)
                .studentId(orders.getStudentId())
                .salerId(orders.getSalerId())
                .productId(orders.getProductId())
                .products(orders.getProducts())
                .orders(orders)
                .refundMethod(OrderConstants.RefundMethod.ORIGINAL)
                .build();
        // 保存退款记录
        this.save(refundRecord);
        
        log.info("退款记录创建成功: refundRecordId={}", refundRecord.getId());
        return refundRecord;
    }


    @Override
    public IPage<RefundRecordDto.Resp> selectRefundRecordsByParam(RefundRecordDto.QueryReq req) {
        Page<RefundRecordDto.Resp> page = new Page<>(req.getPageNum(), req.getPageSize());
        return this.baseMapper.selectRefundRecordsByParam(page, req);
    }

    @Override
    public RefundRecordDto.DetailResp getRefundRecordDetail(String refundRecordId) {
        return this.baseMapper.selectRefundRecordDetail(refundRecordId);
    }

    @Override
    public void approveRefundRecord(RefundRecordDto.ApproveReq req, List<OrderRefunds> refundRecords) {
        log.info("审批退款记录: req={}", req.toString());
        // 校验退款记录是否存在
        if(CollUtil.isEmpty(refundRecords)) {
            throw new IllegalArgumentException("退款记录不存在");
        }

        List<String> refundIds = refundRecords.stream().map(OrderRefunds::getId).toList();

        List<OrderRefundsTrx> refundsTrxes = orderRefundsTrxService.lambdaQuery().in(OrderRefundsTrx::getRefundId, refundIds).list();
        if(CollUtil.isEmpty(refundsTrxes)) {
            throw new IllegalArgumentException("退款交易流水不存在");
        }

        if(req.getApproveResult().equalsIgnoreCase("审批通过")) {
            // 调用支付平台退款接口
            refundsTrxes.forEach(trx -> {
                RefundDto.Req refundReq = RefundDto.Req.builder().refundTrxId(trx.getRefundTrxId())
                        .originalTrxId(trx.getOriginalTrxId())
                        .amount(trx.getRefundAmount())
                        .remark(req.getApproveDesc()).build();

                try {
                    RefundOutput output = allinPayService.refund(refundReq);
                    log.info("通联退款请求: refundReq={}, output={}", refundReq, output);
                    if(!output.isSuccess()) {
                        throw new IllegalCallerException("通联退款失败: " + output.getErrmsg());
                    }

                    orderRefundsTrxService.lambdaUpdate().eq(OrderRefundsTrx::getId, trx.getId())
                            .set(OrderRefundsTrx::getRefundStatus, OrderConstants.RefundStatus.WAIT_REFUND)
                            .set(OrderRefundsTrx::getPlatformRefundId, output.getTrxid())
                            .set(OrderRefundsTrx::getPlatformResponse, JSONUtil.toJsonStr(output))
                            .set(OrderRefundsTrx::getUpdateTime, DateUtil.date())
                            .update();

                } catch (AllinPayException.ProcessingException e) {
                    throw new RuntimeException(e);
                }

            });

            this.lambdaUpdate().in(OrderRefunds::getId, refundIds)
                    .set(OrderRefunds::getRefundStatus, OrderConstants.RefundStatus.WAIT_REFUND)
                    .set(OrderRefunds::getApproveResult, req.getApproveResult())
                    .set(OrderRefunds::getApproveDesc, req.getApproveDesc())
                    .set(OrderRefunds::getApproveTime, DateUtil.date())
                    .set(OrderRefunds::getApproveUserId, String.valueOf(SecurityUtils.getUserId()))
                    .update();

            return ;
        }

        // 审批不通过
        orderRefundsTrxService.lambdaUpdate().in(OrderRefundsTrx::getRefundId, refundIds)
                .set(OrderRefundsTrx::getRefundStatus, OrderConstants.RefundStatus.REJECT)
                .set(OrderRefundsTrx::getUpdateTime, DateUtil.date())
                .update();

        this.lambdaUpdate().in(OrderRefunds::getId, refundIds)
                .set(OrderRefunds::getRefundStatus, OrderConstants.RefundStatus.REJECT)
                .set(OrderRefunds::getApproveResult, req.getApproveResult())
                .set(OrderRefunds::getApproveDesc, req.getApproveDesc())
                .set(OrderRefunds::getApproveTime, DateUtil.date())
                .set(OrderRefunds::getApproveUserId, String.valueOf(SecurityUtils.getUserId()))
                .update();
    }

    @Override
    public RefundRecordDto.StatisticsResp getRefundStatistics(String startDate, String endDate) {
        // 查询基础统计数据
        List<Map<String, Object>> basicStats = this.baseMapper.selectRefundStatistics(startDate, endDate);
        
        // 查询学科统计数据
        List<Map<String, Object>> subjectStats = this.baseMapper.selectRefundStatisticsBySubject(startDate, endDate);
        
        // 查询操作人统计数据
        List<Map<String, Object>> operatorStats = this.baseMapper.selectRefundStatisticsByOperator(startDate, endDate);
        
        // 构建响应对象
        RefundRecordDto.StatisticsResp resp = new RefundRecordDto.StatisticsResp();
        
        if (CollUtil.isNotEmpty(basicStats)) {
            Map<String, Object> stat = basicStats.get(0);
            resp.setStatisticsDate(stat.get("statistics_date").toString());
            resp.setTotalRefundCount(((Number) stat.get("total_refund_count")).intValue());
            resp.setTotalRefundAmountYuan(formatAmountToYuan((Number) stat.get("total_refund_amount")));
            resp.setPartialRefundCount(((Number) stat.get("partial_refund_count")).intValue());
            resp.setPartialRefundAmountYuan(formatAmountToYuan((Number) stat.get("partial_refund_amount")));
            resp.setFullRefundCount(((Number) stat.get("full_refund_count")).intValue());
            resp.setFullRefundAmountYuan(formatAmountToYuan((Number) stat.get("full_refund_amount")));
            resp.setSuccessRefundCount(((Number) stat.get("success_refund_count")).intValue());
            resp.setFailedRefundCount(((Number) stat.get("failed_refund_count")).intValue());
            resp.setAvgRefundAmountYuan(formatAmountToYuan((Number) stat.get("avg_refund_amount")));
        }
        
        // 设置学科统计
        List<RefundRecordDto.SubjectStatistics> subjectStatistics = subjectStats.stream()
                .map(stat -> {
                    RefundRecordDto.SubjectStatistics subjectStat = new RefundRecordDto.SubjectStatistics();
                    subjectStat.setSubject(stat.get("subject").toString());
                    subjectStat.setRefundCount(((Number) stat.get("refund_count")).intValue());
                    subjectStat.setRefundAmountYuan(formatAmountToYuan((Number) stat.get("refund_amount")));
                    return subjectStat;
                })
                .collect(Collectors.toList());
        resp.setSubjectStatistics(subjectStatistics);
        
        // 设置操作人统计
        List<RefundRecordDto.OperatorStatistics> operatorStatistics = operatorStats.stream()
                .map(stat -> {
                    RefundRecordDto.OperatorStatistics operatorStat = new RefundRecordDto.OperatorStatistics();
                    operatorStat.setOperatorName(stat.get("operator_name").toString());
                    operatorStat.setRefundCount(((Number) stat.get("refund_count")).intValue());
                    operatorStat.setRefundAmountYuan(formatAmountToYuan((Number) stat.get("refund_amount")));
                    return operatorStat;
                })
                .collect(Collectors.toList());
        resp.setOperatorStatistics(operatorStatistics);
        
        return resp;
    }

    @Override
    public List<OrderRefunds> getRefundRecordsByOrderId(String orderId) {
        return this.baseMapper.selectByOrderId(orderId);
    }

    @Override
    public Integer getPendingApprovalCount() {
        return Math.toIntExact(this.lambdaQuery().eq(OrderRefunds::getRefundStatus, OrderConstants.RefundStatus.WAIT_APPROVE).count());
    }

    @Override
    public List<RefundRecordDto.Resp> exportRefundRecords(RefundRecordDto.QueryReq req) {
        // 设置大的页面大小用于导出
        req.setPageSize(req.getMaxExportCount() != null ? req.getMaxExportCount() : 10000);
        req.setPageNum(1);
        
        IPage<RefundRecordDto.Resp> page = selectRefundRecordsByParam(req);
        return page.getRecords();
    }


    /**
     * 格式化金额为元（保留2位小数）
     */
    private String formatAmountToYuan(Number amountFen) {
        if (amountFen == null) {
            return "0.00";
        }
        return String.format("%.2f", amountFen.longValue() / 100.0);
    }
}
