package org.nonamespace.word.server.service.order;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.order.OrderRefunds;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.dto.order.OrderRefundDto;
import org.nonamespace.word.server.dto.order.RefundRecordDto;

import java.util.List;

/**
 * 退款记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface IOrderRefundsService extends IService<OrderRefunds> {

    /**
     * 订单退款申请，需要财务审核
     * @param orderId
     * @param refundReq
     */
    OrderRefundDto.RefundResp refundApply(String orderId, OrderRefundDto.RefundReq refundReq);


    /**
     * 创建退款记录
     * 
     * @param orders 订单信息
     * @param refundType 退款类型
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 退款记录
     */
    OrderRefunds createRefundRecord(Orders orders, String refundType, Long refundAmount, String refundReason);

    /**
     * 分页查询退款记录
     * 
     * @param req 查询请求
     * @return 分页结果
     */
    IPage<RefundRecordDto.Resp> selectRefundRecordsByParam(RefundRecordDto.QueryReq req);

    /**
     * 查询退款记录详情
     * 
     * @param refundRecordId 退款记录ID
     * @return 退款记录详情
     */
    RefundRecordDto.DetailResp getRefundRecordDetail(String refundRecordId);

    /**
     * 审批退款记录
     */
    void approveRefundRecord(RefundRecordDto.ApproveReq req, List<OrderRefunds> refundRecords);

    /**
     * 查询退款统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    RefundRecordDto.StatisticsResp getRefundStatistics(String startDate, String endDate);

    /**
     * 查询订单的退款记录
     * 
     * @param orderId 订单ID
     * @return 退款记录列表
     */
    List<OrderRefunds> getRefundRecordsByOrderId(String orderId);

    /**
     * 查询待审批的退款记录数量
     * 
     * @return 待审批数量
     */
    Integer getPendingApprovalCount();

    /**
     * 导出退款记录
     * 
     * @param req 查询条件
     * @return 导出数据
     */
    List<RefundRecordDto.Resp> exportRefundRecords(RefundRecordDto.QueryReq req);

}
