package org.nonamespace.word.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.management.course.CourseHoursImportDto;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 课时导入Service实现类
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
public class CourseHoursImportServiceImpl implements ICourseHoursImportService {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IUserService userService;
    
    @Autowired
    private ITeacherStudentRelationService teacherStudentRelationService;
    
    @Autowired
    private IStudentCourseHoursService studentCourseHoursService;
    
    @Autowired
    private SystemDataQueryUtil systemDataQueryUtil;

    @Autowired
    private UserStudentExtService userStudentExtService;

    @Autowired
    private ITeacherProfileService teacherProfileService;

    @Autowired
    private ITeachingGroupService teachingGroupService;

    @Autowired
    private ITeachingGroupMemberService teachingGroupMemberService;

    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private IDeptService deptService;

    @Override
    public CourseHoursImportDto.ImportResult importCourseHours(MultipartFile file, CourseHoursImportDto.ImportRequest request) {
        CourseHoursImportDto.ImportResult result = new CourseHoursImportDto.ImportResult();
        result.setTotalRows(0);
        result.setSuccessRows(0);
        result.setFailedRows(0);
        result.setCreatedTeachers(0);
        result.setCreatedStudents(0);
        result.setCreatedRelations(0);
        result.setCreatedHours(0);

        StringBuilder detailMessage = new StringBuilder();

        try {
            // 读取Excel文件
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
            List<Map<String, Object>> rows = reader.readAll();
            result.setTotalRows(rows.size());

            // 生成批次号
            String batchNo = "BATCH_" + System.currentTimeMillis();

            log.info("开始导入课时数据，总行数: {}, 批次号: {}", rows.size(), batchNo);

            AtomicInteger actualRowIndex = new AtomicInteger(0);

            List<CourseHoursImportDto.ExcelRowData> rowDatas = rows.stream().map(x -> parseRowData(x, actualRowIndex.incrementAndGet())).toList();

            ValidationResult validation = validateData(rowDatas);
            if (validation.hasErrors()) {
                result.setFailedRows(validation.getErrors().size());
                result.setErrorMessage("数据校验失败");
                result.setDetailMessage(String.join("\n", validation.getErrors()));
                return result;
            }


            for (int i = 0; i < rowDatas.size(); i++) {
                CourseHoursImportDto.ExcelRowData rowData = rowDatas.get(i);
                try {

                    // 处理老师
                    SysUser teacher = null;
                    if (StrUtil.isNotEmpty(rowData.getTeacherPhone())) {
                        teacher = processTeacher(rowData);
                        if (teacher.getUserId() == null) {
                            result.setCreatedTeachers(result.getCreatedTeachers() + 1);
                        }
                    }

                    // 处理学生
                    SysUser student = null;
                    if (StrUtil.isNotEmpty(rowData.getStudentPhone())) {
                        student = processStudent(rowData);
                        if (student.getUserId() == null) {
                            result.setCreatedStudents(result.getCreatedStudents() + 1);
                        }
                    }

                    // 处理师生关系
                    if (teacher != null && student != null) {
                        boolean relationCreated = processTeacherStudentRelation(teacher, student, rowData);
                        if (relationCreated) {
                            result.setCreatedRelations(result.getCreatedRelations() + 1);
                        }
                    }

                    // 处理课时记录
                    if (student != null) {
                        boolean hoursCreated = processStudentCourseHours(student, rowData, batchNo);
                        if (hoursCreated) {
                            result.setCreatedHours(result.getCreatedHours() + 1);
                        }
                    }

                    result.setSuccessRows(result.getSuccessRows() + 1);
                    detailMessage.append("第").append(i + 1).append("行处理成功\n");

                } catch (Exception e) {
                    result.setFailedRows(result.getFailedRows() + 1);
                    String errorMsg = "第" + (i + 1) + "行处理失败: " + e.getMessage();
                    detailMessage.append(errorMsg).append("\n");
                    log.error(errorMsg, e);
                }
            }

            result.setDetailMessage(detailMessage.toString());
            log.info("课时数据导入完成，成功: {}, 失败: {}", result.getSuccessRows(), result.getFailedRows());

        } catch (IOException e) {
            result.setErrorMessage("读取Excel文件失败: " + e.getMessage());
            log.error("读取Excel文件失败", e);
        } catch (Exception e) {
            result.setErrorMessage("导入过程中发生错误: " + e.getMessage());
            log.error("导入过程中发生错误", e);
        }

        return result;
    }

    /**
     * 预先数据校验
     */
    private ValidationResult validateData(List<CourseHoursImportDto.ExcelRowData> excelData) {
        ValidationResult validation = new ValidationResult();

        // 用于检查重复数据
        Set<String> studentPhones = new HashSet<>();
        Map<String, String> teacherNameToPhone = new HashMap<>();
        Map<String, String> teacherNameToGroup = new HashMap<>();

        for (int i = 0; i < excelData.size(); i++) {
            CourseHoursImportDto.ExcelRowData row = excelData.get(i);
            int rowNum = i + 1;

            try {
                // 基本字段验证
                if (StrUtil.isBlank(row.getStudentPhone()) && StrUtil.isBlank(row.getTeacherPhone())) {
                    validation.addError("第" + rowNum + "行：学生手机号和老师手机号不能同时为空");
                    continue;
                }

                if (row.getPurchasedTotalHours() == null || row.getPurchasedTotalHours().compareTo(BigDecimal.ZERO) < 0) {
                    validation.addError("第" + rowNum + "行：购买课时不能小于0");
                    continue;
                }

                if (row.getPurchasedRemainingHours() == null || row.getPurchasedRemainingHours().compareTo(BigDecimal.ZERO) < 0) {
                    validation.addError("第" + rowNum + "行：剩余购买课时不能小于0");
                    continue;
                }

                if (row.getPurchasedRemainingHours().compareTo(row.getPurchasedTotalHours()) > 0) {
                    validation.addError("第" + rowNum + "行：剩余购买课时不能大于购买课时");
                    continue;
                }

                if (row.getGiftTotalHours() == null || row.getGiftTotalHours().compareTo(BigDecimal.ZERO) < 0) {
                    validation.addError("第" + rowNum + "行：赠送课时不能小于0");
                    continue;
                }

                if (row.getGiftRemainingHours() == null || row.getGiftRemainingHours().compareTo(BigDecimal.ZERO) < 0) {
                    validation.addError("第" + rowNum + "行：剩余赠送课时不能小于0");
                    continue;
                }

                if (row.getGiftRemainingHours().compareTo(row.getGiftTotalHours()) > 0) {
                    validation.addError("第" + rowNum + "行：剩余赠送课时不能大于赠送课时");
                    continue;
                }

                // 学生手机号唯一性检查
                if (StrUtil.isNotBlank(row.getStudentPhone())) {
                    if (studentPhones.contains(row.getStudentPhone())) {
                        validation.addError("第" + rowNum + "行：学生手机号 " + row.getStudentPhone() + " 在文件中重复");
                        continue;
                    }
                    studentPhones.add(row.getStudentPhone());
                }

                // 同名老师手机号一致性检查
                if (StrUtil.isNotBlank(row.getTeacherName()) && StrUtil.isNotBlank(row.getTeacherPhone())) {
                    String existingPhone = teacherNameToPhone.get(row.getTeacherName());
                    if (existingPhone != null && !existingPhone.equals(row.getTeacherPhone())) {
                        validation.addError("第" + rowNum + "行：老师 " + row.getTeacherName() + " 的手机号与文件中其他行不一致");
                        continue;
                    }
                    teacherNameToPhone.put(row.getTeacherName(), row.getTeacherPhone());
                }

                // 同名老师教学组一致性检查
                if (StrUtil.isNotBlank(row.getTeacherName()) && StrUtil.isNotBlank(row.getTeachingGroupName())) {
                    String existingGroup = teacherNameToGroup.get(row.getTeacherName());
                    if (existingGroup != null && !existingGroup.equals(row.getTeachingGroupName())) {
                        validation.addError("第" + rowNum + "行：老师 " + row.getTeacherName() + " 的教学组与文件中其他行不一致");
                        continue;
                    }
                    teacherNameToGroup.put(row.getTeacherName(), row.getTeachingGroupName());
                }

                // 验证通过，添加到有效数据
                validation.addValidRow(row);

            } catch (Exception e) {
                validation.addError("第" + rowNum + "行数据验证失败: " + e.getMessage());
            }
        }

        log.info("数据校验完成，有效行数: {}, 错误行数: {}", validation.getValidRows().size(), validation.getErrors().size());
        return validation;
    }

    /**
     * 解析行数据
     */
    private CourseHoursImportDto.ExcelRowData parseRowData(Map<String, Object> row, int rowNum) {
        CourseHoursImportDto.ExcelRowData rowData = new CourseHoursImportDto.ExcelRowData();

        try {
            rowData.setTeachingGroupName(getStringValue(row, "教学组"));
            rowData.setTeacherName(getStringValue(row, "老师姓名"));
            rowData.setTeacherPhone(getStringValue(row, "老师手机号"));
            rowData.setStudentName(getStringValue(row, "学生姓名"));
            rowData.setStudentPhone(getStringValue(row, "学生手机号"));
            rowData.setSubject(getStringValue(row, "学科", "英语"));
            rowData.setSpecification(getStringValue(row, "课型", "单词课"));
            rowData.setNature(getStringValue(row, "课时性质", "正式课"));
            rowData.setPurchasedTotalHours(getBigDecimalValue(row, "购买课时", BigDecimal.ZERO));
            rowData.setPurchasedRemainingHours(getBigDecimalValue(row, "剩余购买课时", BigDecimal.ZERO));
            rowData.setGiftTotalHours(getBigDecimalValue(row, "赠送课时", BigDecimal.ZERO));
            rowData.setGiftRemainingHours(getBigDecimalValue(row, "剩余赠送课时", BigDecimal.ZERO));
            rowData.setUnitPrice(getBigDecimalValue(row, "单价", BigDecimal.ZERO));

//            // 验证必填字段
//            if (StrUtil.isBlank(rowData.getTeacherName())) {
//                throw new RuntimeException("老师姓名不能为空");
//            }
//            if (StrUtil.isBlank(rowData.getTeacherPhone())) {
//                throw new RuntimeException("老师手机号不能为空");
//            }
//            if (StrUtil.isBlank(rowData.getStudentName())) {
//                throw new RuntimeException("学生姓名不能为空");
//            }
//            if (StrUtil.isBlank(rowData.getStudentPhone())) {
//                throw new RuntimeException("学生手机号不能为空");
//            }
            if (rowData.getPurchasedTotalHours() == null || rowData.getPurchasedTotalHours().compareTo(BigDecimal.ZERO) < 0) {
                throw new RuntimeException("购买课时不能小于0");
            }
            if (rowData.getPurchasedRemainingHours() == null || rowData.getPurchasedRemainingHours().compareTo(BigDecimal.ZERO) < 0) {
                throw new RuntimeException("剩余购买课时不能小于0");
            }
            if (rowData.getPurchasedRemainingHours().compareTo(rowData.getPurchasedTotalHours()) > 0) {
                throw new RuntimeException("剩余购买课时不能大于购买课时");
            }

            if (rowData.getGiftTotalHours() == null || rowData.getGiftTotalHours().compareTo(BigDecimal.ZERO) < 0) {
                throw new RuntimeException("赠送课时不能小于0");
            }
            if (rowData.getGiftRemainingHours() == null || rowData.getGiftRemainingHours().compareTo(BigDecimal.ZERO) < 0) {
                throw new RuntimeException("剩余赠送课时不能小于0");
            }
            if (rowData.getGiftRemainingHours().compareTo(rowData.getGiftTotalHours()) > 0) {
                throw new RuntimeException("剩余赠送课时不能大于赠送课时");
            }

        } catch (Exception e) {
            throw new RuntimeException("第" + rowNum + "行数据格式错误: " + e.getMessage());
        }

        return rowData;
    }

    /**
     * 获取字符串值
     */
    private String getStringValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }
        return String.valueOf(value).trim();
    }

    /**
     * 获取字符串值
     */
    private String getStringValue(Map<String, Object> row, String key, String defaultValue) {
        return StrUtil.emptyToDefault(getStringValue(row, key), defaultValue);
    }

    /**
     * 获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }
        try {
            if (value instanceof Number) {
                return new BigDecimal(value.toString());
            }
            return new BigDecimal(String.valueOf(value).trim());
        } catch (NumberFormatException e) {
            throw new RuntimeException("字段 " + key + " 的值 " + value + " 不是有效的数字");
        }
    }

    /**
     * 获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> row, String key, BigDecimal defaultValue) {
        return ObjectUtil.defaultIfNull(getBigDecimalValue(row, key), defaultValue);
    }

    /**
     * 处理老师
     */
    private SysUser processTeacher(CourseHoursImportDto.ExcelRowData rowData) {
        // 根据手机号查找老师
        SysUser existingTeacher = sysUserService.selectUserByPhone(rowData.getTeacherPhone());

        if (existingTeacher == null) {
            log.debug("老师不存在存在: {}", rowData.getTeacherPhone());

            // 创建新老师
            SysUser teacher = new SysUser();
            teacher.setUserId(userService.nextUserId());
            teacher.setUserName(rowData.getTeacherPhone()); // 使用手机号作为用户名
            teacher.setNickName(rowData.getTeacherName());
            teacher.setPhonenumber(rowData.getTeacherPhone());
            teacher.setPassword(SecurityUtils.encryptPassword(userService.getDefaultPassword())); // 默认密码
            teacher.setStatus("0"); // 正常状态
            teacher.setSex("2"); // 未知
            teacher.setDeptId(systemDataQueryUtil.getTeachingCenterDept().getDeptId());

            // 设置老师角色
            Long[] roleIds = {systemDataQueryUtil.getTeacherRole().getRoleId()};
            teacher.setRoleIds(roleIds);

            userService.save(teacher);

            existingTeacher = sysUserService.selectUserByPhone(rowData.getTeacherPhone());

            if(!teacherProfileService.lambdaQuery().eq(TeacherProfile::getTeacherId, String.valueOf(existingTeacher.getUserId())).exists()) {

                // 创建老师个人资料
                TeacherProfile teacherProfile = new TeacherProfile();
                teacherProfile.setTeacherId(String.valueOf(existingTeacher.getUserId()));
                teacherProfile.setRealName(rowData.getTeacherName());
                teacherProfile.setPhonenumber(existingTeacher.getPhonenumber());
                teacherProfile.setNickName(existingTeacher.getNickName());
                teacherProfile.setDeleted(false);
                teacherProfile.setSubjects(CollUtil.list(false, rowData.getSubject())); // 默认设置为英语

                teacherProfileService.save(teacherProfile);

                log.debug("创建老师成功: {} ({})", rowData.getTeacherName(), rowData.getTeacherPhone());
            }
        }


        // 处理教学组
        String groupId = null;
        if(StrUtil.isNotBlank(rowData.getTeachingGroupName())){
            String deptName = rowData.getTeachingGroupName().replaceAll("\\s+", " ").trim();
            SysDept sysDept = deptService.lambdaQuery().select(SysDept::getDeptId, SysDept::getDeptName)
                    .eq(SysDept::getDeptName, deptName)
                    .one();

            if (sysDept == null) {
                sysDept = new SysDept();
                sysDept.setDeptId(deptService.nextDeptId());
                sysDept.setParentId(systemDataQueryUtil.getTeachingCenterDept().getDeptId());
                sysDept.setDeptName(deptName);
                sysDept.setStatus("0");
                sysDeptService.insertDept(sysDept);

                TeachingGroup teachingGroup = new TeachingGroup();
                teachingGroup.setId(IdUtil.getSnowflakeNextIdStr());
                teachingGroup.setName(deptName);
                teachingGroup.setDeptId(sysDept.getDeptId());
                teachingGroup.setStatus("active");
                teachingGroup.setDeleted(false);
                teachingGroupService.save(teachingGroup);
                groupId = teachingGroup.getId();

            } else {
                groupId = teachingGroupService.lambdaQuery().eq(TeachingGroup::getDeptId, sysDept.getDeptId())
                        .oneOpt()
                        .map(TeachingGroup::getId)
                        .orElse(null);
                if (groupId == null) {

                    TeachingGroup teachingGroup = new TeachingGroup();
                    teachingGroup.setId(IdUtil.getSnowflakeNextIdStr());
                    teachingGroup.setName(deptName);
                    teachingGroup.setDeptId(sysDept.getDeptId());
                    teachingGroup.setStatus("active");
                    teachingGroup.setDeleted(false);
                    teachingGroupService.save(teachingGroup);

                    groupId = teachingGroup.getId();
                }

            }

        }

        // 处理老师进组
        if(groupId != null){

            boolean isInGroup = teachingGroupMemberService.lambdaQuery().eq(TeachingGroupMember::getTeacherId, String.valueOf(existingTeacher.getUserId()))
                    .eq(TeachingGroupMember::getGroupId, groupId)
                    .oneOpt()
                    .map(member -> member.getStatus().equals("active"))
                    .orElse(false);
            if (!isInGroup) {

                teachingGroupMemberService.lambdaUpdate().set(TeachingGroupMember::getDeleted, true)
                        .set(TeachingGroupMember::getStatus, "inactive")
                        .set(TeachingGroupMember::getUpdateTime, WssContext.now())
                        .set(TeachingGroupMember::getUpdateBy, WssContext.userId())
                        .eq(TeachingGroupMember::getTeacherId, String.valueOf(existingTeacher.getUserId()))
                        .update();

                TeachingGroupMember member = new TeachingGroupMember();
                member.setId(IdUtil.getSnowflakeNextIdStr());
                member.setGroupId(groupId);
                member.setTeacherId(String.valueOf(existingTeacher.getUserId()));
                member.setRoleType("member"); // 默认角色为成员
                member.setJoinTime(WssContext.now());
                member.setStatus("active");
                member.setDeleted(false);
                boolean added = teachingGroupMemberService.save(member);
                if (added) {
                    log.info("老师 {} ({}) 成功加入教学组 {}", existingTeacher.getNickName(), existingTeacher.getPhonenumber(), rowData.getTeachingGroupName());
                } else {
                    log.warn("老师 {} ({}) 加入教学组 {} 失败", existingTeacher.getNickName(), existingTeacher.getPhonenumber(), rowData.getTeachingGroupName());
                }
            } else {
                log.debug("老师 {} ({}) 已在教学组 {}", existingTeacher.getNickName(), existingTeacher.getPhonenumber(), rowData.getTeachingGroupName());
            }
        }

        return existingTeacher;
    }

    /**
     * 处理学生
     */
    private SysUser processStudent(CourseHoursImportDto.ExcelRowData rowData) {
        // 根据手机号查找学生
        SysUser existingStudent = sysUserService.selectUserByPhone(rowData.getStudentPhone());

        if (existingStudent != null) {
            log.debug("学生已存在: {}", rowData.getStudentPhone());
            return existingStudent;
        }

        // 创建新学生
        SysUser student = new SysUser();
        student.setUserId(userService.nextUserId());
        student.setUserName(rowData.getStudentPhone()); // 使用手机号作为用户名
        student.setNickName(rowData.getStudentName());
        student.setPhonenumber(rowData.getStudentPhone());
        student.setPassword(SecurityUtils.encryptPassword(userService.getDefaultPassword())); // 默认密码
        student.setStatus("0"); // 正常状态
        student.setSex("2"); // 未知
        student.setDeptId(systemDataQueryUtil.getStudentDept().getDeptId());

        // 设置学生角色
        Long[] roleIds = {systemDataQueryUtil.getStudentRole().getRoleId()};
        student.setRoleIds(roleIds);

        userService.save(student);

        UserStudentExt userStudentExt = new UserStudentExt();
        userStudentExt.setStudentId(String.valueOf(student.getUserId()));
        userStudentExt.setName(rowData.getStudentName());
        userStudentExt.setPhone(rowData.getStudentPhone());
        userStudentExt.setRemarks("课时导入创建的学生");
        userStudentExt.setDeleted(false);
        userStudentExtService.save(userStudentExt);


        log.debug("创建学生成功: {} ({})", rowData.getStudentName(), rowData.getStudentPhone());
        return student;
    }

    /**
     * 处理师生关系
     */
    private boolean processTeacherStudentRelation(SysUser teacher, SysUser student, CourseHoursImportDto.ExcelRowData rowData) {
        // 查找是否已存在相同的师生关系
        TeacherStudentRelation existingRelation = teacherStudentRelationService.lambdaQuery()
                .eq(TeacherStudentRelation::getTeacherId, String.valueOf(teacher.getUserId()))
                .eq(TeacherStudentRelation::getStudentId, String.valueOf(student.getUserId()))
                .eq(TeacherStudentRelation::getSubject, rowData.getSubject())
                .eq(TeacherStudentRelation::getSpecification, rowData.getSpecification())
                .eq(TeacherStudentRelation::getStatus, "active")
                .eq(TeacherStudentRelation::getSubject, rowData.getSubject())
                .eq(TeacherStudentRelation::getSpecification, rowData.getSpecification())
                .one();

        if (existingRelation != null) {
            log.debug("师生关系已存在: teacher={}, student={}, subject={}, courseType={}",
                    teacher.getUserId(), student.getUserId(), rowData.getSubject(), rowData.getSpecification());
            return false;
        }

        // 创建新的师生关系
        TeacherStudentRelation relation = new TeacherStudentRelation();
        relation.setId(IdUtil.getSnowflakeNextIdStr());
        relation.setTeacherId(String.valueOf(teacher.getUserId()));
        relation.setStudentId(String.valueOf(student.getUserId()));
        relation.setRelationType("teaching");
        relation.setSubject(rowData.getSubject());
        relation.setSpecification(rowData.getSpecification());
        relation.setStatus("active");
        relation.setStartDate(WssContext.now());
        relation.setCreateTime(WssContext.now());
        relation.setUpdateTime(WssContext.now());
        relation.setDeleted(false);

        boolean result = teacherStudentRelationService.save(relation);
        if (result) {
            log.debug("创建师生关系成功: teacher={} ({}), student={} ({}), subject={}, courseType={}",
                    teacher.getNickName(), teacher.getPhonenumber(),
                    student.getNickName(), student.getPhonenumber(),
                    rowData.getSubject(), rowData.getSpecification());
        }

        return result;
    }

    /**
     * 处理学生课时记录
     */
    private boolean processStudentCourseHours(SysUser student, CourseHoursImportDto.ExcelRowData rowData, String batchNo) {
        // 计算总课时和剩余课时
        BigDecimal totalHours = rowData.getTotalHours();
        BigDecimal remainingHours = rowData.getRemainingHours();

        // 每次导入都创建新的课时记录，不更新现有记录
        StudentCourseHours hours = studentCourseHoursService.createNewHoursRecordWithDetails(
                student.getUserId().toString(),
                rowData.getSubject(),
                rowData.getSpecification(),
                rowData.getNature(),
                totalHours,
                remainingHours,
                rowData.getPurchasedTotalHours(),
                rowData.getGiftTotalHours(),
                rowData.getPurchasedRemainingHours(),
                rowData.getGiftRemainingHours(),
                rowData.getUnitPrice(),
                batchNo);

        if (hours != null) {
            log.debug("创建课时记录成功: student={} ({}), subject={}, specification={}, batchNo={}, total={}, remaining={}, purchased={}, gift={}",
                    student.getNickName(), student.getPhonenumber(),
                    rowData.getSubject(), rowData.getSpecification(), batchNo,
                    totalHours, remainingHours, rowData.getPurchasedTotalHours(), rowData.getGiftTotalHours());
            return true;
        }

        return false;
    }

    /**
     * 数据验证结果
     */
    private static class ValidationResult {
        private final List<CourseHoursImportDto.ExcelRowData> validRows = new ArrayList<>();
        private final List<String> errors = new ArrayList<>();

        public void addValidRow(CourseHoursImportDto.ExcelRowData row) {
            validRows.add(row);
        }

        public void addError(String error) {
            errors.add(error);
        }

        public List<CourseHoursImportDto.ExcelRowData> getValidRows() {
            return validRows;
        }

        public List<String> getErrors() {
            return errors;
        }

        public boolean hasErrors() {
            return !errors.isEmpty();
        }
    }
}
