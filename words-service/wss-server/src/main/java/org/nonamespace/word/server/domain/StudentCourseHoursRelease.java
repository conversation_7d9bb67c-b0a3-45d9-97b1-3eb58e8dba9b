package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.server.entity.DataEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 学生课时释放记录实体类
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("student_course_hours_release")
public class StudentCourseHoursRelease extends DataEntity {

    /**
     * 课时包ID
     */
    private String courseHoursId;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 订单交易流水ID
     */
    private String orderTrxId;

    /**
     * 学生ID
     */
    private String studentId;

    /**
     * 释放的购买课时数 (保留2位小数)
     */
    private BigDecimal releasedPurchasedHours;

    /**
     * 释放的赠送课时数 (保留2位小数)
     */
    private BigDecimal releasedGiftHours;

    /**
     * 支付比例 (保留4位小数)
     */
    private BigDecimal paymentRatio;

    /**
     * 支付金额(分)
     */
    private Long paymentAmount;

    /**
     * 订单总金额(分)
     */
    private Long totalOrderAmount;

    /**
     * 释放时间
     */
    private Date releaseTime;

    /**
     * 释放类型：部分释放、最终释放
     */
    private String releaseType;

    /**
     * 备注
     */
    private String remark;
}
