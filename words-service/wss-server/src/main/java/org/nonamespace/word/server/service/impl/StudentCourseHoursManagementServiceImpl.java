package org.nonamespace.word.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;

import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.management.student.StudentCourseHoursDto;
import org.nonamespace.word.server.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 学生课时管理Service实现类
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
public class StudentCourseHoursManagementServiceImpl extends BaseServiceImpl<StudentCourseHours> implements IStudentCourseHoursManagementService {

    @Autowired
    private IStudentCourseHoursService studentCourseHoursService;

    @Autowired
    private IStudentCourseConsumptionService studentCourseConsumptionService;

    @Autowired
    private IStudentCourseHoursAdjustmentService adjustmentService;

    @Autowired
    private UserStudentExtService userStudentExtService;

    @Autowired
    private ITeacherProfileService teacherProfileService;

    @Autowired
    private ITeacherStudentRelationService teacherStudentRelationService;



    @Override
    public Page<StudentCourseHoursDto.CourseHoursResponse> queryStudentCourseHours(StudentCourseHoursDto.QueryRequest request) {
        Page<StudentCourseHours> page = new Page<>(request.getPageNum(), request.getPageSize());

        MPJLambdaWrapper<StudentCourseHours> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(StudentCourseHours.class)
                .leftJoin(UserStudentExt.class, UserStudentExt::getStudentId, StudentCourseHours::getStudentId)
                .eq(StudentCourseHours::getDeleted, false)
                .eq(StrUtil.isNotBlank(request.getSubject()), StudentCourseHours::getSubject, request.getSubject())
                .eq(StrUtil.isNotBlank(request.getSpecification()), StudentCourseHours::getSpecification, request.getSpecification())
                .eq(StrUtil.isNotBlank(request.getNature()), StudentCourseHours::getNature, request.getNature())
                .eq(StrUtil.isNotBlank(request.getStatus()), StudentCourseHours::getStatus, request.getStatus())
                .like(StrUtil.isNotBlank(request.getStudentName()), UserStudentExt::getName, request.getStudentName())
                .like(StrUtil.isNotBlank(request.getStudentPhone()), UserStudentExt::getPhone, request.getStudentPhone());

        // 如果有老师查询条件，需要通过师生关系表关联查询，并且要匹配学科和课型
        if (StrUtil.isNotBlank(request.getTeacherName()) || StrUtil.isNotBlank(request.getTeacherPhone())) {
            queryWrapper.leftJoin(TeacherStudentRelation.class, TeacherStudentRelation::getStudentId, StudentCourseHours::getStudentId,
                            ext -> ext.eq(TeacherStudentRelation::getDeleted, false)
                                    .eq(TeacherStudentRelation::getStatus, "active")
                                    .eq(TeacherStudentRelation::getSubject, StudentCourseHours::getSubject)
                                    .eq(TeacherStudentRelation::getSpecification, StudentCourseHours::getSpecification))
                    .leftJoin(TeacherProfile.class, TeacherProfile::getTeacherId, TeacherStudentRelation::getTeacherId)
                    .like(StrUtil.isNotBlank(request.getTeacherName()), TeacherProfile::getRealName, request.getTeacherName())
                    .like(StrUtil.isNotBlank(request.getTeacherPhone()), TeacherProfile::getPhonenumber, request.getTeacherPhone());
        }

        queryWrapper.orderByDesc(StudentCourseHours::getCreateTime);

        studentCourseHoursService.page(page, queryWrapper);

        // 获取学生信息
        List<String> studentIds = page.getRecords().stream()
                .map(StudentCourseHours::getStudentId)
                .distinct()
                .collect(Collectors.toList());

        Map<String, UserStudentExt> studentMap = getStudentMap(studentIds, request.getStudentName(), request.getStudentPhone());

        // 获取老师信息映射（根据学科和课型匹配）
        Map<String, String> studentTeacherInfoMap = getStudentTeacherInfoMapBySubjectAndSpec(studentIds, request.getSubject(), request.getSpecification(), request.getTeacherName(), request.getTeacherPhone());

        // 转换为响应对象
        List<StudentCourseHoursDto.CourseHoursResponse> responseList = page.getRecords().stream()
                .filter(hours -> studentMap.containsKey(hours.getStudentId()))
                .map(hours -> {
                    UserStudentExt student = studentMap.get(hours.getStudentId());
                    StudentCourseHoursDto.CourseHoursResponse response = new StudentCourseHoursDto.CourseHoursResponse();
                    response.setId(hours.getId());
                    response.setStudentId(hours.getStudentId());
                    response.setStudentName(student.getName());
                    response.setStudentPhone(student.getPhone());
                    response.setSubject(hours.getSubject());
                    response.setSpecification(hours.getSpecification());
                    response.setNature(hours.getNature());
                    response.setTotalHours(hours.getTotalHours());
                    response.setRemainingHours(hours.getRemainingHours());
                    response.setPurchasedHours(hours.getPurchasedHours());
                    response.setGiftHours(hours.getGiftHours());
                    response.setConsumedTotalHours(hours.getConsumedTotalHours());
                    response.setConsumedPurchasedHours(hours.getConsumedPurchasedHours());
                    response.setConsumedGiftHours(hours.getConsumedGiftHours());

                    // 计算剩余购买课时和剩余赠送课时
                    BigDecimal remainingPurchased = hours.getPurchasedHours().subtract(hours.getConsumedPurchasedHours());
                    BigDecimal remainingGift = hours.getGiftHours().subtract(hours.getConsumedGiftHours());
                    response.setRemainingPurchasedHours(remainingPurchased);
                    response.setRemainingGiftHours(remainingGift);

                    // 设置老师信息
                    response.setTeacherInfo(studentTeacherInfoMap.get(hours.getStudentId()));

                    response.setUnitPrice(hours.getUnitPrice());
                    response.setStatus(hours.getStatus());
                    response.setCreateTime(hours.getCreateTime());
                    response.setUpdateTime(hours.getUpdateTime());
                    return response;
                })
                .collect(Collectors.toList());

        Page<StudentCourseHoursDto.CourseHoursResponse> resultPage = new Page<>(request.getPageNum(), request.getPageSize());
        resultPage.setRecords(responseList);
        resultPage.setTotal(page.getTotal());

        return resultPage;
    }

    @Override
    public StudentCourseHoursDto.CourseHoursResponse getStudentCourseHoursDetail(String studentId, String subject, String specification) {
        StudentCourseHours hours = studentCourseHoursService.getByStudentAndSubjectAndType(studentId, subject, specification);
        if (hours == null) {
            return null;
        }

        // 获取学生信息
        UserStudentExt student = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getStudentId, studentId)
                .one();
        if (student == null) {
            return null;
        }

        StudentCourseHoursDto.CourseHoursResponse response = new StudentCourseHoursDto.CourseHoursResponse();
        response.setId(hours.getId());
        response.setStudentId(hours.getStudentId());
        response.setStudentName(student.getName());
        response.setStudentPhone(student.getPhone());
        response.setSubject(hours.getSubject());
        response.setSpecification(hours.getSpecification());
        response.setNature(hours.getNature());
        response.setTotalHours(hours.getTotalHours());
        response.setRemainingHours(hours.getRemainingHours());
        response.setPurchasedHours(hours.getPurchasedHours());
        response.setGiftHours(hours.getGiftHours());
        response.setConsumedTotalHours(hours.getConsumedTotalHours());
        response.setConsumedPurchasedHours(hours.getConsumedPurchasedHours());
        response.setConsumedGiftHours(hours.getConsumedGiftHours());

        // 计算剩余购买课时和剩余赠送课时
        BigDecimal remainingPurchased = hours.getPurchasedHours().subtract(hours.getConsumedPurchasedHours());
        BigDecimal remainingGift = hours.getGiftHours().subtract(hours.getConsumedGiftHours());
        response.setRemainingPurchasedHours(remainingPurchased);
        response.setRemainingGiftHours(remainingGift);

        response.setUnitPrice(hours.getUnitPrice());
        response.setStatus(hours.getStatus());
        response.setCreateTime(hours.getCreateTime());
        response.setUpdateTime(hours.getUpdateTime());

        return response;
    }

    @Override
    public StudentCourseHoursDto.CourseHoursResponse getCourseHoursById(String courseHoursId) {
        // 根据课时包ID获取课时包记录
        StudentCourseHours courseHours = studentCourseHoursService.getById(courseHoursId);
        if (courseHours == null) {
            throw new RuntimeException("课时包不存在");
        }

        // 构建响应对象
        StudentCourseHoursDto.CourseHoursResponse response = new StudentCourseHoursDto.CourseHoursResponse();
        response.setId(courseHours.getId());
        response.setStudentId(courseHours.getStudentId());
        response.setSubject(courseHours.getSubject());
        response.setSpecification(courseHours.getSpecification());
        response.setNature(courseHours.getNature());
        response.setTotalHours(courseHours.getTotalHours());
        response.setPurchasedHours(courseHours.getPurchasedHours());
        response.setGiftHours(courseHours.getGiftHours());
        response.setConsumedTotalHours(courseHours.getConsumedTotalHours());
        response.setConsumedPurchasedHours(courseHours.getConsumedPurchasedHours());
        response.setConsumedGiftHours(courseHours.getConsumedGiftHours());
        response.setRemainingHours(courseHours.getRemainingHours());

        // 计算剩余购买课时和剩余赠送课时
        BigDecimal remainingPurchased = courseHours.getPurchasedHours().subtract(courseHours.getConsumedPurchasedHours());
        BigDecimal remainingGift = courseHours.getGiftHours().subtract(courseHours.getConsumedGiftHours());
        response.setRemainingPurchasedHours(remainingPurchased);
        response.setRemainingGiftHours(remainingGift);
        response.setUnitPrice(courseHours.getUnitPrice());
        response.setStatus(courseHours.getStatus());
        response.setCreateTime(courseHours.getCreateTime());
        response.setUpdateTime(courseHours.getUpdateTime());

        return response;
    }

    @Override
    public boolean adjustStudentCourseHours(StudentCourseHoursDto.AdjustRequest request, String operatorId, String operatorName) {
        return studentCourseHoursService.adjustHoursByCourseHoursId(
                request.getCourseHoursId(),
                request.getPurchasedHoursAdjustment(),
                request.getGiftHoursAdjustment(),
                request.getUnitPrice(),
                request.getAdjustmentReason(),
                operatorId,
                operatorName
        );
    }

    @Override
    public boolean updateStudentCourseHours(StudentCourseHoursDto.UpdateRequest request, String operatorId, String operatorName) {
        try {
            // 获取课时包记录
            StudentCourseHours courseHours = studentCourseHoursService.getById(request.getCourseHoursId());
            if (courseHours == null) {
                log.warn("课时包不存在: courseHoursId={}", request.getCourseHoursId());
                return false;
            }

            // 数据校验
            if (request.getRemainingPurchasedHours().compareTo(request.getPurchasedHours()) > 0) {
                log.warn("剩余购买课时不能大于购买课时: remaining={}, total={}",
                        request.getRemainingPurchasedHours(), request.getPurchasedHours());
                return false;
            }

            if (request.getRemainingGiftHours().compareTo(request.getGiftHours()) > 0) {
                log.warn("剩余赠送课时不能大于赠送课时: remaining={}, total={}",
                        request.getRemainingGiftHours(), request.getGiftHours());
                return false;
            }

            // 记录调整前的数据
            BigDecimal beforeTotalHours = courseHours.getTotalHours();
            BigDecimal beforeRemainingHours = courseHours.getRemainingHours();
            BigDecimal beforePurchasedHours = courseHours.getPurchasedHours();
            BigDecimal beforeGiftHours = courseHours.getGiftHours();

            // 计算新的总课时和剩余课时
            BigDecimal newTotalHours = request.getPurchasedHours().add(request.getGiftHours());
            BigDecimal newRemainingHours = request.getRemainingPurchasedHours().add(request.getRemainingGiftHours());

            // 计算已消耗的课时
            BigDecimal newConsumedPurchasedHours = request.getPurchasedHours().subtract(request.getRemainingPurchasedHours());
            BigDecimal newConsumedGiftHours = request.getGiftHours().subtract(request.getRemainingGiftHours());
            BigDecimal newConsumedTotalHours = newConsumedPurchasedHours.add(newConsumedGiftHours);

            // 更新课时包数据
            courseHours.setPurchasedHours(request.getPurchasedHours());
            courseHours.setGiftHours(request.getGiftHours());
            courseHours.setTotalHours(newTotalHours);
            courseHours.setRemainingHours(newRemainingHours);
            courseHours.setConsumedPurchasedHours(newConsumedPurchasedHours);
            courseHours.setConsumedGiftHours(newConsumedGiftHours);
            courseHours.setConsumedTotalHours(newConsumedTotalHours);
            courseHours.setUnitPrice(request.getUnitPrice());
            courseHours.setUpdateTime(WssContext.now());

            // 保存更新
            studentCourseHoursService.updateById(courseHours);

            // 记录调整历史
            StudentCourseHoursAdjustment adjustment = new StudentCourseHoursAdjustment();
            adjustment.setId(IdUtil.getSnowflakeNextIdStr());
            adjustment.setCourseHoursId(request.getCourseHoursId());
            adjustment.setStudentId(request.getStudentId());
            adjustment.setSubject(request.getSubject());
            adjustment.setSpecification(request.getSpecification());
            adjustment.setNature(courseHours.getNature());

            // 计算调整类型和调整数
            BigDecimal totalAdjustment = newTotalHours.subtract(beforeTotalHours);
            String adjustmentType = totalAdjustment.compareTo(BigDecimal.ZERO) >= 0 ? "increase" : "decrease";

            adjustment.setAdjustmentType(adjustmentType);
            adjustment.setAdjustmentHours(totalAdjustment.abs());

            // 计算购买和赠送课时的调整数
            BigDecimal purchasedAdjustment = request.getPurchasedHours().subtract(beforePurchasedHours);
            BigDecimal giftAdjustment = request.getGiftHours().subtract(beforeGiftHours);

            adjustment.setPurchasedHoursAdjustment(purchasedAdjustment);
            adjustment.setGiftHoursAdjustment(giftAdjustment);

            // 设置调整前后的数据
            adjustment.setBeforeTotalHours(beforeTotalHours);
            adjustment.setAfterTotalHours(newTotalHours);
            adjustment.setBeforeRemainingHours(beforeRemainingHours);
            adjustment.setAfterRemainingHours(newRemainingHours);
            adjustment.setBeforePurchasedHours(beforePurchasedHours);
            adjustment.setAfterPurchasedHours(request.getPurchasedHours());
            adjustment.setBeforeGiftHours(beforeGiftHours);
            adjustment.setAfterGiftHours(request.getGiftHours());

            adjustment.setAdjustmentReason(request.getAdjustmentReason());
            adjustment.setOperatorId(operatorId);
            adjustment.setOperatorName(operatorName);
            adjustment.setAdjustmentTime(WssContext.now());
            adjustment.setCreateTime(WssContext.now());
            adjustment.setUpdateTime(WssContext.now());
            adjustment.setDeleted(false);

            adjustmentService.save(adjustment);

            log.info("课时更新成功: courseHoursId={}, newTotal={}, newRemaining={}, operator={}",
                    request.getCourseHoursId(), newTotalHours, newRemainingHours, operatorName);

            return true;
        } catch (Exception e) {
            log.error("课时更新失败: courseHoursId=" + request.getCourseHoursId(), e);
            return false;
        }
    }

    @Override
    public boolean addStudentCourseHours(StudentCourseHoursDto.AddCourseHoursRequest request, String operatorId, String operatorName) {
        try {
            // 验证购买课时和赠送课时至少有一项大于0
            if (request.getPurchasedHours().compareTo(BigDecimal.ZERO) <= 0 &&
                request.getGiftHours().compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("购买课时和赠送课时至少有一项必须大于0");
                return false;
            }

            // 验证单价大于等于0
            if (request.getUnitPrice().compareTo(BigDecimal.ZERO) < 0) {
                log.warn("单价不能小于0");
                return false;
            }

            // 验证学生是否存在
            UserStudentExt student = userStudentExtService.lambdaQuery()
                    .eq(UserStudentExt::getStudentId, request.getStudentId())
                    .one();
            if (student == null) {
                log.warn("学生不存在: studentId={}", request.getStudentId());
                return false;
            }

            // 计算总课时
            BigDecimal totalHours = request.getPurchasedHours().add(request.getGiftHours());

            // 生成批次号
            String batchNo = "ADD_" + System.currentTimeMillis();

            // 创建新的课时记录
            StudentCourseHours courseHours = studentCourseHoursService.createNewHoursRecord(
                    request.getStudentId(),
                    request.getSubject(),
                    request.getSpecification(),
                    request.getNature(),
                    totalHours,
                    totalHours, // 剩余课时等于总课时
                    request.getUnitPrice(),
                    batchNo
            );

            if (courseHours == null) {
                log.error("创建课时记录失败");
                return false;
            }

            // 更新课时记录的购买和赠送课时
            courseHours.setPurchasedHours(request.getPurchasedHours());
            courseHours.setGiftHours(request.getGiftHours());
            studentCourseHoursService.updateById(courseHours);

            // 创建调整历史记录
            StudentCourseHoursAdjustment adjustment = new StudentCourseHoursAdjustment();
            adjustment.setId(IdUtil.getSnowflakeNextIdStr());
            adjustment.setCourseHoursId(courseHours.getId());
            adjustment.setStudentId(request.getStudentId());
            adjustment.setSubject(request.getSubject());
            adjustment.setSpecification(request.getSpecification());
            adjustment.setNature(request.getNature());
            adjustment.setAdjustmentType("increase");
            adjustment.setAdjustmentHours(totalHours);
            adjustment.setPurchasedHoursAdjustment(request.getPurchasedHours());
            adjustment.setGiftHoursAdjustment(request.getGiftHours());
            adjustment.setBeforeTotalHours(BigDecimal.ZERO);
            adjustment.setAfterTotalHours(totalHours);
            adjustment.setBeforeRemainingHours(BigDecimal.ZERO);
            adjustment.setAfterRemainingHours(totalHours);
            adjustment.setBeforePurchasedHours(BigDecimal.ZERO);
            adjustment.setAfterPurchasedHours(request.getPurchasedHours());
            adjustment.setBeforeGiftHours(BigDecimal.ZERO);
            adjustment.setAfterGiftHours(request.getGiftHours());
            adjustment.setAdjustmentReason(request.getReason());
            adjustment.setOperatorId(operatorId);
            adjustment.setOperatorName(operatorName);
            adjustment.setAdjustmentTime(WssContext.now());
            adjustment.setCreateTime(WssContext.now());
            adjustment.setUpdateTime(WssContext.now());
            adjustment.setDeleted(false);

            adjustmentService.save(adjustment);

            log.info("新增课时成功: studentId={}, subject={}, specification={}, nature={}, totalHours={}, batchNo={}",
                    request.getStudentId(), request.getSubject(), request.getSpecification(),
                    request.getNature(), totalHours, batchNo);

            return true;
        } catch (Exception e) {
            log.error("新增课时失败", e);
            return false;
        }
    }

    @Override
    public Page<StudentCourseHoursDto.ConsumptionResponse> queryConsumptionRecords(StudentCourseHoursDto.ConsumptionQueryRequest request) {
        Page<StudentCourseConsumption> page = new Page<>(request.getPageNum(), request.getPageSize());

        MPJLambdaWrapper<StudentCourseConsumption> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(StudentCourseConsumption.class)
                .leftJoin(UserStudentExt.class, UserStudentExt::getStudentId, StudentCourseConsumption::getStudentId)
                .leftJoin(TeacherProfile.class, TeacherProfile::getTeacherId, StudentCourseConsumption::getTeacherId)
                .leftJoin(StudentCourseHours.class, StudentCourseHours::getId, StudentCourseConsumption::getCourseHoursId)
                .eq(StudentCourseConsumption::getDeleted, false)
                .eq(request.getStudentId() != null, StudentCourseConsumption::getStudentId, request.getStudentId())
                .eq(StrUtil.isNotBlank(request.getCourseHoursId()), StudentCourseConsumption::getCourseHoursId, request.getCourseHoursId())
                .eq(StrUtil.isNotBlank(request.getSubject()), StudentCourseConsumption::getSubject, request.getSubject())
                .eq(StrUtil.isNotBlank(request.getSpecification()), StudentCourseConsumption::getSpecification, request.getSpecification())
                .eq(StrUtil.isNotBlank(request.getStatus()), StudentCourseConsumption::getStatus, request.getStatus())
                .like(StrUtil.isNotBlank(request.getStudentName()), UserStudentExt::getName, request.getStudentName())
                .like(StrUtil.isNotBlank(request.getStudentPhone()), UserStudentExt::getPhone, request.getStudentPhone())
                .like(StrUtil.isNotBlank(request.getTeacherName()), TeacherProfile::getNickName, request.getTeacherName())
                .ge(request.getStartTime() != null, StudentCourseConsumption::getConsumptionTime, request.getStartTime())
                .le(request.getEndTime() != null, StudentCourseConsumption::getConsumptionTime, request.getEndTime())
                .orderByDesc(StudentCourseConsumption::getConsumptionTime);

        studentCourseConsumptionService.page(page, queryWrapper);

        // 获取学生、老师和课时记录信息
        List<String> studentIds = page.getRecords().stream()
                .map(StudentCourseConsumption::getStudentId)
                .distinct()
                .collect(Collectors.toList());

        List<String> teacherIds = page.getRecords().stream()
                .map(StudentCourseConsumption::getTeacherId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        List<String> courseHoursIds = page.getRecords().stream()
                .map(StudentCourseConsumption::getCourseHoursId)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        Map<String, UserStudentExt> studentMap = getStudentMap(studentIds, null, null);
        Map<String, TeacherProfile> teacherMap = getTeacherMap(teacherIds);
        Map<String, StudentCourseHours> courseHoursMap = getCourseHoursMap(courseHoursIds);

        // 转换为响应对象
        List<StudentCourseHoursDto.ConsumptionResponse> responseList = page.getRecords().stream()
                .map(consumption -> {
                    UserStudentExt student = studentMap.get(consumption.getStudentId());
                    TeacherProfile teacher = teacherMap.get(consumption.getTeacherId());
                    StudentCourseHours courseHours = courseHoursMap.get(consumption.getCourseHoursId());

                    StudentCourseHoursDto.ConsumptionResponse response = new StudentCourseHoursDto.ConsumptionResponse();
                    response.setId(consumption.getId());
                    response.setStudentId(consumption.getStudentId());
                    response.setStudentName(student != null ? student.getName() : null);
                    response.setStudentPhone(student != null ? student.getPhone() : null);
                    response.setSubject(consumption.getSubject());
                    response.setSpecification(consumption.getSpecification());
                    response.setNature(consumption.getNature());
                    response.setConsumedHours(consumption.getConsumedHours());
                    response.setConsumptionTime(consumption.getConsumptionTime());
                    response.setCourseId(consumption.getCourseId());
                    response.setCourseHoursId(consumption.getCourseHoursId());
                    response.setBatchNo(courseHours != null ? courseHours.getBatchNo() : null);
                    response.setTeacherId(consumption.getTeacherId());
                    response.setTeacherName(teacher != null ? teacher.getNickName() : null);
                    response.setRemark(consumption.getRemark());
                    response.setStatus(consumption.getStatus());
                    response.setCreateTime(consumption.getCreateTime());
                    return response;
                })
                .collect(Collectors.toList());

        Page<StudentCourseHoursDto.ConsumptionResponse> resultPage = new Page<>(request.getPageNum(), request.getPageSize());
        resultPage.setRecords(responseList);
        resultPage.setTotal(page.getTotal());

        return resultPage;
    }

    @Override
    public Page<StudentCourseHoursDto.AdjustmentHistoryResponse> queryAdjustmentHistory(StudentCourseHoursDto.AdjustmentHistoryQueryRequest request) {
        Page<StudentCourseHoursAdjustment> page = new Page<>(request.getPageNum(), request.getPageSize());

        MPJLambdaWrapper<StudentCourseHoursAdjustment> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(StudentCourseHoursAdjustment.class)
                .leftJoin(UserStudentExt.class, UserStudentExt::getStudentId, StudentCourseHoursAdjustment::getStudentId)
                .eq(StudentCourseHoursAdjustment::getDeleted, false)
                .eq(StrUtil.isNotBlank(request.getCourseHoursId()), StudentCourseHoursAdjustment::getCourseHoursId, request.getCourseHoursId())
                .eq(request.getStudentId() != null, StudentCourseHoursAdjustment::getStudentId, request.getStudentId())
                .eq(StrUtil.isNotBlank(request.getSubject()), StudentCourseHoursAdjustment::getSubject, request.getSubject())
                .eq(StrUtil.isNotBlank(request.getSpecification()), StudentCourseHoursAdjustment::getSpecification, request.getSpecification())
                .like(StrUtil.isNotBlank(request.getStudentName()), UserStudentExt::getName, request.getStudentName())
                .like(StrUtil.isNotBlank(request.getStudentPhone()), UserStudentExt::getPhone, request.getStudentPhone())
                .like(StrUtil.isNotBlank(request.getOperatorName()), StudentCourseHoursAdjustment::getOperatorName, request.getOperatorName())
                .ge(request.getStartTime() != null, StudentCourseHoursAdjustment::getAdjustmentTime, request.getStartTime())
                .le(request.getEndTime() != null, StudentCourseHoursAdjustment::getAdjustmentTime, request.getEndTime())
                .orderByDesc(StudentCourseHoursAdjustment::getAdjustmentTime);

        adjustmentService.page(page, queryWrapper);

        // 获取学生信息
        List<String> studentIds = page.getRecords().stream()
                .map(StudentCourseHoursAdjustment::getStudentId)
                .distinct()
                .collect(Collectors.toList());

        Map<String, UserStudentExt> studentMap = getStudentMap(studentIds, null, null);

        // 转换为响应对象
        List<StudentCourseHoursDto.AdjustmentHistoryResponse> responseList = page.getRecords().stream()
                .map(adjustment -> {
                    UserStudentExt student = studentMap.get(adjustment.getStudentId());

                    StudentCourseHoursDto.AdjustmentHistoryResponse response = new StudentCourseHoursDto.AdjustmentHistoryResponse();
                    response.setId(adjustment.getId());
                    response.setCourseHoursId(adjustment.getCourseHoursId());
                    response.setStudentId(adjustment.getStudentId());
                    response.setStudentName(student != null ? student.getName() : null);
                    response.setStudentPhone(student != null ? student.getPhone() : null);
                    response.setSubject(adjustment.getSubject());
                    response.setSpecification(adjustment.getSpecification());
                    response.setNature(adjustment.getNature());
                    response.setAdjustmentType(adjustment.getAdjustmentType());
                    response.setAdjustmentHours(adjustment.getAdjustmentHours());
                    response.setPurchasedHoursAdjustment(adjustment.getPurchasedHoursAdjustment());
                    response.setGiftHoursAdjustment(adjustment.getGiftHoursAdjustment());
                    response.setBeforeTotalHours(adjustment.getBeforeTotalHours());
                    response.setAfterTotalHours(adjustment.getAfterTotalHours());
                    response.setBeforeRemainingHours(adjustment.getBeforeRemainingHours());
                    response.setAfterRemainingHours(adjustment.getAfterRemainingHours());
                    response.setBeforePurchasedHours(adjustment.getBeforePurchasedHours());
                    response.setAfterPurchasedHours(adjustment.getAfterPurchasedHours());
                    response.setBeforeGiftHours(adjustment.getBeforeGiftHours());
                    response.setAfterGiftHours(adjustment.getAfterGiftHours());
                    response.setAdjustmentReason(adjustment.getAdjustmentReason());
                    response.setOperatorId(adjustment.getOperatorId());
                    response.setOperatorName(adjustment.getOperatorName());
                    response.setAdjustmentTime(adjustment.getAdjustmentTime());
                    response.setCreateTime(adjustment.getCreateTime());
                    return response;
                })
                .collect(Collectors.toList());

        Page<StudentCourseHoursDto.AdjustmentHistoryResponse> resultPage = new Page<>(request.getPageNum(), request.getPageSize());
        resultPage.setRecords(responseList);
        resultPage.setTotal(page.getTotal());

        return resultPage;
    }

    /**
     * 获取学生信息映射
     */
    private Map<String, UserStudentExt> getStudentMap(List<String> studentIds, String studentName, String studentPhone) {

        return userStudentExtService.lambdaQuery()
                .in(CollUtil.isNotEmpty(studentIds), UserStudentExt::getStudentId, studentIds)
                .like(StrUtil.isNotBlank(studentName), UserStudentExt::getName, studentName)
                .like(StrUtil.isNotBlank(studentPhone), UserStudentExt::getPhone, studentPhone)
                .list().stream().collect(Collectors.toMap(
                        UserStudentExt::getStudentId,
                        Function.identity()
                ));
    }

    /**
     * 获取老师信息映射
     */
    private Map<String, TeacherProfile> getTeacherMap(List<String> teacherIds) {
        return teacherProfileService.lambdaQuery()
                .in(CollUtil.isNotEmpty(teacherIds), TeacherProfile::getTeacherId, teacherIds)
                .list().stream().collect(Collectors.toMap(TeacherProfile::getTeacherId, Function.identity()));
    }

    /**
     * 获取学生对应的老师信息映射（根据学科和课型匹配）
     * 返回格式：学生ID -> 老师信息（姓名(手机号)）
     */
    private Map<String, String> getStudentTeacherInfoMapBySubjectAndSpec(List<String> studentIds, String subject, String specification, String teacherName, String teacherPhone) {
        if (CollUtil.isEmpty(studentIds)) {
            return new HashMap<>();
        }

        // 构建查询条件
        var relationQuery = teacherStudentRelationService.lambdaQuery()
                .in(TeacherStudentRelation::getStudentId, studentIds)
                .eq(TeacherStudentRelation::getDeleted, false)
                .eq(TeacherStudentRelation::getStatus, "active");

        // 如果有学科和课型条件，添加匹配
        if (StrUtil.isNotBlank(subject)) {
            relationQuery.eq(TeacherStudentRelation::getSubject, subject);
        }
        if (StrUtil.isNotBlank(specification)) {
            relationQuery.eq(TeacherStudentRelation::getSpecification, specification);
        }

        List<TeacherStudentRelation> relations = relationQuery.list();

        // 获取所有相关的老师ID
        List<String> teacherIds = relations.stream()
                .map(TeacherStudentRelation::getTeacherId)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(teacherIds)) {
            return new HashMap<>();
        }

        // 获取老师信息（带过滤条件）
        var teacherQuery = teacherProfileService.lambdaQuery()
//                .eq(TeacherProfile::getStatus, "active")
                .in(TeacherProfile::getTeacherId, teacherIds);

        if (StrUtil.isNotBlank(teacherName)) {
            teacherQuery.like(TeacherProfile::getRealName, teacherName);
        }
        if (StrUtil.isNotBlank(teacherPhone)) {
            teacherQuery.like(TeacherProfile::getPhonenumber, teacherPhone);
        }

        List<TeacherProfile> teachers = teacherQuery.list();
        Map<String, TeacherProfile> teacherMap = teachers.stream()
                .collect(Collectors.toMap(TeacherProfile::getTeacherId, Function.identity()));

        // 构建学生ID到老师信息的映射
        Map<String, List<String>> studentTeachersMap = new HashMap<>();
        for (TeacherStudentRelation relation : relations) {
            TeacherProfile teacher = teacherMap.get(relation.getTeacherId());
            if (teacher != null) {
                String teacherInfo = teacher.getRealName();
                if (StrUtil.isNotBlank(teacher.getPhonenumber())) {
                    teacherInfo += "(" + teacher.getPhonenumber() + ")";
                }

                studentTeachersMap.computeIfAbsent(relation.getStudentId(), k -> new ArrayList<>()).add(teacherInfo);
            }
        }

        // 将多个老师信息合并为一个字符串
        Map<String, String> result = new HashMap<>();
        for (String studentId : studentIds) {
            List<String> teacherInfos = studentTeachersMap.get(studentId);
            if (CollUtil.isNotEmpty(teacherInfos)) {
                result.put(studentId, String.join(", ", teacherInfos));
            } else {
                result.put(studentId, "");
            }
        }

        return result;
    }

    /**
     * 获取课时记录信息映射
     */
    private Map<String, StudentCourseHours> getCourseHoursMap(List<String> courseHoursIds) {
        if (courseHoursIds.isEmpty()) {
            return Map.of();
        }

        List<StudentCourseHours> courseHoursList = studentCourseHoursService.listByIds(courseHoursIds);

        return courseHoursList.stream()
                .collect(Collectors.toMap(StudentCourseHours::getId, hours -> hours));
    }

    @Override
    public List<StudentCourseHoursDto.ExportResponse> exportStudentCourseHours(StudentCourseHoursDto.QueryRequest request) {
        MPJLambdaWrapper<StudentCourseHours> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(StudentCourseHours.class)
                .leftJoin(UserStudentExt.class, UserStudentExt::getStudentId, StudentCourseHours::getStudentId)
                .eq(StudentCourseHours::getDeleted, false)
                .eq(StrUtil.isNotBlank(request.getSubject()), StudentCourseHours::getSubject, request.getSubject())
                .eq(StrUtil.isNotBlank(request.getSpecification()), StudentCourseHours::getSpecification, request.getSpecification())
                .eq(StrUtil.isNotBlank(request.getNature()), StudentCourseHours::getNature, request.getNature())
                .eq(StrUtil.isNotBlank(request.getStatus()), StudentCourseHours::getStatus, request.getStatus())
                .like(StrUtil.isNotBlank(request.getStudentName()), UserStudentExt::getName, request.getStudentName())
                .like(StrUtil.isNotBlank(request.getStudentPhone()), UserStudentExt::getPhone, request.getStudentPhone());

        // 如果有老师查询条件，需要通过师生关系表关联查询，并且要匹配学科和课型
        if (StrUtil.isNotBlank(request.getTeacherName()) || StrUtil.isNotBlank(request.getTeacherPhone())) {
            queryWrapper.leftJoin(TeacherStudentRelation.class, TeacherStudentRelation::getStudentId, StudentCourseHours::getStudentId,
                            ext -> ext.eq(TeacherStudentRelation::getDeleted, false)
                                    .eq(TeacherStudentRelation::getStatus, "active")
                                    .eq(TeacherStudentRelation::getSubject, StudentCourseHours::getSubject)
                                    .eq(TeacherStudentRelation::getSpecification, StudentCourseHours::getSpecification))
                    .leftJoin(TeacherProfile.class, TeacherProfile::getTeacherId, TeacherStudentRelation::getTeacherId,
                            ext -> ext.eq(TeacherProfile::getDeleted, false)
//                                    .eq(TeacherProfile::getStatus, "active")
                    )
                    .like(StrUtil.isNotBlank(request.getTeacherName()), TeacherProfile::getRealName, request.getTeacherName())
                    .like(StrUtil.isNotBlank(request.getTeacherPhone()), TeacherProfile::getPhonenumber, request.getTeacherPhone());
        }

        queryWrapper.orderByDesc(StudentCourseHours::getCreateTime);

        List<StudentCourseHours> courseHoursList = studentCourseHoursService.list(queryWrapper);

        // 获取学生信息
        List<String> studentIds = courseHoursList.stream()
                .map(StudentCourseHours::getStudentId)
                .distinct()
                .collect(Collectors.toList());

        Map<String, UserStudentExt> studentMap = getStudentMap(studentIds, request.getStudentName(), request.getStudentPhone());

        // 获取老师信息映射（根据学科和课型匹配）
        Map<String, String> studentTeacherInfoMap = getStudentTeacherInfoMapBySubjectAndSpec(studentIds, request.getSubject(), request.getSpecification(), request.getTeacherName(), request.getTeacherPhone());

        // 转换为导出响应对象
        return courseHoursList.stream()
                .map(courseHours -> {
                    UserStudentExt student = studentMap.get(courseHours.getStudentId());

                    StudentCourseHoursDto.ExportResponse response = new StudentCourseHoursDto.ExportResponse();
                    response.setStudentName(student != null ? student.getName() : null);
                    response.setStudentPhone(student != null ? student.getPhone() : null);
                    response.setSubject(courseHours.getSubject());
                    response.setSpecification(courseHours.getSpecification());
                    response.setNature(courseHours.getNature());

                    // 设置老师信息
                    response.setTeacherInfo(studentTeacherInfoMap.get(courseHours.getStudentId()));
                    response.setTotalHours(courseHours.getTotalHours());
                    response.setPurchasedHours(courseHours.getPurchasedHours());
                    response.setGiftHours(courseHours.getGiftHours());

                    // 计算剩余购买课时和剩余赠送课时
                    BigDecimal remainingPurchasedHours = courseHours.getPurchasedHours().subtract(courseHours.getConsumedPurchasedHours());
                    BigDecimal remainingGiftHours = courseHours.getGiftHours().subtract(courseHours.getConsumedGiftHours());

                    response.setRemainingPurchasedHours(remainingPurchasedHours);
                    response.setRemainingGiftHours(remainingGiftHours);
                    response.setRemainingHours(courseHours.getRemainingHours());
                    response.setConsumedTotalHours(courseHours.getConsumedTotalHours());
                    response.setUnitPrice(courseHours.getUnitPrice());
                    response.setBatchNo(courseHours.getBatchNo());
                    response.setCreateTime(courseHours.getCreateTime());

                    return response;
                })
                .collect(Collectors.toList());
    }

    @Override
    public boolean recordCourseConsumption(StudentCourseHoursDto.RecordConsumptionRequest request, String operatorId, String operatorName) {
        try {
            // 获取课时包记录
            StudentCourseHours courseHours = studentCourseHoursService.getById(request.getCourseHoursId());
            if (courseHours == null) {
                log.warn("课时包不存在: courseHoursId={}", request.getCourseHoursId());
                return false;
            }

            // 检查剩余课时是否足够
            if (courseHours.getRemainingHours().compareTo(request.getConsumedHours()) < 0) {
                log.warn("剩余课时不足: remaining={}, consume={}",
                        courseHours.getRemainingHours(), request.getConsumedHours());
                return false;
            }

            // 使用课时服务进行课消
            boolean consumeResult = studentCourseHoursService.consumeHours(
                    request.getStudentId(),
                    request.getSubject(),
                    request.getSpecification(),
                    request.getNature(),
                    request.getConsumedHours(),
                    request.getCourseId(),
                    request.getTeacherId(),
                    request.getRemark()
            );

            if (consumeResult) {
                log.info("课消录入成功: courseHoursId={}, consumedHours={}, operator={}",
                        request.getCourseHoursId(), request.getConsumedHours(), operatorName);
                return true;
            } else {
                log.warn("课消录入失败: courseHoursId={}", request.getCourseHoursId());
                return false;
            }

        } catch (Exception e) {
            log.error("课消录入失败: courseHoursId=" + request.getCourseHoursId(), e);
            return false;
        }
    }
}
