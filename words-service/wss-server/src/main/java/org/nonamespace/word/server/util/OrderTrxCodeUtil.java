package org.nonamespace.word.server.util;

import lombok.RequiredArgsConstructor;
import org.nonamespace.word.server.constants.OrderConstants;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@RequiredArgsConstructor
public class OrderTrxCodeUtil {

    // 日期格式
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSSSS");
    // 序列号，防止重复
    private static final AtomicInteger SEQUENCE = new AtomicInteger(0);

    private final StringRedisTemplate redisTemplate;

    /**
     * 生成订单编号
     * @return
     */
    public String generalOrderCode(String trxType) {

        LocalDateTime now = LocalDateTime.now();
        String timeStr = DATE_FORMATTER.format(now);
        int seq = SEQUENCE.getAndIncrement() % 10000; // 4位序列号

        String orderCode = switch (trxType) {
            case OrderConstants.TrxType.PAY -> String.format("%s%s%04d", "P", timeStr, seq);
            case OrderConstants.TrxType.REFUND -> String.format("%s%s%04d", "R", timeStr, seq);
            case OrderConstants.TrxType.CANCEL -> String.format("%s%s%04d", "C", timeStr, seq);
            default -> String.format("%s%s%04d", "NAN", timeStr, seq);
        };

        // 判断5s内，当前订单是否重复了。
        String key = "order:order_code:" + orderCode;
        if (redisTemplate.hasKey(key)) {
            // 订单重复了，重新生成一个
            return generalOrderCode(trxType);
        }
        redisTemplate.opsForValue().set(key, orderCode, 5, TimeUnit.SECONDS);
        return orderCode;
    }


    /**
     * 交易流水号
     * @return
     */
    public String generalTrxSeq() {
        LocalDateTime now = LocalDateTime.now();
        String timeStr = DATE_FORMATTER.format(now);
        long nanoTime = System.nanoTime();

        // 判断5s内，当前订单是否重复了。
        String seq = timeStr + nanoTime;
        String key = "order:trx_seq:" + seq;
        if (redisTemplate.hasKey(key)) {
            return generalTrxSeq();
        }
        redisTemplate.opsForValue().set(key, seq, 5, TimeUnit.SECONDS);
        return seq;
    }

}
