package org.nonamespace.word.server.dto.product;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 产品相关DTO（课时包）
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public class ProductDto {

    /**
     * 产品基础信息响应
     */
    @Data
    public static class BasicResp {
        private String id;
        private String name;
        private String description;
        private String subject;
        private String courseType;
        private List<String> applicableGrades;
        private Long unitPrice;
        private Integer quantity;
        private Boolean hasBonusHours;
        private Integer bonusHoursQuantity;
        private Boolean hasMaterialFee;
        private Long materialFee;
        private Long originalPrice;
        private Long sellingPrice;
        private String status;
        private String coverImage;
        private Integer sortOrder;
        private Integer stock;
        private Boolean stockLimited;
        private Integer salesCount;
        private List<String> tags;
        private Date createTime;
        private Date updateTime;
    }

    /**
     * 产品详细信息响应
     */
    @Data
    public static class DetailResp {
        private String id;
        private String name;
        private String description;
        private String subject;
        private String courseType;
        private List<String> applicableGrades;
        private Long unitPrice;
        private Integer quantity;
        private Boolean hasBonusHours;
        private Integer bonusHoursQuantity;
        private Boolean hasMaterialFee;
        private Long materialFee;
        private Long originalPrice;
        private Long sellingPrice;
        private String status;
        private String coverImage;
        private List<String> detailImages;
        private Integer sortOrder;
        private String remark;
        private Integer stock;
        private Boolean stockLimited;
        private Integer salesCount;
        private List<String> tags;
        private String content;
        private Date createTime;
        private Date updateTime;
    }

    /**
     * 创建产品请求
     */
    @Data
    public static class CreateReq {
        @NotBlank(message = "产品名称不能为空")
        private String name;

        private String description;

        @NotBlank(message = "学科不能为空")
        private String subject;

        private String courseType;

        @NotNull(message = "适用年级不能为空")
        private List<String> applicableGrades;

        @NotNull(message = "单价不能为空")
        @Min(value = 1, message = "单价必须大于0")
        private Long unitPrice;

        @NotNull(message = "数量不能为空")
        @Min(value = 1, message = "数量必须大于0")
        private Integer quantity;

        private Boolean hasBonusHours = false;
        private Integer bonusHoursQuantity = 0;
        private Boolean hasMaterialFee = false;
        private Long materialFee = 0L;


        @NotNull(message = "原价不能为空")
        @Min(value = 1, message = "原价必须大于0")
        private Long originalPrice = 0L;

        @NotNull(message = "售价不能为空")
        @Min(value = 1, message = "售价必须大于0")
        private Long sellingPrice;

        @NotBlank(message = "产品状态不能为空")
        private String status;

        private String coverImage;
        private List<String> detailImages;
        private Integer sortOrder;
        private String remark;
        private Integer stock;
        private Boolean stockLimited;
        private List<String> tags;
        private String content;
    }

    /**
     * 更新产品请求
     */
    @Data
    public static class UpdateReq {
        @NotBlank(message = "产品ID不能为空")
        private String id;

        @NotBlank(message = "产品名称不能为空")
        private String name;

        private String description;

        @NotBlank(message = "学科不能为空")
        private String subject;

        private String courseType;

        @NotNull(message = "适用年级不能为空")
        private List<String> applicableGrades;

        @NotNull(message = "单价不能为空")
        @Min(value = 1, message = "单价必须大于0")
        private Long unitPrice;

        @NotNull(message = "数量不能为空")
        @Min(value = 1, message = "数量必须大于0")
        private Integer quantity;

        private Boolean hasBonusHours = false;
        private Integer bonusHoursQuantity = 0;
        private Boolean hasMaterialFee = false;
        private Long materialFee = 0L;

        @NotNull(message = "售价不能为空")
        @Min(value = 1, message = "售价必须大于0")
        private Long sellingPrice;

        @NotBlank(message = "产品状态不能为空")
        private String status;

        private String coverImage;
        private List<String> detailImages;
        private Integer sortOrder;
        private String remark;
        private Integer stock;
        private Boolean stockLimited;
        private List<String> tags;
        private String content;
    }

    /**
     * 分页查询产品请求
     */
    @Data
    public static class GetListReq {
        private String name;
        private String subject;
        private String courseType;
        private String applicableGrade;
        private String status;
        private String tag;
        private Long minPrice;
        private Long maxPrice;
        private Integer pageNum = 1;
        private Integer pageSize = 10;
        private String orderBy = "sort_order";
        private String orderDirection = "ASC";
    }

    /**
     * 产品统计信息
     */
    @Data
    public static class StatisticsResp {
        private Long totalProducts;
        private Long onlineProducts;
        private Long offlineProducts;
        private Long totalSales;
        private Long totalRevenue;
    }
}
