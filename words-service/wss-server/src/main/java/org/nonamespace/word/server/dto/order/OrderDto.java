package org.nonamespace.word.server.dto.order;

import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单相关DTO
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public class OrderDto {

    /**
     * 订单基础信息响应
     */
    @Data
    public static class BasicResp {
        private String id;
        private String no;
        private String source;
        private String body;
        private String orderStatus;
        private Long totalAmt;
        private String salerId;
        private String salerName;
        private String studentId;
        private String studentName;
        private Long amtPaid;
        private Long amtUnpaid;
        private String signStatus;
        private String trxMethod;
        private Date createTime;
        private Date updateTime;
    }

    /**
     * 订单详细信息响应
     */
    @Data
    public static class DetailResp {
        private String id;
        private String no;
        private String source;
        private String body;
        private String orderStatus;
        private Long totalAmt;
        private String salerId;
        private String salerName;
        private String studentId;
        private String studentName;
        private Map<String, Object> originOrder;
        private Long amtPaid;
        private Long amtUnpaid;
        private String signStatus;
        private String trxMethod;
        private String remark;
        private Date createTime;
        private Date updateTime;
        private List<OrderTrxResp> transactions;
    }

    /**
     * 订单交易流水响应
     */
    @Data
    public static class OrderTrxResp {
        private String id;
        private String orderId;
        private String orderNo;
        private String cusTrxSeq;
        private Integer trxIdx;
        private String payType;
        private Long trxAmt;
        private String trxType;
        private String trxStatus;
        private Integer trxId;
        private Date createTime;
        private Date updateTime;
    }

    /**
     * 分页查询订单请求
     */
    @Data
    public static class GetListReq {
        private String orderNo;
        private String studentId;
        private String studentName;
        private String salerId;
        private String salerName;
        private String orderStatus;
        private String trxMethod;
        private Date createTimeStart;
        private Date createTimeEnd;
        private Integer pageNum = 1;
        private Integer pageSize = 10;
        private String orderBy = "create_time";
        private String orderDirection = "DESC";
    }

    /**
     * 支付请求
     */
    @Data
    public static class PayReq {
        private String orderTrxId;
        private String payType;
    }

    /**
     * 支付响应
     */
    @Data
    public static class PayResp {
        private String payInfo;
        private String qrCodeBase64;
        private String payUrl;
    }
}
