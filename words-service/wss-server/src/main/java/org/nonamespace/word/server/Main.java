package org.nonamespace.word.server;

import java.io.File;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

public class Main {
    public static void main(String[] args) {
        String dirPath = "/Users/<USER>/alidisk/2222";
        File dir = new File(dirPath);
        if (!dir.isDirectory()) {
            System.out.println("目录不存在: " + dirPath);
            return;
        }

        Set<String> names = new HashSet<>();
        for (File file : Objects.requireNonNull(dir.listFiles())) {
            String name = file.getName().toLowerCase().trim();
            name = name.replace(".mp4", "");
            name = name.substring(name.indexOf(".") + 1);
            name = name.replaceAll("[^a-zA-Z0-9]", "_");
            if(names.contains(name)) {
                throw new RuntimeException("文件名重复: " + name + "，请检查目录下的文件名是否有重复。");
            }
            names.add(name);
        }


        for (File file : Objects.requireNonNull(dir.listFiles())) {
            String name = file.getName().toLowerCase();
            name = name.toLowerCase();
            name = name.replace(".mp4", "");
            name = name.substring(name.indexOf(".") + 1);
            name = name.replaceAll("[^a-zA-Z0-9]", "_");
            name = name + ".mp4";


            File newFile = new File(dir, name.toLowerCase());
            boolean success = file.renameTo(newFile);
            if (success) {
                System.out.println("重命名: " + name + " -> " + newFile.getName());
            } else {
                System.out.println("重命名失败: " + name);
            }
        }

    }
}
