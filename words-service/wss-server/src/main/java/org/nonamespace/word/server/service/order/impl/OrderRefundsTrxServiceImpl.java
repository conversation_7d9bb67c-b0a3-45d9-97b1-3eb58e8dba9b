package org.nonamespace.word.server.service.order.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.order.OrderRefundsTrx;
import org.nonamespace.word.server.mapper.order.OrderRefundsTrxMapper;
import org.nonamespace.word.server.service.order.IOrderRefundsTrxService;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderRefundsTrxServiceImpl extends MPJBaseServiceImpl<OrderRefundsTrxMapper, OrderRefundsTrx> implements IOrderRefundsTrxService {
}
