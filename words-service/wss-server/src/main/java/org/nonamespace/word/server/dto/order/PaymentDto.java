package org.nonamespace.word.server.dto.order;

import lombok.Data;

/**
 * 支付相关DTO
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public class PaymentDto {

    /**
     * 支付请求
     */
    @Data
    public static class PayReq {
        private String orderTrxId;
        private String payType;
    }

    /**
     * 支付响应
     */
    @Data
    public static class PayResp {
        private String payInfo;
        private String qrCodeBase64;
        private String payUrl;
        private String orderTrxId;
        private String cusTrxSeq;
        private Long trxAmt;
    }

    /**
     * 二维码生成请求
     */
    @Data
    public static class QRCodeReq {
        private String orderTrxId;
        private Integer width = 200;
        private Integer height = 200;
    }

    /**
     * 二维码生成响应
     */
    @Data
    public static class QRCodeResp {
        private String qrCodeBase64;
        private String payUrl;
    }

    /**
     * 微信模板消息请求
     */
    @Data
    public static class WechatTemplateReq {
        private String orderTrxId;
        private String studentId;
        private String templateId;
    }

    /**
     * 微信模板消息响应
     */
    @Data
    public static class WechatTemplateResp {
        private boolean success;
        private String message;
        private String msgId;
    }
}
