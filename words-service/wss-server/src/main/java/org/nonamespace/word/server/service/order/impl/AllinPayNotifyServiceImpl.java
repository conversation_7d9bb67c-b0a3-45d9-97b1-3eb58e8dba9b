package org.nonamespace.word.server.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.constants.OrderConstants;
import org.nonamespace.word.server.domain.order.OrderRefunds;
import org.nonamespace.word.server.domain.order.OrderRefundsTrx;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.domain.order.OrdersTrx;
import org.nonamespace.word.server.service.order.*;
import org.nonamespace.word.thirdpart.allinpay.constants.AllinPayConstants;
import org.nonamespace.word.thirdpart.allinpay.dto.AllinPayNotifyDto;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 通联支付回调通知服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AllinPayNotifyServiceImpl implements IAllinPayNotifyService {
    
    private final StringRedisTemplate stringRedisTemplate;
    private final IOrdersTrxService ordersTrxService;
    private final IOrdersService ordersService;
    private final IOrderRefundsService orderRefundsService;
    private final IOrderRefundsTrxService orderRefundsTrxService;

    /**
     * Redis幂等性控制Key前缀
     * 格式: notify:processed:{订单号}:{通联交易号}
     */
    private static final String NOTIFY_PROCESSED_KEY = "order:pay:locked:%s:%s:%s";

    /**
     * 幂等性控制过期时间（小时）
     */
    private static final int IDEMPOTENT_EXPIRE_HOURS = 24;
    
    @Override
    public boolean processPaymentNotify(AllinPayNotifyDto notifyDto) {
        String idempotentKey = String.format(NOTIFY_PROCESSED_KEY,
            notifyDto.getCusorderid(), notifyDto.getTrxid(), AllinPayConstants.AllinPayType.PAY_NOTIFY);

        try {
            // 幂等性检查：如果已经处理过，直接返回成功
            if (stringRedisTemplate.hasKey(idempotentKey)) {
                log.info("[通联支付回调] 订单已处理过，跳过重复处理, 订单号: {}, 通联交易号: {}",
                    notifyDto.getCusorderid(), notifyDto.getTrxid());
                return true;
            }

            log.info("[通联支付回调] 开始处理业务逻辑, 订单号: {}, 交易状态: {}, 交易金额: {}",
                notifyDto.getCusorderid(), notifyDto.getTrxstatus(), notifyDto.getTrxamt());

            boolean processResult = false;

            // 根据交易状态处理不同的业务逻辑
            if (notifyDto.isSuccess()) {
                // 交易成功处理逻辑
                processResult = handleSuccessPayment(notifyDto);
            } else if (notifyDto.isProcessing()) {
                // 交易处理中逻辑
                processResult = handleProcessingPayment(notifyDto);
            } else {
                // 交易失败处理逻辑
                processResult = handleFailedPayment(notifyDto);
            }

            // 只有处理成功才设置幂等性标记
            if (processResult) {
                // 设置幂等性标记，防止重复处理
                stringRedisTemplate.opsForValue().set(idempotentKey,
                    DateUtil.now(), IDEMPOTENT_EXPIRE_HOURS, TimeUnit.HOURS);
                log.info("[通联支付回调] 业务处理成功，已设置幂等性标记, 订单号: {}", notifyDto.getCusorderid());
            }

            return processResult;

        } catch (Exception e) {
            log.error("[通联支付回调] 业务处理异常, 订单号: {}", notifyDto.getCusorderid(), e);
            return false;
        }
    }

    @Override
    public boolean processRefundNotify(AllinPayNotifyDto notifyDto) {
        String idempotentKey = String.format(NOTIFY_PROCESSED_KEY,
                notifyDto.getCusorderid(), notifyDto.getTrxid(), AllinPayConstants.AllinPayType.REFUND);

        try {
            // 幂等性检查：如果已经处理过，直接返回成功
            if (stringRedisTemplate.hasKey(idempotentKey)) {
                log.info("[通联退款回调] 订单已处理过，跳过重复处理, 订单号: {}, 通联交易号: {}",
                        notifyDto.getCusorderid(), notifyDto.getTrxid());
                return true;
            }

            log.info("[通联退款回调] 开始处理业务逻辑, 订单号: {}, 交易状态: {}, 交易金额: {}",
                    notifyDto.getCusorderid(), notifyDto.getTrxstatus(), notifyDto.getTrxamt());

            boolean processResult;

            // 根据交易状态处理不同的业务逻辑
            if (notifyDto.isSuccess()) {
                // 交易成功处理逻辑
                processResult = handleSuccessRefund(notifyDto);
            } else if (notifyDto.isProcessing()) {
                // 交易处理中逻辑
                processResult = handleProcessingRefund(notifyDto);
            } else {
                // 交易失败处理逻辑
                processResult = handleFailedRefund(notifyDto);
            }

            // 只有处理成功才设置幂等性标记
            if (processResult) {
                // 设置幂等性标记，防止重复处理
                stringRedisTemplate.opsForValue().set(idempotentKey,
                        DateUtil.now(), IDEMPOTENT_EXPIRE_HOURS, TimeUnit.HOURS);
                log.info("[通联退款回调] 业务处理成功，已设置幂等性标记, 订单号: {}", notifyDto.getCusorderid());
            }

            return processResult;

        } catch (Exception e) {
            log.error("[通联退款回调] 业务处理异常, 订单号: {}", notifyDto.getCusorderid(), e);
            return false;
        }
    }

    private boolean handleFailedRefund(AllinPayNotifyDto notifyDto) {
        return false;
    }

    private boolean handleProcessingRefund(AllinPayNotifyDto notifyDto) {
        return false;
    }


    /**
     * 处理成功退款
     * @param notifyDto
     * @return
     */
    private boolean handleSuccessRefund(AllinPayNotifyDto notifyDto) {
        String cusorderid = notifyDto.getCusorderid();
        log.info("[通联退款回调] 处理成功支付, 商户流水号: {}, 通联交易号: {}, 渠道交易号: {}",
                cusorderid, notifyDto.getTrxid(), notifyDto.getChnltrxid());

        try {
            List<OrderRefundsTrx> orderRefundsTrxs = orderRefundsTrxService.lambdaQuery()
                    .eq(OrderRefundsTrx::getRefundTrxId, notifyDto.getTrxid()).list();
            if(CollUtil.isEmpty(orderRefundsTrxs)) {
                log.error("[通联退款回调] 户流水号不存在, 商户流水号: {}", cusorderid);
                return false;
            }
            OrderRefundsTrx orderRefundsTrx = orderRefundsTrxs.getFirst();
            // 查看交易流水状态
            if(!orderRefundsTrx.getRefundStatus().equalsIgnoreCase(OrderConstants.RefundStatus.WAIT_REFUND)) {
                log.error("[通联退款回调] 当前交易流水状有误, 商户流水号: {}", cusorderid);
                return false;
            }

            // 更新退款记录状态
            // 更新orderRefundsTrx
            LambdaUpdateWrapper<OrderRefundsTrx> updateWrapper = Wrappers.lambdaUpdate(OrderRefundsTrx.class)
                    .set(OrderRefundsTrx::getRefundStatus, OrderConstants.RefundStatus.REFUNDED)
                    .eq(OrderRefundsTrx::getRefundTrxId, notifyDto.getTrxid());
            orderRefundsTrxService.update(updateWrapper);


            // 查询同订单下，是否还有未支付的其他期数
            Long count = orderRefundsTrxService.lambdaQuery().eq(OrderRefundsTrx::getRefundId, orderRefundsTrx.getRefundId())
                    .eq(OrderRefundsTrx::getRefundStatus, OrderConstants.RefundStatus.WAIT_REFUND).count();
            if(count == 0) {
                LambdaUpdateWrapper<OrderRefunds> orderUpdateWrapper = Wrappers.lambdaUpdate(OrderRefunds.class)
                        .set(OrderRefunds::getRefundStatus, OrderConstants.RefundStatus.REFUNDED)
                        .eq(OrderRefunds::getId, orderRefundsTrx.getRefundId());
                orderRefundsService.update(orderUpdateWrapper);
            }

            // 更新订单状态
            ordersService.lambdaUpdate().set(Orders::getOrderStatus, OrderConstants.OrderStatus.REFUNDED)
                    .eq(Orders::getId, orderRefundsTrx.getOrderId())
                    .update();

            //
            ordersTrxService.lambdaUpdate().set(OrdersTrx::getTrxStatus, OrderConstants.OrderStatus.REFUNDED)
                    .eq(OrdersTrx::getOrderId, orderRefundsTrx.getOrderId())
                    .update();

            // todo... 调用课时接口

            log.info("[通联退款回调] 成功退款处理完成, 订单号: {}", cusorderid);
            return true;

        } catch (Exception e) {
            log.error("[通联退款回调] 成功退款处理异常, 订单号: {}", cusorderid, e);
            return false;
        }
    }

    /**
     * 处理成功支付
     * 
     * @param notifyDto 通知DTO对象
     * @return 处理结果
     */
    private boolean handleSuccessPayment(AllinPayNotifyDto notifyDto) {
        String cusorderid = notifyDto.getCusorderid();
        log.info("[通联支付回调] 处理成功支付, 商户流水号: {}, 通联交易号: {}, 渠道交易号: {}",
                cusorderid, notifyDto.getTrxid(), notifyDto.getChnltrxid());
        
        try {
            OrdersTrx ordersTrx = ordersTrxService.getByCusTrxSeq(notifyDto.getTrxid());
            if(ordersTrx == null) {
                log.error("[通联支付回调] 户流水号不存在, 商户流水号: {}", cusorderid);
                return false;
            }
            // 查看交易流水状态
            if(!ordersTrx.getTrxStatus().equalsIgnoreCase(OrderConstants.OrderStatus.UNPAID)) {
                log.error("[通联支付回调] 当前交易流水状有误, 商户流水号: {}", cusorderid);
                return false;
            }
            // 更新order_trx
            LambdaUpdateWrapper<OrdersTrx> updateWrapper = Wrappers.lambdaUpdate(OrdersTrx.class)
                    .set(OrdersTrx::getTrxStatus, OrderConstants.OrderStatus.PAID)
                    .eq(OrdersTrx::getCusTrxSeq, notifyDto.getTrxid());
            ordersTrxService.update(updateWrapper);

            // 更新订单状态
            // 查询同订单下，是否还有未支付的其他期数
            Long count = ordersTrxService.lambdaQuery().eq(OrdersTrx::getOrderId, ordersTrx.getOrderId())
                    .eq(OrdersTrx::getTrxStatus, OrderConstants.OrderStatus.UNPAID).count();

            LambdaUpdateWrapper<Orders> orderUpdateWrapper = Wrappers.lambdaUpdate(Orders.class)
                    .set(Orders::getOrderStatus, count > 0 ? OrderConstants.OrderStatus.PART_PAID : OrderConstants.OrderStatus.FULL_PAID)
                    .eq(Orders::getId, ordersTrx.getOrderId());
            ordersService.update(orderUpdateWrapper);

            // todo... 调用课时接口

            log.info("[通联支付回调] 成功支付处理完成, 订单号: {}", cusorderid);
            return true;
            
        } catch (Exception e) {
            log.error("[通联支付回调] 成功支付处理异常, 订单号: {}", cusorderid, e);
            return false;
        }
    }
    
    /**
     * 处理处理中支付
     * 
     * @param notifyDto 通知DTO对象
     * @return 处理结果
     */
    private boolean handleProcessingPayment(AllinPayNotifyDto notifyDto) {
        log.info("[通联支付回调] 处理处理中支付, 订单号: {}, 通联交易号: {}", 
            notifyDto.getCusorderid(), notifyDto.getTrxid());
        
        try {
            // TODO: 实现具体的处理中支付处理逻辑
            // 1. 更新订单状态为处理中
            // 2. 记录处理中状态
            
            log.info("[通联支付回调] 处理中支付处理完成, 订单号: {}", notifyDto.getCusorderid());
            return true;
            
        } catch (Exception e) {
            log.error("[通联支付回调] 处理中支付处理异常, 订单号: {}", notifyDto.getCusorderid(), e);
            return false;
        }
    }
    
    /**
     * 处理失败支付
     * 
     * @param notifyDto 通知DTO对象
     * @return 处理结果
     */
    private boolean handleFailedPayment(AllinPayNotifyDto notifyDto) {
        log.info("[通联支付回调] 处理失败支付, 订单号: {}, 通联交易号: {}, 交易状态: {}", 
            notifyDto.getCusorderid(), notifyDto.getTrxid(), notifyDto.getTrxstatus());
        
        try {
            // TODO: 实现具体的失败支付处理逻辑
            // 1. 更新订单状态为支付失败
            // 2. 记录失败原因
            // 3. 发送支付失败通知
            
            log.info("[通联支付回调] 失败支付处理完成, 订单号: {}", notifyDto.getCusorderid());
            return true;
            
        } catch (Exception e) {
            log.error("[通联支付回调] 失败支付处理异常, 订单号: {}", notifyDto.getCusorderid(), e);
            return false;
        }
    }
}
