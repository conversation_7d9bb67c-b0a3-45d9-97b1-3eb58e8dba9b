package org.nonamespace.word.server.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 退款记录相关DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public class RefundRecordDto {

    @Data
    @ToString
    public static class ApproveReq {
        private List<String> refundIds;
        private String approveResult;
        private String approveDesc;
    }

    /**
     * 退款记录查询请求
     */
    @Data
    public static class QueryReq {
        
        /**
         * 退款订单号
         */
        private String refundNo;
        
        /**
         * 订单ID
         */
        private String orderId;
        
        /**
         * 退款类型
         */
        private String refundType;
        
        /**
         * 退款状态
         */
        private String refundStatus;
        
        /**
         * 学生ID
         */
        private String studentId;

        /**
         * 销售员ID
         */
        private String salerId;

        /**
         * 产品ID
         */
        private String productId;
        
        /**
         * 退款开始时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date refundTimeStart;
        
        /**
         * 退款结束时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date refundTimeEnd;
        
        /**
         * 创建开始时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTimeStart;
        
        /**
         * 创建结束时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTimeEnd;
        
        /**
         * 最小退款金额（分）
         */
        private Long minRefundAmount;
        
        /**
         * 最大退款金额（分）
         */
        private Long maxRefundAmount;
        
        /**
         * 页码
         */
        private Integer pageNum = 1;
        
        /**
         * 页大小
         */
        private Integer pageSize = 10;
        
        /**
         * 排序字段
         */
        private String orderBy = "create_time";
        
        /**
         * 排序方向
         */
        private String orderDirection = "DESC";

        /**
         * 最大导出数量
         */
        private Integer maxExportCount = 10000;
    }

    /**
     * 退款记录响应
     */
    @Data
    public static class Resp {
        
        /**
         * 退款记录ID
         */
        private String id;
        
        /**
         * 订单ID
         */
        private String orderId;
        
        /**
         * 退款订单号
         */
        private String refundNo;
        
        /**
         * 退款类型
         */
        private String refundType;
        
        /**
         * 退款类型描述
         */
        private String refundTypeDesc;
        
        /**
         * 退款金额（元）
         */
        private String refundAmountYuan;
        
        /**
         * 退款原因
         */
        private String refundReason;
        
        /**
         * 退款状态
         */
        private String refundStatus;
        
        /**
         * 退款状态描述
         */
        private String refundStatusDesc;
        
        /**
         * 原产品信息
         */
        private String products;

        /**
         * 原订单信息
         */
        private String orders;

        /**
         * 原交易流水信息
         */
        private String ordersTrxs;

        /**
         * 退款方式
         */
        private String refundMethod;

        /**
         * 退款方式描述
         */
        private String refundMethodDesc;
        

        
        /**
         * 退款完成时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date refundTime;
        
        /**
         * 创建时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createTime;
        
        /**
         * 错误信息
         */
        private String errorMessage;
        
        /**
         * 备注
         */
        private String remark;
    }

    /**
     * 退款记录详情响应
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DetailResp extends Resp {
        
        /**
         * 原交易流水ID
         */
        private String originalTrxId;
        
        /**
         * 退款交易流水ID
         */
        private String refundTrxId;
        
        /**
         * 支付平台退款ID
         */
        private String platformRefundId;
        
        /**
         * 支付平台响应信息
         */
        private String platformResponse;
        

        
        /**
         * 更新时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date updateTime;
        
        /**
         * 创建人
         */
        private String createBy;
        
        /**
         * 更新人
         */
        private String updateBy;
    }



    /**
     * 退款统计响应
     */
    @Data
    public static class StatisticsResp {
        
        /**
         * 统计日期
         */
        private String statisticsDate;
        
        /**
         * 总退款笔数
         */
        private Integer totalRefundCount;
        
        /**
         * 总退款金额（元）
         */
        private String totalRefundAmountYuan;
        
        /**
         * 部分退款笔数
         */
        private Integer partialRefundCount;
        
        /**
         * 部分退款金额（元）
         */
        private String partialRefundAmountYuan;
        
        /**
         * 全额退款笔数
         */
        private Integer fullRefundCount;
        
        /**
         * 全额退款金额（元）
         */
        private String fullRefundAmountYuan;
        
        /**
         * 成功退款笔数
         */
        private Integer successRefundCount;
        
        /**
         * 失败退款笔数
         */
        private Integer failedRefundCount;
        
        /**
         * 平均退款金额（元）
         */
        private String avgRefundAmountYuan;
        
        /**
         * 按学科统计
         */
        private List<SubjectStatistics> subjectStatistics;
        
        /**
         * 按操作人统计
         */
        private List<OperatorStatistics> operatorStatistics;
    }

    /**
     * 学科统计
     */
    @Data
    public static class SubjectStatistics {
        private String subject;
        private Integer refundCount;
        private String refundAmountYuan;
    }

    /**
     * 操作人统计
     */
    @Data
    public static class OperatorStatistics {
        private String operatorName;
        private Integer refundCount;
        private String refundAmountYuan;
    }
}
