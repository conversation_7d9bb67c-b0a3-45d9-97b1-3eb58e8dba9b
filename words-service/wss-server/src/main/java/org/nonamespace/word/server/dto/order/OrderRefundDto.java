package org.nonamespace.word.server.dto.order;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

/**
 * 订单退款相关DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public class OrderRefundDto {

    /**
     * 订单退款请求
     */
    @Data
    public static class RefundReq {
        
        @NotBlank(message = "订单ID不能为空")
        private String orderId;

        @NotBlank(message = "退款原因不能为空")
        @Length(max = 200, message = "退款原因不能超过200个字符")
        private String refundReason;
    }

    /**
     * 退款响应
     */
    @Data
    public static class RefundResp {
        
        private String orderId;
        private String orderNo;
        private Long refundAmount;
        private String refundReason;
        private String orderStatus;
        private Long amtPaid;
        private Long amtUnpaid;
        private Date refundTime;
        private String message;
    }

    /**
     * 退款记录查询请求
     */
    @Data
    public static class RefundQueryReq {
        
        private String orderId;
        private String orderNo;
        private String studentId;
        private String studentName;
        private Date refundTimeStart;
        private Date refundTimeEnd;
        private Integer pageNum = 1;
        private Integer pageSize = 10;
        private String orderBy = "create_time";
        private String orderDirection = "DESC";
    }

    /**
     * 退款记录响应
     */
    @Data
    public static class RefundRecordResp {
        
        private String id;
        private String orderId;
        private String orderNo;
        private String cusTrxSeq;
        private Long refundAmount;
        private String refundReason;
        private String refundStatus;
        private String studentId;
        private String studentName;
        private String operatorId;
        private String operatorName;
        private Date refundTime;
        private Date createTime;
    }
}
