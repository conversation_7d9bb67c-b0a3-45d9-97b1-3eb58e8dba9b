package org.nonamespace.word.server.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.misc.WssContext;
import org.nonamespace.word.server.domain.StudentCourseHours;
import org.nonamespace.word.server.domain.StudentCourseHoursAdjustment;
import org.nonamespace.word.server.mapper.StudentCourseHoursMapper;
import org.nonamespace.word.server.service.IStudentCourseConsumptionService;
import org.nonamespace.word.server.service.IStudentCourseHoursAdjustmentService;
import org.nonamespace.word.server.service.IStudentCourseHoursService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 学生课时记录Service实现类
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
public class StudentCourseHoursServiceImpl extends ServiceImpl<StudentCourseHoursMapper, StudentCourseHours>
        implements IStudentCourseHoursService {

    @Autowired
    private IStudentCourseHoursAdjustmentService adjustmentService;

    @Autowired
    private IStudentCourseConsumptionService studentCourseConsumptionService;

    @Override
    public StudentCourseHours getByStudentAndSubjectAndType(String studentId, String subject, String specification) {
        // 获取所有相关记录并汇总
        List<StudentCourseHours> records = lambdaQuery()
                .eq(StudentCourseHours::getStudentId, studentId)
                .eq(StudentCourseHours::getSubject, subject)
                .eq(StudentCourseHours::getSpecification, specification)
                .eq(StudentCourseHours::getStatus, "active")
                .eq(StudentCourseHours::getDeleted, false)
                .list();

        if (records.isEmpty()) {
            return null;
        }

        // 如果只有一条记录，直接返回
        if (records.size() == 1) {
            return records.get(0);
        }

        // 多条记录时，创建汇总记录
        StudentCourseHours summary = new StudentCourseHours();
        summary.setStudentId(studentId);
        summary.setSubject(subject);
        summary.setSpecification(specification);
        summary.setNature(records.get(0).getNature()); // 使用第一条记录的性质

        // 汇总各项数据
        BigDecimal totalHours = BigDecimal.ZERO;
        BigDecimal remainingHours = BigDecimal.ZERO;
        BigDecimal purchasedHours = BigDecimal.ZERO;
        BigDecimal giftHours = BigDecimal.ZERO;
        BigDecimal consumedTotalHours = BigDecimal.ZERO;
        BigDecimal consumedPurchasedHours = BigDecimal.ZERO;
        BigDecimal consumedGiftHours = BigDecimal.ZERO;

        for (StudentCourseHours record : records) {
            totalHours = totalHours.add(record.getTotalHours());
            remainingHours = remainingHours.add(record.getRemainingHours());
            purchasedHours = purchasedHours.add(record.getPurchasedHours());
            giftHours = giftHours.add(record.getGiftHours());
            consumedTotalHours = consumedTotalHours.add(record.getConsumedTotalHours());
            consumedPurchasedHours = consumedPurchasedHours.add(record.getConsumedPurchasedHours());
            consumedGiftHours = consumedGiftHours.add(record.getConsumedGiftHours());
        }

        summary.setTotalHours(totalHours);
        summary.setRemainingHours(remainingHours);
        summary.setPurchasedHours(purchasedHours);
        summary.setGiftHours(giftHours);
        summary.setConsumedTotalHours(consumedTotalHours);
        summary.setConsumedPurchasedHours(consumedPurchasedHours);
        summary.setConsumedGiftHours(consumedGiftHours);
        summary.setStatus("active");

        return summary;
    }

    @Override
    public StudentCourseHours createOrUpdateHours(String studentId, String subject, String specification,
                                                 String nature, BigDecimal totalHours, BigDecimal remainingHours) {
        StudentCourseHours existing = getByStudentAndSubjectAndType(studentId, subject, specification);
        
        if (existing != null) {
            // 更新现有记录
            existing.setTotalHours(totalHours);
            existing.setRemainingHours(remainingHours);
            existing.setUpdateTime(WssContext.now());
            updateById(existing);
            return existing;
        } else {
            // 创建新记录
            StudentCourseHours newRecord = new StudentCourseHours();
            newRecord.setId(IdUtil.getSnowflakeNextIdStr());
            newRecord.setStudentId(studentId);
            newRecord.setSubject(subject);
            newRecord.setSpecification(specification);
            newRecord.setNature(nature);
            newRecord.setTotalHours(totalHours);
            newRecord.setRemainingHours(remainingHours);
            newRecord.setPurchasedHours(totalHours); // 默认全部为购买课时
            newRecord.setGiftHours(BigDecimal.ZERO);
            newRecord.setConsumedTotalHours(BigDecimal.ZERO);
            newRecord.setConsumedPurchasedHours(BigDecimal.ZERO);
            newRecord.setConsumedGiftHours(BigDecimal.ZERO);
            newRecord.setStatus("active");
            newRecord.setCreateTime(WssContext.now());
            newRecord.setUpdateTime(WssContext.now());
            newRecord.setDeleted(false);
            
            save(newRecord);
            return newRecord;
        }
    }

    @Override
    public boolean consumeHours(String studentId, String subject, String specification, BigDecimal consumedHours) {
        // 获取所有可用的课时记录，按FIFO顺序
        List<StudentCourseHours> records = getAvailableHoursRecords(studentId, subject, specification);

        if (records.isEmpty()) {
            log.warn("未找到学生课时记录: studentId={}, subject={}, specification={}", studentId, subject, specification);
            return false;
        }

        // 计算总剩余课时
        BigDecimal totalRemainingHours = records.stream()
                .map(StudentCourseHours::getRemainingHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalRemainingHours.compareTo(consumedHours) < 0) {
            log.warn("剩余课时不足: studentId={}, totalRemaining={}, consume={}",
                    studentId, totalRemainingHours, consumedHours);
            return false;
        }

        // 按FIFO顺序消费课时
        BigDecimal remainingToConsume = consumedHours;
        for (StudentCourseHours record : records) {
            if (remainingToConsume.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            BigDecimal recordRemainingHours = record.getRemainingHours();
            if (recordRemainingHours.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 计算从当前记录消费的课时数
            BigDecimal consumeFromThisRecord = remainingToConsume.min(recordRemainingHours);

            // 更新当前记录
            record.setRemainingHours(recordRemainingHours.subtract(consumeFromThisRecord));
            record.setConsumedTotalHours(record.getConsumedTotalHours().add(consumeFromThisRecord));

            // 优先消耗购买课时，再消耗赠送课时
            BigDecimal remainingPurchased = record.getPurchasedHours().subtract(record.getConsumedPurchasedHours());
            if (remainingPurchased.compareTo(consumeFromThisRecord) >= 0) {
                // 全部从购买课时中扣除
                record.setConsumedPurchasedHours(record.getConsumedPurchasedHours().add(consumeFromThisRecord));
            } else {
                // 先扣完购买课时，再扣赠送课时
                record.setConsumedPurchasedHours(record.getPurchasedHours());
                BigDecimal remainingToConsumeFromGift = consumeFromThisRecord.subtract(remainingPurchased);
                record.setConsumedGiftHours(record.getConsumedGiftHours().add(remainingToConsumeFromGift));
            }

            record.setUpdateTime(WssContext.now());
            updateById(record);

            remainingToConsume = remainingToConsume.subtract(consumeFromThisRecord);

            log.info("从课时记录消费: recordId={}, batchNo={}, consumed={}, remaining={}",
                    record.getId(), record.getBatchNo(), consumeFromThisRecord, record.getRemainingHours());
        }

        log.info("课时消费完成: studentId={}, subject={}, specification={}, totalConsumed={}",
                studentId, subject, specification, consumedHours);

        return true;
    }

    @Override
    public boolean consumeHours(String studentId, String subject, String specification, String nature, BigDecimal consumedHours,
                               String courseId, String teacherId, String remark) {
        // 获取所有可用的课时记录，按FIFO顺序（包含性质匹配）
        List<StudentCourseHours> records = getAvailableHoursRecords(studentId, subject, specification, nature);

        if (records.isEmpty()) {
            log.warn("未找到学生课时记录: studentId={}, subject={}, specification={}, nature={}", studentId, subject, specification, nature);
            return false;
        }

        // 计算总剩余课时
        BigDecimal totalRemainingHours = records.stream()
                .map(StudentCourseHours::getRemainingHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalRemainingHours.compareTo(consumedHours) < 0) {
            log.warn("剩余课时不足: studentId={}, subject={}, specification={}, nature={}, totalRemaining={}, consume={}",
                    studentId, subject, specification, nature, totalRemainingHours, consumedHours);
            return false;
        }

        // 按FIFO顺序消费课时，并记录每次消费
        BigDecimal remainingToConsume = consumedHours;
        for (StudentCourseHours record : records) {
            if (remainingToConsume.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            BigDecimal recordRemainingHours = record.getRemainingHours();
            if (recordRemainingHours.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 计算从当前记录消费的课时数
            BigDecimal consumeFromThisRecord = remainingToConsume.min(recordRemainingHours);

            // 更新当前记录
            record.setRemainingHours(recordRemainingHours.subtract(consumeFromThisRecord));
            record.setConsumedTotalHours(record.getConsumedTotalHours().add(consumeFromThisRecord));

            // 优先消耗购买课时，再消耗赠送课时
            BigDecimal remainingPurchased = record.getPurchasedHours().subtract(record.getConsumedPurchasedHours());
            if (remainingPurchased.compareTo(consumeFromThisRecord) >= 0) {
                // 全部从购买课时中扣除
                record.setConsumedPurchasedHours(record.getConsumedPurchasedHours().add(consumeFromThisRecord));
            } else {
                // 先扣完购买课时，再扣赠送课时
                record.setConsumedPurchasedHours(record.getPurchasedHours());
                BigDecimal remainingToConsumeFromGift = consumeFromThisRecord.subtract(remainingPurchased);
                record.setConsumedGiftHours(record.getConsumedGiftHours().add(remainingToConsumeFromGift));
            }

            record.setUpdateTime(WssContext.now());
            updateById(record);

            // 记录课消记录（每个课时记录单独记录，包含性质信息）
            studentCourseConsumptionService.recordConsumption(
                    studentId,
                    subject,
                    specification,
                    nature, // 传递课程性质
                    consumeFromThisRecord, // 从当前课时记录消费的课时数
                    WssContext.now(),
                    courseId,
                    record.getId(), // 记录具体的课时记录ID
                    teacherId,
                    remark
            );

            remainingToConsume = remainingToConsume.subtract(consumeFromThisRecord);

            log.info("从课时记录消费并记录课消: recordId={}, batchNo={}, nature={}, consumed={}, remaining={}, courseId={}",
                    record.getId(), record.getBatchNo(), nature, consumeFromThisRecord, record.getRemainingHours(), courseId);
        }

        log.info("课时消费完成: studentId={}, subject={}, specification={}, nature={}, totalConsumed={}, courseId={}",
                studentId, subject, specification, nature, consumedHours, courseId);

        return true;
    }

    @Override
    public boolean adjustHours(String studentId, String subject, String specification,
                              BigDecimal purchasedHoursAdjustment, BigDecimal giftHoursAdjustment,
                              String adjustmentReason, String operatorId, String operatorName) {

        // 验证调整参数
        if (purchasedHoursAdjustment.compareTo(BigDecimal.ZERO) == 0 &&
            giftHoursAdjustment.compareTo(BigDecimal.ZERO) == 0) {
            log.warn("购买课时和赠送课时调整数不能都为0");
            return false;
        }

        // 获取所有相关的课时记录
        List<StudentCourseHours> records = getHoursRecordsByStudentAndSubjectAndType(studentId, subject, specification);
        if (records.isEmpty()) {
            log.warn("未找到学生课时记录: studentId={}, subject={}, specification={}", studentId, subject, specification);
            return false;
        }

        // 计算调整前的汇总数据
        BigDecimal beforeTotalHours = BigDecimal.ZERO;
        BigDecimal beforeRemainingHours = BigDecimal.ZERO;
        BigDecimal beforePurchasedHours = BigDecimal.ZERO;
        BigDecimal beforeGiftHours = BigDecimal.ZERO;

        for (StudentCourseHours record : records) {
            beforeTotalHours = beforeTotalHours.add(record.getTotalHours());
            beforeRemainingHours = beforeRemainingHours.add(record.getRemainingHours());
            beforePurchasedHours = beforePurchasedHours.add(record.getPurchasedHours());
            beforeGiftHours = beforeGiftHours.add(record.getGiftHours());
        }

        // 计算总调整数
        BigDecimal totalAdjustment = purchasedHoursAdjustment.add(giftHoursAdjustment);

        // 选择要调整的记录
        // 优先选择有剩余课时的最新记录，如果没有则选择最新的记录
        StudentCourseHours recordToAdjust = records.stream()
                .filter(r -> r.getRemainingHours().compareTo(BigDecimal.ZERO) > 0)
                .max((r1, r2) -> r1.getCreateTime().compareTo(r2.getCreateTime()))
                .orElse(records.stream()
                        .max((r1, r2) -> r1.getCreateTime().compareTo(r2.getCreateTime()))
                        .orElse(records.get(0)));

        // 计算调整后的数据（基于要调整的记录）
        BigDecimal afterPurchasedHours = recordToAdjust.getPurchasedHours().add(purchasedHoursAdjustment);
        BigDecimal afterGiftHours = recordToAdjust.getGiftHours().add(giftHoursAdjustment);
        BigDecimal afterTotalHours = recordToAdjust.getTotalHours().add(totalAdjustment);
        BigDecimal afterRemainingHours = recordToAdjust.getRemainingHours().add(totalAdjustment);

        // 验证调整后的数据合法性
        if (afterPurchasedHours.compareTo(BigDecimal.ZERO) < 0) {
            log.warn("调整后购买课时不能为负数: studentId={}, recordId={}, afterPurchasedHours={}",
                    studentId, recordToAdjust.getId(), afterPurchasedHours);
            return false;
        }

        if (afterGiftHours.compareTo(BigDecimal.ZERO) < 0) {
            log.warn("调整后赠送课时不能为负数: studentId={}, recordId={}, afterGiftHours={}",
                    studentId, recordToAdjust.getId(), afterGiftHours);
            return false;
        }

        if (afterTotalHours.compareTo(BigDecimal.ZERO) < 0) {
            log.warn("调整后总课时不能为负数: studentId={}, recordId={}, afterTotalHours={}",
                    studentId, recordToAdjust.getId(), afterTotalHours);
            return false;
        }

        if (afterRemainingHours.compareTo(BigDecimal.ZERO) < 0) {
            log.warn("调整后剩余课时不能为负数: studentId={}, recordId={}, afterRemainingHours={}",
                    studentId, recordToAdjust.getId(), afterRemainingHours);
            return false;
        }

        // 在原记录上直接调整课时数量

        // 更新课时数量
        recordToAdjust.setPurchasedHours(recordToAdjust.getPurchasedHours().add(purchasedHoursAdjustment));
        recordToAdjust.setGiftHours(recordToAdjust.getGiftHours().add(giftHoursAdjustment));
        recordToAdjust.setTotalHours(recordToAdjust.getTotalHours().add(totalAdjustment));
        recordToAdjust.setRemainingHours(recordToAdjust.getRemainingHours().add(totalAdjustment));
        recordToAdjust.setUpdateTime(WssContext.now());

        // 保存更新
        updateById(recordToAdjust);

        log.info("课时调整完成: recordId={}, batchNo={}, purchasedAdjustment={}, giftAdjustment={}, totalAdjustment={}",
                recordToAdjust.getId(), recordToAdjust.getBatchNo(), purchasedHoursAdjustment, giftHoursAdjustment, totalAdjustment);

        // 记录调整历史（使用调整后的实际数据）
        String adjustmentType = totalAdjustment.compareTo(BigDecimal.ZERO) > 0 ? "increase" : "decrease";
        adjustmentService.recordAdjustment(
            studentId, subject, specification, recordToAdjust.getNature(),
            adjustmentType, totalAdjustment.abs(),
            purchasedHoursAdjustment, giftHoursAdjustment,
            beforeTotalHours, recordToAdjust.getTotalHours(), // 使用调整后的实际总课时
            beforeRemainingHours, recordToAdjust.getRemainingHours(), // 使用调整后的实际剩余课时
            beforePurchasedHours, recordToAdjust.getPurchasedHours(), // 使用调整后的实际购买课时
            beforeGiftHours, recordToAdjust.getGiftHours(), // 使用调整后的实际赠送课时
            adjustmentReason, operatorId, operatorName
        );

        log.info("课时调整成功: studentId={}, subject={}, specification={}, purchasedAdjustment={}, giftAdjustment={}",
                studentId, subject, specification, purchasedHoursAdjustment, giftHoursAdjustment);

        return true;
    }

    @Override
    public StudentCourseHours createNewHoursRecord(String studentId, String subject, String specification,
                                                  String nature, BigDecimal totalHours, BigDecimal remainingHours,
                                                  BigDecimal unitPrice, String batchNo) {
        StudentCourseHours newRecord = new StudentCourseHours();
        newRecord.setId(IdUtil.getSnowflakeNextIdStr());
        newRecord.setStudentId(studentId);
        newRecord.setSubject(subject);
        newRecord.setSpecification(specification);
        newRecord.setNature(nature);
        newRecord.setBatchNo(batchNo);
        newRecord.setUnitPrice(unitPrice);
        newRecord.setTotalHours(totalHours);
        newRecord.setRemainingHours(remainingHours);
        newRecord.setPurchasedHours(totalHours); // 默认全部为购买课时
        newRecord.setGiftHours(BigDecimal.ZERO);
        newRecord.setConsumedTotalHours(BigDecimal.ZERO);
        newRecord.setConsumedPurchasedHours(BigDecimal.ZERO);
        newRecord.setConsumedGiftHours(BigDecimal.ZERO);
        newRecord.setOrderTime(WssContext.now());
        newRecord.setStatus("active");
        newRecord.setSourceType("手动"); // 默认为手动创建
        newRecord.setFrozenPurchasedHours(BigDecimal.ZERO); // 默认无冻结课时
        newRecord.setFrozenGiftHours(BigDecimal.ZERO);
        newRecord.setCreateTime(WssContext.now());
        newRecord.setUpdateTime(WssContext.now());
        newRecord.setDeleted(false);

        save(newRecord);
        log.info("创建新课时记录: studentId={}, subject={}, specification={}, batchNo={}, totalHours={}",
                studentId, subject, specification, batchNo, totalHours);

        return newRecord;
    }

    @Override
    public StudentCourseHours createNewHoursRecordWithDetails(String studentId, String subject, String specification,
                                                            String nature, BigDecimal totalHours, BigDecimal remainingHours,
                                                            BigDecimal purchasedHours, BigDecimal giftHours,
                                                            BigDecimal purchasedRemainingHours, BigDecimal giftRemainingHours,
                                                            BigDecimal unitPrice, String batchNo) {
        StudentCourseHours newRecord = new StudentCourseHours();
        newRecord.setId(IdUtil.getSnowflakeNextIdStr());
        newRecord.setStudentId(studentId);
        newRecord.setSubject(subject);
        newRecord.setSpecification(specification);
        newRecord.setNature(nature);
        newRecord.setBatchNo(batchNo);
        newRecord.setUnitPrice(unitPrice);

        // 设置总课时和剩余课时
        newRecord.setTotalHours(totalHours);
        newRecord.setRemainingHours(remainingHours);

        // 设置购买课时和赠送课时
        newRecord.setPurchasedHours(purchasedHours);
        newRecord.setGiftHours(giftHours);

        // 计算已消耗的课时
        BigDecimal consumedPurchasedHours = purchasedHours.subtract(purchasedRemainingHours);
        BigDecimal consumedGiftHours = giftHours.subtract(giftRemainingHours);
        BigDecimal consumedTotalHours = consumedPurchasedHours.add(consumedGiftHours);

        newRecord.setConsumedTotalHours(consumedTotalHours);
        newRecord.setConsumedPurchasedHours(consumedPurchasedHours);
        newRecord.setConsumedGiftHours(consumedGiftHours);

        newRecord.setOrderTime(WssContext.now());
        newRecord.setStatus("active");
        newRecord.setSourceType("导入"); // 详细创建方法通常用于导入
        newRecord.setFrozenPurchasedHours(BigDecimal.ZERO); // 导入的课时默认无冻结
        newRecord.setFrozenGiftHours(BigDecimal.ZERO);
        newRecord.setCreateTime(WssContext.now());
        newRecord.setUpdateTime(WssContext.now());
        newRecord.setDeleted(false);

        save(newRecord);
        log.info("创建新课时记录(详细): studentId={}, subject={}, specification={}, batchNo={}, totalHours={}, purchasedHours={}, giftHours={}, consumedTotal={}",
                studentId, subject, specification, batchNo, totalHours, purchasedHours, giftHours, consumedTotalHours);

        return newRecord;
    }

    @Override
    public List<StudentCourseHours> getHoursRecordsByStudentAndSubjectAndType(String studentId, String subject, String specification) {
        return lambdaQuery()
                .eq(StudentCourseHours::getStudentId, studentId)
                .eq(StudentCourseHours::getSubject, subject)
                .eq(StudentCourseHours::getSpecification, specification)
                .eq(StudentCourseHours::getStatus, "active")
                .eq(StudentCourseHours::getDeleted, false)
                .orderByAsc(StudentCourseHours::getOrderTime) // 按导入时间升序，实现FIFO
                .list();
    }

    /**
     * 获取有剩余课时的记录（用于课时消费）
     */
    public List<StudentCourseHours> getAvailableHoursRecords(String studentId, String subject, String specification) {
        return lambdaQuery()
                .eq(StudentCourseHours::getStudentId, studentId)
                .eq(StudentCourseHours::getSubject, subject)
                .eq(StudentCourseHours::getSpecification, specification)
                .eq(StudentCourseHours::getStatus, "active")
                .eq(StudentCourseHours::getDeleted, false)
                .gt(StudentCourseHours::getRemainingHours, BigDecimal.ZERO) // 只查询还有剩余课时的记录
                .orderByAsc(StudentCourseHours::getOrderTime) // 按导入时间升序，实现FIFO
                .list();
    }

    /**
     * 获取有剩余课时的记录（用于课时消费，包含性质匹配）
     */
    public List<StudentCourseHours> getAvailableHoursRecords(String studentId, String subject, String specification, String nature) {
        return lambdaQuery()
                .eq(StudentCourseHours::getStudentId, studentId)
                .eq(StudentCourseHours::getSubject, subject)
                .eq(StudentCourseHours::getSpecification, specification)
                .eq(StudentCourseHours::getNature, nature) // 增加性质匹配
                .eq(StudentCourseHours::getStatus, "active")
                .eq(StudentCourseHours::getDeleted, false)
                .orderByAsc(StudentCourseHours::getOrderTime) // 按导入时间升序，实现FIFO
                .list();
    }

    @Override
    public IStudentCourseHoursService.CourseHoursCheckResult checkHoursBalance(String studentId, String subject, String specification, String nature, BigDecimal requiredHours) {
        try {
            log.info("检查课时余额: studentId={}, subject={}, specification={}, nature={}, requiredHours={}",
                    studentId, subject, specification, nature, requiredHours);

            // 参数验证
            if (studentId == null) {
                return new IStudentCourseHoursService.CourseHoursCheckResult(
                        false, BigDecimal.ZERO, requiredHours, "学生ID不能为空");
            }

            if (requiredHours == null || requiredHours.compareTo(BigDecimal.ZERO) <= 0) {
                return new IStudentCourseHoursService.CourseHoursCheckResult(
                        false, BigDecimal.ZERO, requiredHours, "需要的课时数必须大于0");
            }

            // 获取学生的课时余额（按学科、课型、性质匹配）
            List<StudentCourseHours> availableRecords = getAvailableHoursRecords(studentId, subject, specification, nature);

            if (availableRecords.isEmpty()) {
                String message = String.format("学生未购买【%s-%s-%s】课时，无法上课。", subject, specification, nature);
                log.warn("课时记录不存在: studentId={}, subject={}, specification={}, nature={}", studentId, subject, specification, nature);
                return new IStudentCourseHoursService.CourseHoursCheckResult(
                        false, BigDecimal.ZERO, requiredHours, message);
            }

            // 计算总剩余课时
            BigDecimal remainingHours = availableRecords.stream()
                    .map(StudentCourseHours::getRemainingHours)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.info("学生课时余额: studentId={}, subject={}, specification={}, nature={}, remainingHours={}, requiredHours={}",
                    studentId, subject, specification, nature, remainingHours, requiredHours);

            // 检查余额是否足够
            boolean sufficient = remainingHours.compareTo(requiredHours) >= 0;
            String message;

            if (sufficient) {
                message = String.format("课时余额充足。剩余课时：%.2f，需要课时：%.2f", remainingHours, requiredHours);
                log.info("课时余额检查通过: studentId={}, subject={}, specification={}, nature={}, remaining={}, required={}",
                        studentId, subject, specification, nature, remainingHours, requiredHours);
            } else {
                message = String.format("课时余额不足，无法开始上课。剩余课时：%.2f，需要课时：%.2f", remainingHours, requiredHours);
                log.warn("课时余额不足: studentId={}, subject={}, specification={}, nature={}, remaining={}, required={}",
                        studentId, subject, specification, nature, remainingHours, requiredHours);
            }

            return new IStudentCourseHoursService.CourseHoursCheckResult(
                    sufficient, remainingHours, requiredHours, message);

        } catch (Exception e) {
            log.error("课时余额检查失败: studentId={}, subject={}, specification={}, nature={}, requiredHours={}, error={}",
                    studentId, subject, specification, nature, requiredHours, e.getMessage(), e);
            return new IStudentCourseHoursService.CourseHoursCheckResult(
                    false, BigDecimal.ZERO, requiredHours, "课时余额检查失败: " + e.getMessage());
        }
    }

    @Override
    public boolean adjustHoursByCourseHoursId(String courseHoursId, BigDecimal purchasedHoursAdjustment,
                                             BigDecimal giftHoursAdjustment, BigDecimal unitPrice,
                                             String adjustmentReason, String operatorId, String operatorName) {
        try {
            // 获取课时包记录
            StudentCourseHours courseHours = getById(courseHoursId);
            if (courseHours == null) {
                log.warn("课时包不存在: courseHoursId={}", courseHoursId);
                return false;
            }

            // 记录调整前的数据
            BigDecimal beforeTotalHours = courseHours.getTotalHours();
            BigDecimal beforeRemainingHours = courseHours.getRemainingHours();
            BigDecimal beforePurchasedHours = courseHours.getPurchasedHours();
            BigDecimal beforeGiftHours = courseHours.getGiftHours();

            // 计算调整后的数据
            BigDecimal afterPurchasedHours = beforePurchasedHours.add(purchasedHoursAdjustment);
            BigDecimal afterGiftHours = beforeGiftHours.add(giftHoursAdjustment);
            BigDecimal afterTotalHours = afterPurchasedHours.add(afterGiftHours);

            // 验证调整后的数据
            if (afterPurchasedHours.compareTo(BigDecimal.ZERO) < 0) {
                log.warn("调整后购买课时不能为负数: afterPurchasedHours={}", afterPurchasedHours);
                return false;
            }
            if (afterGiftHours.compareTo(BigDecimal.ZERO) < 0) {
                log.warn("调整后赠送课时不能为负数: afterGiftHours={}", afterGiftHours);
                return false;
            }

            // 计算剩余课时的调整
            BigDecimal totalAdjustment = purchasedHoursAdjustment.add(giftHoursAdjustment);
            BigDecimal afterRemainingHours = beforeRemainingHours.add(totalAdjustment);

            if (afterRemainingHours.compareTo(BigDecimal.ZERO) < 0) {
                log.warn("调整后剩余课时不能为负数: afterRemainingHours={}", afterRemainingHours);
                return false;
            }

            // 更新课时记录
            courseHours.setPurchasedHours(afterPurchasedHours);
            courseHours.setGiftHours(afterGiftHours);
            courseHours.setTotalHours(afterTotalHours);
            courseHours.setRemainingHours(afterRemainingHours);
            courseHours.setUnitPrice(unitPrice);
            courseHours.setUpdateTime(WssContext.now());

            updateById(courseHours);

            // 创建调整历史记录
            StudentCourseHoursAdjustment adjustment = new StudentCourseHoursAdjustment();
            adjustment.setId(IdUtil.getSnowflakeNextIdStr());
            adjustment.setCourseHoursId(courseHoursId);
            adjustment.setStudentId(courseHours.getStudentId());
            adjustment.setSubject(courseHours.getSubject());
            adjustment.setSpecification(courseHours.getSpecification());
            adjustment.setNature(courseHours.getNature());

            // 确定调整类型
            String adjustmentType = totalAdjustment.compareTo(BigDecimal.ZERO) >= 0 ? "increase" : "decrease";
            adjustment.setAdjustmentType(adjustmentType);
            adjustment.setAdjustmentHours(totalAdjustment.abs());

            adjustment.setPurchasedHoursAdjustment(purchasedHoursAdjustment);
            adjustment.setGiftHoursAdjustment(giftHoursAdjustment);
            adjustment.setBeforeTotalHours(beforeTotalHours);
            adjustment.setAfterTotalHours(afterTotalHours);
            adjustment.setBeforeRemainingHours(beforeRemainingHours);
            adjustment.setAfterRemainingHours(afterRemainingHours);
            adjustment.setBeforePurchasedHours(beforePurchasedHours);
            adjustment.setAfterPurchasedHours(afterPurchasedHours);
            adjustment.setBeforeGiftHours(beforeGiftHours);
            adjustment.setAfterGiftHours(afterGiftHours);
            adjustment.setAdjustmentReason(adjustmentReason);
            adjustment.setOperatorId(operatorId);
            adjustment.setOperatorName(operatorName);
            adjustment.setAdjustmentTime(WssContext.now());
            adjustment.setCreateTime(WssContext.now());
            adjustment.setUpdateTime(WssContext.now());
            adjustment.setDeleted(false);

            adjustmentService.save(adjustment);

            log.info("课时调整成功: courseHoursId={}, purchasedAdjustment={}, giftAdjustment={}, unitPrice={}, operator={}",
                    courseHoursId, purchasedHoursAdjustment, giftHoursAdjustment, unitPrice, operatorName);

            return true;
        } catch (Exception e) {
            log.error("课时调整失败: courseHoursId=" + courseHoursId, e);
            return false;
        }
    }
}
