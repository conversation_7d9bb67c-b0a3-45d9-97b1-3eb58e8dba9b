package org.nonamespace.word.server.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nonamespace.word.server.entity.DataEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 学生课时记录实体类
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("student_course_hours")
public class StudentCourseHours extends DataEntity {

    /**
     * 学生ID
     */
    private String studentId;

    /**
     * 学科 (英语、数学、语文等)
     */
    private String subject;

    /**
     * 课型（单词课，题型课等）
     */
    private String specification;

    /**
     * 性质 (试听课、正式课)
     */
    private String nature;

    /**
     * 批次号，用于标识同一次导入的课时
     */
    private String batchNo;

    /**
     * 单价，每课时的费用
     */
    private BigDecimal unitPrice;

    /**
     * 导入时间，用于FIFO排序
     */
    private Date orderTime;

    /**
     * 总课时数 (保留2位小数)
     */
    private BigDecimal totalHours;

    /**
     * 总剩余课时数 (保留2位小数)
     */
    private BigDecimal remainingHours;

    /**
     * 购买课时数 (保留2位小数)
     */
    private BigDecimal purchasedHours;

    /**
     * 赠送课时数 (保留2位小数)
     */
    private BigDecimal giftHours;

    /**
     * 已消耗总课时数 (保留2位小数)
     */
    private BigDecimal consumedTotalHours;

    /**
     * 已消耗购买课时数 (保留2位小数)
     */
    private BigDecimal consumedPurchasedHours;

    /**
     * 已消耗赠送课时数 (保留2位小数)
     */
    private BigDecimal consumedGiftHours;

    /**
     * 状态 (active: 活跃, inactive: 停用)
     */
    private String status;

    /**
     * 关联的订单ID（一个课时包对应一个订单）
     */
    private String orderId;

    /**
     * 课时来源：手动、订单、导入、首课
     */
    private String sourceType;

    /**
     * 冻结的购买课时数 (保留2位小数)
     */
    private BigDecimal frozenPurchasedHours;

    /**
     * 冻结的赠送课时数 (保留2位小数)
     */
    private BigDecimal frozenGiftHours;
}
