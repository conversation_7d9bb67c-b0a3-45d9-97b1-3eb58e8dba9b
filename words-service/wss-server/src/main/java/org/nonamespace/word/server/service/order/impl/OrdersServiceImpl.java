package org.nonamespace.word.server.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.constants.OrderConstants;
import org.nonamespace.word.server.domain.Product;
import org.nonamespace.word.server.domain.order.Orders;
import org.nonamespace.word.server.domain.order.OrdersTrx;
import org.nonamespace.word.server.dto.order.OrderCreateDto;
import org.nonamespace.word.server.dto.order.OrderExportDto;
import org.nonamespace.word.server.dto.order.OrderPageDto;
import org.nonamespace.word.server.dto.order.PaymentDto;
import org.nonamespace.word.server.mapper.order.OrdersMapper;
import org.nonamespace.word.server.service.IProductService;
import org.nonamespace.word.server.service.order.IOrdersService;
import org.nonamespace.word.server.service.order.IOrdersTrxService;
import org.nonamespace.word.server.util.OrderTrxCodeUtil;
import org.nonamespace.word.thirdpart.allinpay.dto.PayDto;
import org.nonamespace.word.thirdpart.allinpay.model.NativePayOutput;
import org.nonamespace.word.thirdpart.allinpay.service.IAllinPayService;
import org.nonamespace.word.thirdpart.allinpay.service.IQRCodeService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 订单表Service业务层处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrdersServiceImpl extends MPJBaseServiceImpl<OrdersMapper, Orders> implements IOrdersService {

    private final IAllinPayService allinPayService;
    private final OrderTrxCodeUtil orderTrxCodeUtil;
    private final IOrdersTrxService ordersTrxService;
    private final IQRCodeService qrCodeService;
    private final IProductService productService;
    private final StringRedisTemplate redisTemplate;

    @Override
    public Orders getByOrderNo(String orderNo) {
        return this.lambdaQuery().eq(Orders::getNo, orderNo).one();
    }

    @Override
    public List<Orders> getByStudentId(String studentId) {
        return this.lambdaQuery().eq(Orders::getStudentId, studentId).orderByDesc(Orders::getCreateTime).list();
    }

    @Override
    public List<Orders> getBySalerId(String salerId) {
        return this.lambdaQuery().eq(Orders::getSalerId, salerId).orderByDesc(Orders::getCreateTime).list();
    }

    @Override
    public List<Orders> getByOrderStatus(String orderStatus) {
        return this.lambdaQuery().eq(Orders::getOrderStatus, orderStatus).orderByDesc(Orders::getCreateTime).list();
    }

    @Override
    public Orders createOrder(OrderCreateDto orderCreateDto) {
        Long userId = SecurityUtils.getUserId();
        // 同一个用户3s内只能请求一次
        String key = "order:create:locked:" + userId;
        if(redisTemplate.hasKey(key)) {
            throw new IllegalArgumentException("您最近3秒内已创建订单，请勿重复创建");
        }
        redisTemplate.opsForValue().set(key, orderCreateDto.getProductId(), 3, TimeUnit.SECONDS);
        try {
            String orderId = IdUtil.getSnowflakeNextIdStr();
            Product product = checkProductIsNotNull(orderCreateDto.getProductId());

            Orders orders = this.buildOrder(orderCreateDto, product);
            orders.setId(orderId);

            if(!orderCreateDto.isValidMultiTrxAmt(orders.getTotalAmt())) {
                throw new IllegalArgumentException("分期金额校验失败");
            }

            // 同时生成一笔交易初始流水
            List<OrdersTrx> ordersTrxes = new ArrayList<>();
            if(CollUtil.isNotEmpty(orderCreateDto.getMultiTrxAmts())) {
                orderCreateDto.getMultiTrxAmts().forEach(multiTrxAmt -> {
                    OrdersTrx ordersTrx = OrdersTrx.builder().orderId(orderId).orderNo(orders.getNo())
                            .cusTrxSeq(orderTrxCodeUtil.generalTrxSeq())
                            .trxIdx(multiTrxAmt.getIdx())
                            .trxAmt(multiTrxAmt.getAmt())
                            .trxType(OrderConstants.TrxType.PAY)
                            .trxStatus(OrderConstants.OrderStatus.UNPAID).build();
                    ordersTrxes.add(ordersTrx);
                });
            } else {
                OrdersTrx ordersTrx = OrdersTrx.builder().orderId(orderId).orderNo(orders.getNo())
                        .cusTrxSeq(orderTrxCodeUtil.generalTrxSeq())
                        .trxIdx(1)
                        .trxAmt(orders.getTotalAmt())
                        .trxType(OrderConstants.TrxType.PAY)
                        .trxStatus(OrderConstants.OrderStatus.UNPAID).build();
                ordersTrxes.add(ordersTrx);
            }
            if(CollUtil.isEmpty(ordersTrxes)){
                throw new IllegalArgumentException("订单交易流水不能为空");
            }

            // 保存订单
            this.save(orders);
            // 保存交易流水
            ordersTrxService.saveBatch(ordersTrxes);
            return orders;
        } finally {
            redisTemplate.delete(key);
        }
    }


    @Override
    public void cancelOrder(String orderId) {
        Orders orders = this.checkOrderIfExists(orderId);

        // 检查订单状态是否允许取消
        if(List.of(OrderConstants.OrderStatus.CANCEL, OrderConstants.OrderStatus.REFUNDED)
                .contains(orders.getOrderStatus())) {
            throw new IllegalArgumentException("订单已取消或已退款，无法重复操作");
        }

        // 获取订单的交易流水
        List<OrdersTrx> ordersTrxes = ordersTrxService.lambdaQuery()
                .eq(OrdersTrx::getOrderId, orderId)
                .list();

        // 更新订单状态
        orders.setOrderStatus(OrderConstants.OrderStatus.CANCEL);
        orders.setAmtPaid(0L);
        orders.setAmtUnpaid(orders.getTotalAmt());
        this.updateById(orders);

        // 更新所有未处理的交易流水状态
        if(CollUtil.isNotEmpty(ordersTrxes)) {
            ordersTrxes.forEach(ordersTrx -> {
                if(!OrderConstants.TrxStatus.CANCELLED.equals(ordersTrx.getTrxStatus())) {
                    ordersTrx.setTrxStatus(OrderConstants.TrxStatus.CANCELLED);
                    ordersTrxService.updateById(ordersTrx);
                }
            });
        }

        log.info("订单取消成功: orderId={}, orderNo={}", orderId, orders.getNo());
    }


    @Override
    public Page<OrderPageDto.Resp> selectOrdersByParam(OrderPageDto.Req req) {
        Page<OrderPageDto.Resp> pageParam = new Page<>(req.getPageNum(), req.getPageSize());
        return this.baseMapper.selectOrdersByParam(pageParam, req);
    }

    @Override
    public List<OrderExportDto.ExportResp> exportOrders(OrderExportDto.ExportReq req) {
        log.info("开始导出订单数据: req={}", req);

        // 构建查询条件
        LambdaQueryWrapper<Orders> queryWrapper = buildOrderExportQuery(req);

        // 限制导出数量
        int maxCount = req.getMaxExportCount() != null ? req.getMaxExportCount() : 10000;
        queryWrapper.last("LIMIT " + maxCount);

        // 查询订单数据
        List<Orders> orders = this.list(queryWrapper);

        if (CollUtil.isEmpty(orders)) {
            log.info("没有符合条件的订单数据");
            return new ArrayList<>();
        }

        log.info("查询到订单数据: count={}", orders.size());

        // 转换为导出格式
        List<OrderExportDto.ExportResp> exportData = new ArrayList<>();
        for (Orders order : orders) {
            OrderExportDto.ExportResp exportResp = convertToExportResp(order, req);
            exportData.add(exportResp);
        }

        log.info("订单数据导出完成: exportCount={}", exportData.size());
        return exportData;
    }

    /**
     * 构建订单导出查询条件
     */
    private LambdaQueryWrapper<Orders> buildOrderExportQuery(OrderExportDto.ExportReq req) {
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<>();

        // 基本查询条件
        queryWrapper.eq(StrUtil.isNotBlank(req.getOrderNo()), Orders::getNo, req.getOrderNo())
                .eq(StrUtil.isNotBlank(req.getOrderStatus()), Orders::getOrderStatus, req.getOrderStatus())
                .eq(StrUtil.isNotBlank(req.getSource()), Orders::getSource, req.getSource())
                .eq(StrUtil.isNotBlank(req.getTrxMethod()), Orders::getTrxMethod, req.getTrxMethod())
                .eq(StrUtil.isNotBlank(req.getSignStatus()), Orders::getSignStatus, req.getSignStatus())
                .ge(req.getCreateTimeStart() != null, Orders::getCreateTime, req.getCreateTimeStart())
                .le(req.getCreateTimeEnd() != null, Orders::getCreateTime, req.getCreateTimeEnd())
                .ge(req.getLastPayTimeStart() != null, Orders::getLastPayTime, req.getLastPayTimeStart())
                .le(req.getLastPayTimeEnd() != null, Orders::getLastPayTime, req.getLastPayTimeEnd())
                .ge(req.getMinTotalAmt() != null, Orders::getTotalAmt, req.getMinTotalAmt())
                .le(req.getMaxTotalAmt() != null, Orders::getTotalAmt, req.getMaxTotalAmt());

        // 学生相关条件需要关联查询，这里先简化处理
        // TODO: 如果需要按学生姓名、手机号等查询，需要使用MPJ进行关联查询

        // 排序
        queryWrapper.orderByDesc(Orders::getCreateTime);

        return queryWrapper;
    }

    /**
     * 转换为导出响应对象
     */
    private OrderExportDto.ExportResp convertToExportResp(Orders order, OrderExportDto.ExportReq req) {
        OrderExportDto.ExportResp resp = new OrderExportDto.ExportResp();

        // 基本订单信息
        resp.setOrderId(order.getId());
        resp.setOrderNo(order.getNo());
        resp.setSource(order.getSource());
        resp.setBody(order.getBody());
        resp.setOrderStatus(order.getOrderStatus());
        resp.setTotalAmtYuan(formatAmountToYuan(order.getTotalAmt()));
        resp.setAmtPaidYuan(formatAmountToYuan(order.getAmtPaid()));
        resp.setAmtUnpaidYuan(formatAmountToYuan(order.getAmtUnpaid()));
        resp.setTrxMethod(order.getTrxMethod());
        resp.setSignStatus(order.getSignStatus());
        resp.setLastPayTime(order.getLastPayTime());
        resp.setCreateTime(order.getCreateTime());
        resp.setUpdateTime(order.getUpdateTime());
        resp.setRemark(order.getRemark());

        // 产品信息
        Product product = order.getProducts();
        if (product != null) {
            resp.setProductName(product.getName());
            resp.setProductType(product.getType());
            resp.setSubject(product.getSubject());
            resp.setCourseType(product.getCourseType());
            resp.setQuantity(product.getQuantity() != null ? product.getQuantity().toString() : "");
            resp.setBonusHours(product.getBonusHoursQuantity() != null ? product.getBonusHoursQuantity().toString() : "0");
            resp.setUnitPriceYuan(formatAmountToYuan(product.getUnitPrice()));
            resp.setOriginalPriceYuan(formatAmountToYuan(product.getOriginalPrice()));
            resp.setSellingPriceYuan(formatAmountToYuan(product.getSellingPrice()));
        }

        // 学生和销售员信息需要通过关联查询获取
        // TODO: 优化为批量查询以提高性能
        try {
            // 获取学生信息
            if (StrUtil.isNotBlank(order.getStudentId())) {
                // 这里需要注入用户服务来获取学生信息
                // 暂时设置为ID，后续优化
                resp.setStudentName("学生ID:" + order.getStudentId());
                resp.setStudentPhone("");
            }

            // 获取销售员信息
            if (StrUtil.isNotBlank(order.getSalerId())) {
                resp.setSalerName("销售员ID:" + order.getSalerId());
                resp.setSalerPhone("");
            }
        } catch (Exception e) {
            log.warn("获取用户信息失败: orderId={}, error={}", order.getId(), e.getMessage());
        }

        // 统计交易记录
        if (req.getIncludeTransactions() != null && req.getIncludeTransactions()) {
            List<OrdersTrx> transactions = ordersTrxService.getByOrderId(order.getId());
            resp.setTransactionCount(transactions.size());
            resp.setPaymentCount((int) transactions.stream().filter(t -> OrderConstants.TrxType.PAY.equals(t.getTrxType())).count());
            resp.setRefundCount((int) transactions.stream().filter(t -> OrderConstants.TrxType.REFUND.equals(t.getTrxType())).count());
        }

        return resp;
    }

    /**
     * 格式化金额为元（保留2位小数）
     */
    private String formatAmountToYuan(Long amountFen) {
        if (amountFen == null) {
            return "0.00";
        }
        return String.format("%.2f", amountFen / 100.0);
    }


    private void checkOrderTrxStatus(OrdersTrx ordersTrx) {
        if(ordersTrx == null) {
            throw new IllegalArgumentException("交易流水不存在");
        }
        if(!ordersTrx.getTrxStatus().equalsIgnoreCase(OrderConstants.OrderStatus.UNPAID)) {
            throw new IllegalArgumentException("交易流水状态错误");
        }
        if(ordersTrx.getTrxAmt() <= 0) {
            throw new IllegalArgumentException("交易流水金额错误");
        }
    }

    private Orders buildOrder(OrderCreateDto orderCreateDto, Product product) {
        Orders order = new Orders();
        order.setNo(orderTrxCodeUtil.generalOrderCode(OrderConstants.TrxType.PAY));
        order.setSource(OrderConstants.Source.SYSTEM);
        order.setOrderStatus(OrderConstants.OrderStatus.UNPAID);

        order.setProducts(product);
        order.setProductId(product.getId());

        order.setTotalAmt(product.getSellingPrice());
        order.setBody(product.getName());
        order.setRemark(product.getName() + "待支付订单");

        order.setStudentId(orderCreateDto.getStudentId());
        order.setSalerId(String.valueOf(SecurityUtils.getUserId()));
        order.setAmtPaid(0L);
        order.setAmtUnpaid(order.getTotalAmt());
        order.setSignStatus(OrderConstants.SignStatus.UN_SIGN);
        order.setTrxMethod(CollUtil.isEmpty(orderCreateDto.getMultiTrxAmts()) ? "一次性支付" : "分期支付");
        return order;
    }

    @Override
    public PaymentDto.PayResp generatePayment(String orderTrxId) {
        OrdersTrx ordersTrx = ordersTrxService.getById(orderTrxId);
        checkOrderTrxStatus(ordersTrx);

        // 获取订单信息
        Orders orders = this.getById(ordersTrx.getOrderId());
        if(orders == null) {
            throw new IllegalArgumentException("订单不存在");
        }

        try {
            NativePayOutput nativePayOutput = allinPayService.nativepay(PayDto.NativePayReq.builder()
                    .cusTrxSeq(ordersTrx.getCusTrxSeq())
                    .body(orders.getBody()).remark(orders.getRemark())
                    .trxamt(ordersTrx.getTrxAmt())
                    .build());

            if(!nativePayOutput.isSuccess()) {
                throw new RuntimeException("生成支付信息失败: " + nativePayOutput.getRetmsg());
            }

            PaymentDto.PayResp payResp = new PaymentDto.PayResp();
            payResp.setPayInfo(nativePayOutput.getPayinfo());
            payResp.setPayUrl(nativePayOutput.getPayinfo());
            payResp.setOrderTrxId(orderTrxId);
            payResp.setCusTrxSeq(ordersTrx.getCusTrxSeq());
            payResp.setTrxAmt(ordersTrx.getTrxAmt());

            return payResp;
        } catch (Exception e) {
            log.error("生成支付信息异常", e);
            throw new RuntimeException("生成支付信息失败: " + e.getMessage());
        }
    }

    @Override
    public PaymentDto.QRCodeResp generateQRCode(String orderTrxId) {
        PaymentDto.PayResp payResp = generatePayment(orderTrxId);

        try {
            // 生成二维码
            byte[] qrCode = qrCodeService.generateQRCode(payResp.getPayInfo(), 200, 200);
            String qrCodeBase64 = java.util.Base64.getEncoder().encodeToString(qrCode);

            PaymentDto.QRCodeResp qrCodeResp = new PaymentDto.QRCodeResp();
            qrCodeResp.setQrCodeBase64(qrCodeBase64);
            qrCodeResp.setPayUrl(payResp.getPayUrl());

            return qrCodeResp;
        } catch (Exception e) {
            log.error("生成二维码异常", e);
            throw new RuntimeException("生成二维码失败: " + e.getMessage());
        }
    }


    private Product checkProductIsNotNull(String productId) {
        Product product = productService.getById(productId);
        Objects.requireNonNull(product, "该产品配置信息不存在，请确认");
        return product;
    }

    /**
     *
     * @param orderId
     */
    public Orders checkOrderIfExists(String orderId) {
        Orders orders = this.getById(orderId);
        Objects.requireNonNull(orders, "该订单不存在，请确认");
        return orders;
    }

}