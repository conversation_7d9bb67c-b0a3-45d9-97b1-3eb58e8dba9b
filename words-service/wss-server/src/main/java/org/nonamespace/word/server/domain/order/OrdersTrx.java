package org.nonamespace.word.server.domain.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import org.nonamespace.word.server.entity.DataEntity;

/**
 * 订单交易记录表
 * <AUTHOR>
 */
@Data
@TableName("orders_trx")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class OrdersTrx extends DataEntity {

    /** 订单ID **/
    @TableField("order_id")
    private String orderId;

    /** 订单号 **/
    @TableField("order_no")
    private String orderNo;

    /** 客户交易序列号 **/
    @TableField("cus_trx_seq")
    private String cusTrxSeq;

    /** 交易索引 **/
    @TableField("trx_idx")
    private Integer trxIdx;

    /** 支付类型 **/
    @TableField("pay_type")
    private String payType;

    /** 交易金额(分) **/
    @TableField("trx_amt")
    private Long trxAmt;

    /** 交易类型 **/
    @TableField("trx_type")
    private String trxType;

    /** 交易状态 **/
    @TableField("trx_status")
    private String trxStatus;

    /** 交易ID **/
    @TableField("trx_id")
    private Integer trxId;
}
