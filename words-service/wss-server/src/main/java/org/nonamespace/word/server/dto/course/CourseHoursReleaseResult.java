package org.nonamespace.word.server.dto.course;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课时释放结果DTO
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@Builder
public class CourseHoursReleaseResult {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 消息
     */
    private String message;

    /**
     * 释放详情列表
     */
    private List<ReleaseDetail> releaseDetails;

    /**
     * 总释放的购买课时数
     */
    private BigDecimal totalReleasedPurchasedHours;

    /**
     * 总释放的赠送课时数
     */
    private BigDecimal totalReleasedGiftHours;

    /**
     * 释放详情
     */
    @Data
    @Builder
    public static class ReleaseDetail {
        /**
         * 课时包ID
         */
        private String courseHoursId;

        /**
         * 订单交易流水ID
         */
        private String orderTrxId;

        /**
         * 释放的购买课时数
         */
        private BigDecimal releasedPurchasedHours;

        /**
         * 释放的赠送课时数
         */
        private BigDecimal releasedGiftHours;

        /**
         * 支付比例
         */
        private BigDecimal paymentRatio;

        /**
         * 释放类型
         */
        private String releaseType;
    }

    /**
     * 创建成功结果
     */
    public static CourseHoursReleaseResult success(List<ReleaseDetail> details, 
                                                   BigDecimal totalPurchased, 
                                                   BigDecimal totalGift) {
        return CourseHoursReleaseResult.builder()
                .success(true)
                .message("课时释放成功")
                .releaseDetails(details)
                .totalReleasedPurchasedHours(totalPurchased)
                .totalReleasedGiftHours(totalGift)
                .build();
    }

    /**
     * 创建失败结果
     */
    public static CourseHoursReleaseResult failure(String message) {
        return CourseHoursReleaseResult.builder()
                .success(false)
                .message(message)
                .build();
    }

    /**
     * 创建无需处理结果
     */
    public static CourseHoursReleaseResult noNeedProcess(String message) {
        return CourseHoursReleaseResult.builder()
                .success(true)
                .message(message)
                .build();
    }
}
