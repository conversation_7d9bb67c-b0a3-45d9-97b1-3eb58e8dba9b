package org.nonamespace.word.server.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 订单导出相关DTO
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public class OrderExportDto {

    /**
     * 订单导出查询请求
     */
    @Data
    public static class ExportReq {
        
        /**
         * 订单号
         */
        private String orderNo;
        
        /**
         * 学生姓名
         */
        private String studentName;
        
        /**
         * 学生手机号
         */
        private String studentPhone;
        
        /**
         * 销售员姓名
         */
        private String salerName;
        
        /**
         * 订单状态
         */
        private String orderStatus;
        
        /**
         * 订单来源
         */
        private String source;
        
        /**
         * 交易方式
         */
        private String trxMethod;
        
        /**
         * 签署状态
         */
        private String signStatus;
        
        /**
         * 产品名称
         */
        private String productName;
        
        /**
         * 学科
         */
        private String subject;
        
        /**
         * 课型
         */
        private String courseType;
        
        /**
         * 订单创建开始时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTimeStart;
        
        /**
         * 订单创建结束时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTimeEnd;
        
        /**
         * 最后支付开始时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date lastPayTimeStart;
        
        /**
         * 最后支付结束时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date lastPayTimeEnd;
        
        /**
         * 最小订单金额（分）
         */
        private Long minTotalAmt;
        
        /**
         * 最大订单金额（分）
         */
        private Long maxTotalAmt;
        
        /**
         * 导出字段列表（如果为空则导出所有字段）
         */
        private List<String> exportFields;
        
        /**
         * 导出格式（excel, csv）
         */
        private String exportFormat = "excel";
        
        /**
         * 是否包含产品详情
         */
        private Boolean includeProductDetails = false;
        
        /**
         * 是否包含交易记录
         */
        private Boolean includeTransactions = false;
        
        /**
         * 最大导出数量限制
         */
        private Integer maxExportCount = 10000;
    }

    /**
     * 订单导出数据响应
     */
    @Data
    public static class ExportResp {
        
        /**
         * 订单ID
         */
        private String orderId;
        
        /**
         * 订单号
         */
        private String orderNo;
        
        /**
         * 订单来源
         */
        private String source;
        
        /**
         * 订单标题
         */
        private String body;
        
        /**
         * 订单状态
         */
        private String orderStatus;
        
        /**
         * 订单总金额（元）
         */
        private String totalAmtYuan;
        
        /**
         * 已支付金额（元）
         */
        private String amtPaidYuan;
        
        /**
         * 未支付金额（元）
         */
        private String amtUnpaidYuan;
        
        /**
         * 学生姓名
         */
        private String studentName;
        
        /**
         * 学生手机号
         */
        private String studentPhone;
        
        /**
         * 销售员姓名
         */
        private String salerName;
        
        /**
         * 销售员手机号
         */
        private String salerPhone;
        
        /**
         * 交易方式
         */
        private String trxMethod;
        
        /**
         * 签署状态
         */
        private String signStatus;
        
        /**
         * 产品名称
         */
        private String productName;
        
        /**
         * 产品类型
         */
        private String productType;
        
        /**
         * 学科
         */
        private String subject;
        
        /**
         * 课型
         */
        private String courseType;
        
        /**
         * 课时数量
         */
        private String quantity;
        
        /**
         * 赠送课时
         */
        private String bonusHours;
        
        /**
         * 单价（元）
         */
        private String unitPriceYuan;
        
        /**
         * 原价（元）
         */
        private String originalPriceYuan;
        
        /**
         * 售价（元）
         */
        private String sellingPriceYuan;
        
        /**
         * 最后支付时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date lastPayTime;
        
        /**
         * 订单创建时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createTime;
        
        /**
         * 订单更新时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date updateTime;
        
        /**
         * 备注
         */
        private String remark;
        
        /**
         * 交易记录数量
         */
        private Integer transactionCount;
        
        /**
         * 支付记录数量
         */
        private Integer paymentCount;
        
        /**
         * 退款记录数量
         */
        private Integer refundCount;
    }

    /**
     * 导出结果响应
     */
    @Data
    public static class ExportResultResp {
        
        /**
         * 导出文件名
         */
        private String fileName;
        
        /**
         * 导出记录数
         */
        private Integer exportCount;
        
        /**
         * 文件大小（字节）
         */
        private Long fileSize;
        
        /**
         * 导出时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date exportTime;
        
        /**
         * 导出用户
         */
        private String exportUser;
        
        /**
         * 导出条件摘要
         */
        private String exportConditions;
        
        /**
         * 下载链接（如果是异步导出）
         */
        private String downloadUrl;
        
        /**
         * 导出状态（processing, completed, failed）
         */
        private String status;
        
        /**
         * 错误信息（如果导出失败）
         */
        private String errorMessage;
    }
}
