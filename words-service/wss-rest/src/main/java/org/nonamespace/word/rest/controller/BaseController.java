package org.nonamespace.word.rest.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.exception.ServiceException;

/**
 * 控制器基类
 * 提供统一的异常处理和通用方法
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
public abstract class BaseController extends org.nonamespace.word.rest.base.controller.BaseController {

    /**
     * 统一异常处理
     * 
     * @param operation 操作名称
     * @param action 具体操作
     * @return 操作结果
     */
    protected AjaxResult handleOperation(String operation, OperationAction action) {
        try {
            Object result = action.execute();
            return AjaxResult.success(result);
        } catch (ServiceException e) {
            log.warn("{}业务异常: {}", operation, e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (IllegalArgumentException e) {
            log.warn("{}参数异常: {}", operation, e.getMessage());
            return AjaxResult.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("{}系统异常", operation, e);
            return AjaxResult.error("系统异常，请联系管理员");
        }
    }

    /**
     * 统一异常处理（无返回值）
     * 
     * @param operation 操作名称
     * @param action 具体操作
     * @return 操作结果
     */
    protected AjaxResult handleVoidOperation(String operation, VoidOperationAction action) {
        try {
            action.execute();
            return AjaxResult.success();
        } catch (ServiceException e) {
            log.warn("{}业务异常: {}", operation, e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (IllegalArgumentException e) {
            log.warn("{}参数异常: {}", operation, e.getMessage());
            return AjaxResult.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("{}系统异常", operation, e);
            return AjaxResult.error("系统异常，请联系管理员");
        }
    }

    /**
     * 统一异常处理（布尔返回值）
     * 
     * @param operation 操作名称
     * @param action 具体操作
     * @param successMessage 成功消息
     * @param failureMessage 失败消息
     * @return 操作结果
     */
    protected AjaxResult handleBooleanOperation(String operation, BooleanOperationAction action, 
                                              String successMessage, String failureMessage) {
        try {
            boolean result = action.execute();
            if (result) {
                return AjaxResult.success(successMessage);
            } else {
                return AjaxResult.error(failureMessage);
            }
        } catch (ServiceException e) {
            log.warn("{}业务异常: {}", operation, e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (IllegalArgumentException e) {
            log.warn("{}参数异常: {}", operation, e.getMessage());
            return AjaxResult.error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("{}系统异常", operation, e);
            return AjaxResult.error("系统异常，请联系管理员");
        }
    }

    /**
     * 验证必填参数
     * 
     * @param paramName 参数名
     * @param paramValue 参数值
     */
    protected void validateRequired(String paramName, Object paramValue) {
        if (paramValue == null) {
            throw new IllegalArgumentException(paramName + "不能为空");
        }
        if (paramValue instanceof String && ((String) paramValue).trim().isEmpty()) {
            throw new IllegalArgumentException(paramName + "不能为空");
        }
    }

    /**
     * 验证ID参数
     * 
     * @param id ID值
     * @param idName ID名称
     */
    protected void validateId(String id, String idName) {
        validateRequired(idName, id);
        if (id.trim().length() < 1) {
            throw new IllegalArgumentException(idName + "格式不正确");
        }
    }

    /**
     * 验证分页参数
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     */
    protected void validatePagination(Integer pageNum, Integer pageSize) {
        if (pageNum != null && pageNum < 1) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        if (pageSize != null && (pageSize < 1 || pageSize > 100)) {
            throw new IllegalArgumentException("页大小必须在1-100之间");
        }
    }

    /**
     * 操作接口
     */
    @FunctionalInterface
    protected interface OperationAction {
        Object execute() throws Exception;
    }

    /**
     * 无返回值操作接口
     */
    @FunctionalInterface
    protected interface VoidOperationAction {
        void execute() throws Exception;
    }

    /**
     * 布尔返回值操作接口
     */
    @FunctionalInterface
    protected interface BooleanOperationAction {
        boolean execute() throws Exception;
    }
}
