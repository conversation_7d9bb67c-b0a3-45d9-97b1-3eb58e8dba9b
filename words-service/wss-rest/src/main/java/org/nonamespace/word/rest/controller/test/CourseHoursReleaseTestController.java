package org.nonamespace.word.rest.controller.test;

import com.ruoyi.common.core.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.rest.controller.BaseController;
import org.nonamespace.word.server.dto.course.CourseHoursReleaseResult;
import org.nonamespace.word.server.service.ICourseHoursReleaseService;
import org.springframework.web.bind.annotation.*;

/**
 * 课时释放功能测试控制器
 * 仅用于测试验证，生产环境应删除
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Slf4j
@RestController
@RequestMapping("/test/course-hours-release")
@RequiredArgsConstructor
public class CourseHoursReleaseTestController extends BaseController {

    private final ICourseHoursReleaseService courseHoursReleaseService;

    /**
     * 测试课时释放功能
     */
    @PostMapping("/refresh/{orderNo}")
    public AjaxResult testRefreshCourseHoursRelease(@PathVariable String orderNo) {
        return handleOperation("释放课时", () -> {
            log.info("[测试] 开始测试课时释放功能, 订单号: {}", orderNo);
            
            CourseHoursReleaseResult result = courseHoursReleaseService.refreshCourseHoursRelease(orderNo);
            
            log.info("[测试] 课时释放测试完成, 订单号: {}, 结果: {}", orderNo, result.isSuccess());
            
            return result;
        });
    }

    /**
     * 检查订单是否需要释放课时
     */
    @GetMapping("/check/{orderNo}")
    public AjaxResult checkNeedsRelease(@PathVariable String orderNo) {
        return handleOperation("检查是否需要是否课时", () -> {
            log.info("[测试] 检查订单是否需要释放课时, 订单号: {}", orderNo);
            
            boolean needsRelease = courseHoursReleaseService.needsReleaseCheck(orderNo);
            
            log.info("[测试] 检查完成, 订单号: {}, 需要释放: {}", orderNo, needsRelease);
            
            return needsRelease;
        });
    }
}
