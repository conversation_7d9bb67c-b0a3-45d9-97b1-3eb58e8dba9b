package org.nonamespace.word.rest.controller.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Anonymous;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.service.order.IAllinPayNotifyService;
import org.nonamespace.word.thirdpart.allinpay.config.AllinPayConfig;
import org.nonamespace.word.thirdpart.allinpay.dto.AllinPayNotifyDto;
import org.nonamespace.word.thirdpart.allinpay.util.AllinPaySignUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/allinpay/notify")
@RequiredArgsConstructor
@Anonymous
public class AllinPayNotifyController {

    private final AllinPayConfig allinPayConfig;
    private final IAllinPayNotifyService allinPayNotifyService;

    /**
     * 通联支付交易结果通知接口
     * 根据通联支付接口文档实现的回调接口
     * 
     * @param request HTTP请求对象
     * @return 响应结果
     */
    @PostMapping("/payment")
    public ResponseEntity<String> paymentNotify(HttpServletRequest request) {
        log.info("[通联支付回调] 接收到支付结果通知");
        
        try {
            // 1. 获取所有请求参数（按照文档要求获取全量变量）
            Map<String, Object> params = getAllRequestParams(request);
            log.info("[通联支付回调] 接收到的参数: {}", params);
            
            // 2. 将参数转换为DTO对象
            AllinPayNotifyDto notifyDto = convertToNotifyDto(params);
            
            // 3. 验证必要参数
            if (validateRequiredParams(notifyDto)) {
                log.error("[通联支付回调] 必要参数缺失");
                return ResponseEntity.ok("FAIL");
            }
            
            // 4. 验证签名
            if (verifySignature(params, notifyDto.getSign())) {
                log.error("[通联支付回调] 签名验证失败");
                return ResponseEntity.ok("FAIL");
            }
            
            // 5. 处理业务逻辑（委托给Service处理，包含幂等性控制）
            boolean processResult = allinPayNotifyService.processPaymentNotify(notifyDto);
            
            if (processResult) {
                log.info("[通联支付回调] 处理成功, 订单号: {}, 交易状态: {}", 
                    notifyDto.getCusorderid(), notifyDto.getTrxstatus());
                return ResponseEntity.ok("success");
            } else {
                log.error("[通联支付回调] 业务处理失败, 订单号: {}", notifyDto.getCusorderid());
                return ResponseEntity.ok("FAIL");
            }
            
        } catch (Exception e) {
            log.error("[通联支付回调] 处理异常", e);
            return ResponseEntity.ok("FAIL");
        }
    }


    /**
     * 退款统一回调
     * @param request
     * @return
     */
    @PostMapping("/refund")
    public ResponseEntity<String> refundNotify(HttpServletRequest request) {
        log.info("[通联退款回调] 接收到退款结果通知");

        try {
            // 1. 获取所有请求参数（按照文档要求获取全量变量）
            Map<String, Object> params = getAllRequestParams(request);
            log.info("[通联退款回调] 接收到的参数: {}", params);

            // 2. 将参数转换为DTO对象
            AllinPayNotifyDto notifyDto = convertToNotifyDto(params);

            // 3. 验证必要参数
            if (validateRequiredParams(notifyDto)) {
                log.error("[通联退款回调] 必要参数缺失");
                return ResponseEntity.ok("FAIL");
            }

            // 4. 验证签名
            if (verifySignature(params, notifyDto.getSign())) {
                log.error("[通联退款回调] 签名验证失败");
                return ResponseEntity.ok("FAIL");
            }

            // 5. 处理业务逻辑（委托给Service处理，包含幂等性控制）
            boolean processResult = allinPayNotifyService.processRefundNotify(notifyDto);

            if (processResult) {
                log.info("[通联退款回调] 处理成功, 订单号: {}, 交易状态: {}",
                        notifyDto.getCusorderid(), notifyDto.getTrxstatus());
                return ResponseEntity.ok("success");
            } else {
                log.error("[通联退款回调] 业务处理失败, 订单号: {}", notifyDto.getCusorderid());
                return ResponseEntity.ok("FAIL");
            }

        } catch (Exception e) {
            log.error("[通联退款回调] 处理异常", e);
            return ResponseEntity.ok("FAIL");
        }
    }
    
    /**
     * 获取所有请求参数
     * 按照文档要求获取全量变量，遍历获取非空变量
     * 
     * @param request HTTP请求对象
     * @return 参数Map
     */
    private Map<String, Object> getAllRequestParams(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<>();
        
        // 遍历所有参数名
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            String paramValue = request.getParameter(paramName);
            
            // 只添加非空参数
            if (StrUtil.isNotBlank(paramValue)) {
                params.put(paramName, paramValue);
            }
        }
        
        return params;
    }
    
    /**
     * 将参数Map转换为通知DTO对象
     * 
     * @param params 参数Map
     * @return 通知DTO对象
     */
    private AllinPayNotifyDto convertToNotifyDto(Map<String, Object> params) {
        AllinPayNotifyDto notifyDto = new AllinPayNotifyDto();
        BeanUtil.fillBeanWithMap(params, notifyDto, false);
        return notifyDto;
    }
    
    /**
     * 验证必要参数
     * 
     * @param notifyDto 通知DTO对象
     * @return 验证结果
     */
    private boolean validateRequiredParams(AllinPayNotifyDto notifyDto) {
        return !StrUtil.isNotBlank(notifyDto.getAppid()) ||
                !StrUtil.isNotBlank(notifyDto.getTrxcode()) ||
                !StrUtil.isNotBlank(notifyDto.getTrxid()) ||
                !StrUtil.isNotBlank(notifyDto.getTrxstatus()) ||
                !StrUtil.isNotBlank(notifyDto.getCusid()) ||
                !StrUtil.isNotBlank(notifyDto.getCusorderid()) ||
                notifyDto.getTrxamt() == null;
    }
    
    /**
     * 验证签名
     * 
     * @param params 参数Map
     * @param sign 签名字符串
     * @return 验证结果
     */
    private boolean verifySignature(Map<String, Object> params, String sign) {
        try {
            // 如果没有配置公钥，跳过签名验证
            if (StrUtil.isBlank(allinPayConfig.getPublicKey())) {
                log.warn("[通联支付回调] 未配置公钥，跳过签名验证");
                return false;
            }
            
            // 如果没有签名，验证失败
            if (StrUtil.isBlank(sign)) {
                log.error("[通联支付回调] 签名为空");
                return true;
            }
            
            // 使用工具类验证签名
            return !AllinPaySignUtil.verifySign(params, sign, allinPayConfig.getPublicKey());
            
        } catch (Exception e) {
            log.error("[通联支付回调] 签名验证异常", e);
            return true;
        }
    }
    
}
