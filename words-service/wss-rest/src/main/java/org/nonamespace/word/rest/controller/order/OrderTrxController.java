package org.nonamespace.word.rest.controller.order;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.enums.BusinessType;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.order.OrderTrxExportDto;
import org.nonamespace.word.server.service.order.IOrdersTrxService;
import org.nonamespace.word.server.util.ExcelExportUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * 交易流水管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
@RestController
@RequestMapping("/order-trx")
@RequiredArgsConstructor
public class OrderTrxController extends BaseController {
    
    private final IOrdersTrxService ordersTrxService;

    /**
     * 导出交易流水数据
     */
    @PreAuthorize("@ss.hasPermi('order:trx:export')")
    @Log(title = "导出交易流水数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void exportOrderTrx(OrderTrxExportDto.ExportReq req, HttpServletResponse response) {
        try {
            log.info("开始导出交易流水数据: req={}", req);
            
            // 获取导出数据
            List<OrderTrxExportDto.ExportResp> exportData = ordersTrxService.exportOrderTrx(req);
            
            if (exportData.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_NO_CONTENT);
                return;
            }
            
            // 生成文件名
            String fileName = "交易流水_" + System.currentTimeMillis() + ".xlsx";
            
            // 创建表头映射
            LinkedHashMap<String, String> headers = ExcelExportUtil.createOrderTrxExportHeaders();
            
            // 导出Excel
            ExcelExportUtil.exportToResponse(response, fileName, "交易流水", headers, exportData);
            
            log.info("交易流水数据导出成功: fileName={}, count={}", fileName, exportData.size());
            
        } catch (Exception e) {
            log.error("导出交易流水数据失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}
