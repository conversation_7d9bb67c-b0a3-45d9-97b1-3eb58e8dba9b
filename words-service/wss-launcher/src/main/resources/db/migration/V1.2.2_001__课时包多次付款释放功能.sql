-- =====================================================
-- 课时包多次付款释放功能数据库变更
-- 创建时间：2025-01-08
-- 说明：支持订单多次付款逐步释放课时的功能
-- =====================================================

-- 1. 为student_course_hours表增加字段
ALTER TABLE words.student_course_hours ADD COLUMN IF NOT EXISTS order_id VARCHAR(64);
ALTER TABLE words.student_course_hours ADD COLUMN IF NOT EXISTS source_type VARCHAR(20) NOT NULL DEFAULT '手动';
ALTER TABLE words.student_course_hours ADD COLUMN IF NOT EXISTS frozen_purchased_hours DECIMAL(10,2) DEFAULT 0;
ALTER TABLE words.student_course_hours ADD COLUMN IF NOT EXISTS frozen_gift_hours DECIMAL(10,2) DEFAULT 0;

-- 2. 创建课时释放记录表
CREATE TABLE IF NOT EXISTS words.student_course_hours_release (
    id VARCHAR(64) NOT NULL PRIMARY KEY,
    course_hours_id VARCHAR(64) NOT NULL,
    order_id VARCHAR(64) NOT NULL,
    order_trx_id VARCHAR(64) NOT NULL,
    student_id VARCHAR(64) NOT NULL,
    released_purchased_hours DECIMAL(10,2) NOT NULL DEFAULT 0,
    released_gift_hours DECIMAL(10,2) NOT NULL DEFAULT 0,
    payment_ratio DECIMAL(5,4) NOT NULL,
    payment_amount BIGINT NOT NULL,
    total_order_amount BIGINT NOT NULL,
    release_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    release_type VARCHAR(20) NOT NULL,
    remark TEXT,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(64) NOT NULL DEFAULT 'system',
    update_by VARCHAR(64) NOT NULL DEFAULT 'system',
    deleted BOOLEAN NOT NULL DEFAULT FALSE
);

-- 3. 设置表所有者
ALTER TABLE words.student_course_hours_release OWNER TO main;

-- 4. 创建更新时间触发器
CREATE TRIGGER update_student_course_hours_release_timestamp 
    BEFORE UPDATE ON words.student_course_hours_release
    FOR EACH ROW
    EXECUTE PROCEDURE words.update_timestamp();

-- 5. 创建索引
CREATE INDEX IF NOT EXISTS idx_student_course_hours_order_id ON words.student_course_hours(order_id);
CREATE INDEX IF NOT EXISTS idx_student_course_hours_source_type ON words.student_course_hours(source_type);

CREATE INDEX IF NOT EXISTS idx_release_course_hours_id ON words.student_course_hours_release(course_hours_id);
CREATE INDEX IF NOT EXISTS idx_release_order_id ON words.student_course_hours_release(order_id);
CREATE INDEX IF NOT EXISTS idx_release_order_trx_id ON words.student_course_hours_release(order_trx_id);
CREATE UNIQUE INDEX IF NOT EXISTS uk_release_order_trx ON words.student_course_hours_release(order_trx_id) WHERE deleted = FALSE;

-- 6. 添加字段注释
COMMENT ON COLUMN words.student_course_hours.order_id IS '关联的订单ID（一个课时包对应一个订单）';
COMMENT ON COLUMN words.student_course_hours.source_type IS '课时来源：手动、订单、导入、首课';
COMMENT ON COLUMN words.student_course_hours.frozen_purchased_hours IS '冻结的购买课时数';
COMMENT ON COLUMN words.student_course_hours.frozen_gift_hours IS '冻结的赠送课时数';

-- 7. 添加表和字段注释
COMMENT ON TABLE words.student_course_hours_release IS '学生课时释放记录表';
COMMENT ON COLUMN words.student_course_hours_release.id IS '主键ID';
COMMENT ON COLUMN words.student_course_hours_release.course_hours_id IS '课时包ID';
COMMENT ON COLUMN words.student_course_hours_release.order_id IS '订单ID';
COMMENT ON COLUMN words.student_course_hours_release.order_trx_id IS '订单交易流水ID';
COMMENT ON COLUMN words.student_course_hours_release.student_id IS '学生ID';
COMMENT ON COLUMN words.student_course_hours_release.released_purchased_hours IS '释放的购买课时数';
COMMENT ON COLUMN words.student_course_hours_release.released_gift_hours IS '释放的赠送课时数';
COMMENT ON COLUMN words.student_course_hours_release.payment_ratio IS '支付比例';
COMMENT ON COLUMN words.student_course_hours_release.payment_amount IS '支付金额(分)';
COMMENT ON COLUMN words.student_course_hours_release.total_order_amount IS '订单总金额(分)';
COMMENT ON COLUMN words.student_course_hours_release.release_time IS '释放时间';
COMMENT ON COLUMN words.student_course_hours_release.release_type IS '释放类型：部分释放、最终释放';
COMMENT ON COLUMN words.student_course_hours_release.remark IS '备注';
COMMENT ON COLUMN words.student_course_hours_release.create_time IS '创建时间';
COMMENT ON COLUMN words.student_course_hours_release.update_time IS '更新时间';
COMMENT ON COLUMN words.student_course_hours_release.create_by IS '创建人';
COMMENT ON COLUMN words.student_course_hours_release.update_by IS '更新人';
COMMENT ON COLUMN words.student_course_hours_release.deleted IS '删除标志';

-- 8. 更新现有数据的source_type字段
UPDATE words.student_course_hours SET source_type = '手动' WHERE source_type IS NULL OR source_type = '';
