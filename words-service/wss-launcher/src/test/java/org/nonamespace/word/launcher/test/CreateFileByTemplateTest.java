package org.nonamespace.word.launcher.test;

import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.nonamespace.word.thirdpart.esign.model.CreateFileByTemplateInput;
import org.nonamespace.word.thirdpart.esign.model.CreateFileByTemplateOutput;
import org.nonamespace.word.thirdpart.esign.service.IESignContractsFileService;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * e签宝填写模板生成文件接口测试
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@SpringBootTest
public class CreateFileByTemplateTest {

    @Resource
    private IESignContractsFileService eSignService;

    @Test
    public void testCreateFileByTemplate() {
        try {
            // 创建填写模板生成文件请求
            CreateFileByTemplateInput input = new CreateFileByTemplateInput();
            input.setDocTemplateId("3706ab4b0c864f6b99c70bd289585236");
            input.setFileName("北大军哥一对一陪练协议.docx");

            // 设置模板控件填充数据
            List<CreateFileByTemplateInput.ComponentInfo> componentInfos = new ArrayList<>();
            
            CreateFileByTemplateInput.ComponentInfo 签订合同日期 = new CreateFileByTemplateInput.ComponentInfo("77d5c3be7dc74e8a82df683632056f5a", "2025年08月03日");
            CreateFileByTemplateInput.ComponentInfo 学生姓名 = new CreateFileByTemplateInput.ComponentInfo("09ad28b2db0148349bfef513f34e2afd", "小阿三");
            CreateFileByTemplateInput.ComponentInfo 企业章 = new CreateFileByTemplateInput.ComponentInfo("0a63365dc85c44cba4b0ef767adf05fd", "企业章");
            CreateFileByTemplateInput.ComponentInfo 骑缝签署区 = new CreateFileByTemplateInput.ComponentInfo("e0f34bebe4c948dbaab7fae6bf9a70e2", "骑缝签署区");
            CreateFileByTemplateInput.ComponentInfo 个人章_签名 = new CreateFileByTemplateInput.ComponentInfo("0ed11368a9f84defb9c2e92e774902ca", "个人章_签名");
            CreateFileByTemplateInput.ComponentInfo 家长姓名 = new CreateFileByTemplateInput.ComponentInfo("9dc5ae0c5fcf481887c7d8087161bfd2", "小阿三家长");
            CreateFileByTemplateInput.ComponentInfo 家长电话号码 = new CreateFileByTemplateInput.ComponentInfo("a7217d2ad00c453e9e567440733d316b", "13333333333");
            CreateFileByTemplateInput.ComponentInfo 课程名称 = new CreateFileByTemplateInput.ComponentInfo("db25d638d29a4bffabcfce1353486b7f", "暑期一对一私教");
            CreateFileByTemplateInput.ComponentInfo 大写金额 = new CreateFileByTemplateInput.ComponentInfo("e5f4f072edd440c1aff7c78e3551870a", "壹万陆仟");
            CreateFileByTemplateInput.ComponentInfo 小写金额 = new CreateFileByTemplateInput.ComponentInfo("0afc61be608f485c87da6d5e186d51a6", "一万六千");
            CreateFileByTemplateInput.ComponentInfo 正价课课时数 = new CreateFileByTemplateInput.ComponentInfo("87cab392f5e745cb88b437b3ff02387c", "500");
            CreateFileByTemplateInput.ComponentInfo 赠送课时数 = new CreateFileByTemplateInput.ComponentInfo("91255dfa7300434cb6068c3ccb24741f", "100");
            CreateFileByTemplateInput.ComponentInfo 单次课价格 = new CreateFileByTemplateInput.ComponentInfo("0711d32541ff4cf19c10a0bedbdf1fa0", "50.00");

            componentInfos.add(签订合同日期);
            componentInfos.add(学生姓名);
            componentInfos.add(企业章);
            componentInfos.add(骑缝签署区);
            componentInfos.add(个人章_签名);
            componentInfos.add(家长姓名);
            componentInfos.add(家长电话号码);
            componentInfos.add(课程名称);
            componentInfos.add(大写金额);
            componentInfos.add(小写金额);
            componentInfos.add(正价课课时数);
            componentInfos.add(赠送课时数);
            componentInfos.add(单次课价格);

            input.setComponents(componentInfos);

            // 调用接口
            CreateFileByTemplateOutput result = eSignService.createFileByTemplate(input);
            System.out.println("填写模板生成文件测试返回：" + result);

        } catch (Exception e) {
            System.err.println("填写模板生成文件测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

//    @Test
//    public void testCreateFileByTemplateWithMultipleFields() {
//        try {
//            // 创建包含更多控件类型的测试
//            CreateFileByTemplateInput input = new CreateFileByTemplateInput();
//            input.setTemplateId("complex_template_id_789012");
//            input.setFileName("复杂合同模板测试.pdf");
//
//            // 设置多种类型的控件数据
//            List<CreateFileByTemplateInput.SimpleFormField> simpleFormFields = new ArrayList<>();
//
//            // 甲方信息
//            simpleFormFields.add(createField("party_a_name", "北京测试科技有限公司"));
//            simpleFormFields.add(createField("party_a_legal_person", "张三"));
//            simpleFormFields.add(createField("party_a_phone", "010-12345678"));
//            simpleFormFields.add(createField("party_a_address", "北京市海淀区中关村大街1号"));
//
//            // 乙方信息
//            simpleFormFields.add(createField("party_b_name", "上海合作伙伴有限公司"));
//            simpleFormFields.add(createField("party_b_legal_person", "李四"));
//            simpleFormFields.add(createField("party_b_phone", "021-87654321"));
//            simpleFormFields.add(createField("party_b_address", "上海市浦东新区陆家嘴金融区"));
//
//            // 合同条款
//            simpleFormFields.add(createField("contract_title", "技术服务合作协议"));
//            simpleFormFields.add(createField("contract_amount", "500000.00"));
//            simpleFormFields.add(createField("contract_currency", "人民币"));
//            simpleFormFields.add(createField("start_date", "2024-01-01"));
//            simpleFormFields.add(createField("end_date", "2024-12-31"));
//            simpleFormFields.add(createField("payment_method", "银行转账"));
//            simpleFormFields.add(createField("service_content", "提供软件开发和技术咨询服务"));
//
//            input.setSimpleFormFields(simpleFormFields);
//
//            // 调用接口
//            CreateFileByTemplateOutput result = eSignService.createFileByTemplate(input);
//
//            System.out.println("=== 复杂模板填写测试结果 ===");
//            System.out.println("文件ID: " + result.getFileId());
//            System.out.println("文件名称: " + result.getFileName());
//            System.out.println("文件大小: " + result.getFileSize() + " bytes");
//            System.out.println("下载链接: " + result.getDownloadUrl());
//
//        } catch (Exception e) {
//            System.err.println("复杂模板填写测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testCreateFileByTemplateWithEmptyFields() {
//        try {
//            // 测试空字段处理
//            CreateFileByTemplateInput input = new CreateFileByTemplateInput();
//            input.setTemplateId("simple_template_id_345678");
//            input.setFileName("简单测试合同.pdf");
//
//            // 只设置必要字段
//            List<CreateFileByTemplateInput.SimpleFormField> simpleFormFields = new ArrayList<>();
//            simpleFormFields.add(createField("required_field", "必填字段值"));
//            simpleFormFields.add(createField("optional_field", ""));
//
//            input.setSimpleFormFields(simpleFormFields);
//
//            // 调用接口
//            CreateFileByTemplateOutput result = eSignService.createFileByTemplate(input);
//
//            System.out.println("=== 空字段处理测试结果 ===");
//            System.out.println("文件ID: " + result.getFileId());
//            System.out.println("文件名称: " + result.getFileName());
//            System.out.println("文件大小: " + result.getFileSize() + " bytes");
//            System.out.println("下载链接: " + result.getDownloadUrl());
//
//        } catch (Exception e) {
//            System.err.println("空字段处理测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testCreateFileByTemplateWithSpecialCharacters() {
//        try {
//            // 测试特殊字符处理
//            CreateFileByTemplateInput input = new CreateFileByTemplateInput();
//            input.setTemplateId("special_template_id_567890");
//            input.setFileName("特殊字符测试合同.pdf");
//
//            // 包含特殊字符的字段
//            List<CreateFileByTemplateInput.SimpleFormField> simpleFormFields = new ArrayList<>();
//            simpleFormFields.add(createField("company_name", "测试&公司（有限责任）"));
//            simpleFormFields.add(createField("amount_text", "￥100,000.00元整"));
//            simpleFormFields.add(createField("description", "包含换行\n和制表符\t的描述内容"));
//            simpleFormFields.add(createField("email", "<EMAIL>"));
//            simpleFormFields.add(createField("percentage", "85%"));
//
//            input.setSimpleFormFields(simpleFormFields);
//
//            // 调用接口
//            CreateFileByTemplateOutput result = eSignService.createFileByTemplate(input);
//
//            System.out.println("=== 特殊字符处理测试结果 ===");
//            System.out.println("文件ID: " + result.getFileId());
//            System.out.println("文件名称: " + result.getFileName());
//            System.out.println("文件大小: " + result.getFileSize() + " bytes");
//            System.out.println("下载链接: " + result.getDownloadUrl());
//
//        } catch (Exception e) {
//            System.err.println("特殊字符处理测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 创建表单字段的辅助方法
//     *
//     * @param key   字段key
//     * @param value 字段值
//     * @return 表单字段对象
//     */
//    private CreateFileByTemplateInput.SimpleFormField createField(String key, String value) {
//        CreateFileByTemplateInput.SimpleFormField field = new CreateFileByTemplateInput.SimpleFormField();
//        field.setKey(key);
//        field.setValue(value);
//        return field;
//    }
//
//    /**
//     * 测试参数校验
//     */
//    @Test
//    public void testCreateFileByTemplateValidation() {
//        try {
//            // 测试空模板ID
//            CreateFileByTemplateInput input = new CreateFileByTemplateInput();
//            input.setTemplateId("");
//            input.setFileName("测试文件.pdf");
//            input.setSimpleFormFields(new ArrayList<>());
//
//            eSignService.createFileByTemplate(input);
//
//        } catch (Exception e) {
//            System.out.println("=== 参数校验测试结果 ===");
//            System.out.println("预期异常: " + e.getMessage());
//        }
//
//        try {
//            // 测试空文件名
//            CreateFileByTemplateInput input = new CreateFileByTemplateInput();
//            input.setTemplateId("test_template_id");
//            input.setFileName("");
//            input.setSimpleFormFields(new ArrayList<>());
//
//            eSignService.createFileByTemplate(input);
//
//        } catch (Exception e) {
//            System.out.println("空文件名校验异常: " + e.getMessage());
//        }
//
//        try {
//            // 测试null参数
//            eSignService.createFileByTemplate(null);
//
//        } catch (Exception e) {
//            System.out.println("null参数校验异常: " + e.getMessage());
//        }
//    }
}