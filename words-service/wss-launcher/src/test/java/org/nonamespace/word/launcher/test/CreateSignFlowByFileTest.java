package org.nonamespace.word.launcher.test;

import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.nonamespace.word.thirdpart.esign.model.CreateSignFlowByFileInput;
import org.nonamespace.word.thirdpart.esign.model.CreateSignFlowByFileOutput;
import org.nonamespace.word.thirdpart.esign.service.IESignFlowService;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * e签宝基于文件发起签署接口测试
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@SpringBootTest
public class CreateSignFlowByFileTest {

    @Resource
    private IESignFlowService eSignService;

    @Test
    public void testCreateSignFlowByFile() {
        try {
            // 创建基于文件发起签署请求
            CreateSignFlowByFileInput input = new CreateSignFlowByFileInput();

            // 设置文档信息
            List<CreateSignFlowByFileInput.DocInfo> docs = new ArrayList<>();
            CreateSignFlowByFileInput.DocInfo doc = new CreateSignFlowByFileInput.DocInfo();
            doc.setFileId("41f624ba500944d4b606094c7bbfe897");
            doc.setFileName("北大军哥一对一陪练协议.docx.pdf");
            doc.setNeededPwd(false);
            doc.setFileEditPwd(""); // 文件编辑密码（如果需要的话）
            doc.setContractBizTypeId(""); // 合同业务类型ID
            doc.setOrder(1);
            docs.add(doc);
            input.setDocs(docs);

            // 设置签署流程配置
            CreateSignFlowByFileInput.SignFlowConfig config = new CreateSignFlowByFileInput.SignFlowConfig();
            config.setSignFlowTitle("【测试】基于文件发起的签署流程");
            config.setAutoStart(true); // 自动开启签署流程
            config.setAutoFinish(true); // 自动完结签署流程
            config.setIdentityVerify(false); // 不进行身份校验
            config.setSignFlowExpireTime(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000); // 30天后过期
            config.setNotifyUrl("https://your-domain.com/esign/callback"); // 回调通知地址
            
            // 设置通知配置
            CreateSignFlowByFileInput.NoticeConfig noticeConfig = new CreateSignFlowByFileInput.NoticeConfig();
            noticeConfig.setNoticeTypes("1,2"); // 短信和邮件通知
            noticeConfig.setExamineNotice(false); // 不通知审批人员
            config.setNoticeConfig(noticeConfig);
            
            // 设置重定向配置
            CreateSignFlowByFileInput.SignFlowConfig.RedirectConfig redirectConfig = new CreateSignFlowByFileInput.SignFlowConfig.RedirectConfig();
            redirectConfig.setRedirectUrl("https://your-domain.com/sign/success");
            redirectConfig.setRedirectDelayTime(3); // 3秒后跳转
            config.setRedirectConfig(redirectConfig);
            
            // 设置合同配置
            CreateSignFlowByFileInput.SignFlowConfig.ContractConfig contractConfig = new CreateSignFlowByFileInput.SignFlowConfig.ContractConfig();
            contractConfig.setContractSecrecy(1); // 合同不保密
            contractConfig.setAllowToRescind(true); // 允许发起解约
            contractConfig.setContractExpirationDate(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000); // 1年后合同到期
            config.setContractConfig(contractConfig);
            
            input.setSignFlowConfig(config);

            // 设置签署人信息
            List<CreateSignFlowByFileInput.SignerInfo> signers = new ArrayList<>();
            CreateSignFlowByFileInput.SignerInfo signer = new CreateSignFlowByFileInput.SignerInfo();
            signer.setSignerType(0); // 个人签署方

            // 设置个人签署方信息
            CreateSignFlowByFileInput.PsnSignerInfo psnSignerInfo = new CreateSignFlowByFileInput.PsnSignerInfo();
            psnSignerInfo.setPsnAccount("<EMAIL>"); // 个人账号标识
            
            // 设置个人身份信息
            CreateSignFlowByFileInput.PsnSignerInfo.PsnInfo psnInfo = new CreateSignFlowByFileInput.PsnSignerInfo.PsnInfo();
            psnInfo.setPsnName("张三"); // 个人姓名
            psnInfo.setPsnIDCardNum("110101199001011234"); // 身份证号
            psnInfo.setPsnIDCardType("CRED_PSN_CH_IDCARD"); // 身份证类型
            psnInfo.setPsnMobile("***********"); // 手机号
            psnInfo.setBankCardNum(""); // 银行卡号（可选）
            psnSignerInfo.setPsnInfo(psnInfo);
            
            signer.setPsnSignerInfo(psnSignerInfo);

            // 设置签署区信息
            List<CreateSignFlowByFileInput.SignFieldInfo> signFields = new ArrayList<>();
            CreateSignFlowByFileInput.SignFieldInfo signField = new CreateSignFlowByFileInput.SignFieldInfo();
            signField.setFileId("41f624ba500944d4b606094c7bbfe897"); // 对应文档的文件ID
            signField.setSignFieldType(0); // 签章区
            // 自由模式下不设置mustSign字段

            // 设置签章区配置
            CreateSignFlowByFileInput.NormalSignFieldConfig fieldConfig = new CreateSignFlowByFileInput.NormalSignFieldConfig();
            fieldConfig.setFreeMode(false); // 自由模式，签署人可自由选择签署位置
            fieldConfig.setAutoSign(false); // 不自动签署
            fieldConfig.setMovableSignField(false); // 允许移动签署区
            fieldConfig.setAssignedSealId(""); // 指定印章ID（可选）
            signField.setNormalSignFieldConfig(fieldConfig);
            
            signFields.add(signField);
            signer.setSignFields(signFields);
            
            // 设置签署方通知配置
            CreateSignFlowByFileInput.NoticeConfig signerNoticeConfig = new CreateSignFlowByFileInput.NoticeConfig();
            signerNoticeConfig.setNoticeTypes("1,5"); // 短信和微信通知
            signerNoticeConfig.setExamineNotice(false); // 不通知审批人员
            signer.setNoticeConfig(signerNoticeConfig);

            signers.add(signer);
            input.setSigners(signers);

            // 打印请求参数（用于调试）
            System.out.println("=== 请求参数 ===");
            System.out.println("文档数量: " + input.getDocs().size());
            System.out.println("签署流程标题: " + input.getSignFlowConfig().getSignFlowTitle());
            System.out.println("签署人数量: " + input.getSigners().size());
            System.out.println("第一个签署人: " + input.getSigners().get(0).getPsnSignerInfo().getPsnInfo().getPsnName());
            
            // 调用接口
            CreateSignFlowByFileOutput result = eSignService.createSignFlowByFile(input);

            // 输出测试结果
            System.out.println("\n=== 基于文件发起签署测试结果 ===");
            System.out.println("✅ 测试成功！");
            System.out.println("签署流程ID: " + result.getData().getSignFlowId());

            // 验证返回结果
            assert result.getData().getSignFlowId() != null && !result.getData().getSignFlowId().isEmpty() : "签署流程ID不能为空";

            System.out.println("\n🎉 所有断言验证通过！");

        } catch (Exception e) {
            System.err.println("❌ 基于文件发起签署测试失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("测试失败", e);
        }
    }

//    @Test
//    public void testCreateSignFlowByFileWithAttachments() {
//        try {
//            // 创建基于文件发起签署请求（包含附件）
//            CreateSignFlowByFileInput input = new CreateSignFlowByFileInput();
//
//            // 设置文档信息
//            List<CreateSignFlowByFileInput.DocInfo> docs = new ArrayList<>();
//            CreateSignFlowByFileInput.DocInfo doc = new CreateSignFlowByFileInput.DocInfo();
//            doc.setFileId("main_file_id_123456");
//            doc.setFileName("主合同文件.pdf");
//            doc.setNeededPwd(false);
//            doc.setOrder(1);
//            docs.add(doc);
//            input.setDocs(docs);
//
//            // 设置附件信息
//            List<CreateSignFlowByFileInput.AttachmentInfo> attachments = new ArrayList<>();
//            CreateSignFlowByFileInput.AttachmentInfo attachment = new CreateSignFlowByFileInput.AttachmentInfo();
//            attachment.setFileId("attachment_file_id_123456");
//            attachment.setFileName("附件文档.pdf");
//            attachments.add(attachment);
//            input.setAttachments(attachments);
//
//            // 设置签署流程配置
//            CreateSignFlowByFileInput.SignFlowConfig config = new CreateSignFlowByFileInput.SignFlowConfig();
//            config.setSignFlowTitle("带附件的合同签署流程");
//            config.setAutoStart(true);
//            config.setAutoFinish(false);
//            config.setIdentityVerify(true);
//            config.setSignFlowExpireTime(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000); // 30天后过期
//            config.setNotifyUrl("https://example.com/callback");
//
//            // 设置通知配置
//            CreateSignFlowByFileInput.NoticeConfig noticeConfig = new CreateSignFlowByFileInput.NoticeConfig();
//            noticeConfig.setNoticeTypes("1,2"); // 短信和邮件通知
//            noticeConfig.setExamineNotice(true);
//            config.setNoticeConfig(noticeConfig);
//
//            // 设置重定向配置
//            CreateSignFlowByFileInput.SignFlowConfig.RedirectConfig redirectConfig = new CreateSignFlowByFileInput.SignFlowConfig.RedirectConfig();
//            redirectConfig.setRedirectUrl("https://example.com/success");
//            redirectConfig.setRedirectDelayTime(0); // 立即跳转
//            config.setRedirectConfig(redirectConfig);
//
//            // 设置合同配置
//            CreateSignFlowByFileInput.SignFlowConfig.ContractConfig contractConfig = new CreateSignFlowByFileInput.SignFlowConfig.ContractConfig();
//            contractConfig.setContractSecrecy(2); // 合同全部保密
//            contractConfig.setAllowToRescind(false); // 不允许解约
//            contractConfig.setContractExpirationDate(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000); // 1年后到期
//            config.setContractConfig(contractConfig);
//
//            input.setSignFlowConfig(config);
//
//            // 设置签署人信息
//            List<CreateSignFlowByFileInput.SignerInfo> signers = new ArrayList<>();
//            CreateSignFlowByFileInput.SignerInfo signer = new CreateSignFlowByFileInput.SignerInfo();
//            signer.setSignerType(0); // 个人签署方
//
//            // 设置个人签署方信息
//            CreateSignFlowByFileInput.PsnSignerInfo psnSignerInfo = new CreateSignFlowByFileInput.PsnSignerInfo();
//            psnSignerInfo.setPsnAccount("<EMAIL>");
//
//            // 设置个人身份信息
//            CreateSignFlowByFileInput.PsnSignerInfo.PsnInfo psnInfo = new CreateSignFlowByFileInput.PsnSignerInfo.PsnInfo();
//            psnInfo.setPsnName("测试用户2");
//            psnInfo.setPsnIDCardNum("110101199002022345");
//            psnInfo.setPsnIDCardType("CRED_PSN_CH_IDCARD");
//            psnInfo.setPsnMobile("***********");
//            psnInfo.setBankCardNum("6222021234567890123");
//            psnSignerInfo.setPsnInfo(psnInfo);
//
//            signer.setPsnSignerInfo(psnSignerInfo);
//
//            // 设置签署区信息
//            List<CreateSignFlowByFileInput.SignFieldInfo> signFields = new ArrayList<>();
//            CreateSignFlowByFileInput.SignFieldInfo signField = new CreateSignFlowByFileInput.SignFieldInfo();
//            signField.setFileId("main_file_id_123456");
//            signField.setSignFieldType(0); // 签章区
//            signField.setMustSign(true);
//
//            // 设置签章区配置
//            CreateSignFlowByFileInput.NormalSignFieldConfig fieldConfig = new CreateSignFlowByFileInput.NormalSignFieldConfig();
//            fieldConfig.setFreeMode(false);
//            fieldConfig.setAutoSign(false);
//            fieldConfig.setMovableSignField(false);
//            fieldConfig.setAssignedSealId("seal_id_123456"); // 指定印章ID
//            signField.setNormalSignFieldConfig(fieldConfig);
//
//            signFields.add(signField);
//            signer.setSignFields(signFields);
//
//            // 设置签署方通知配置
//            CreateSignFlowByFileInput.NoticeConfig signerNoticeConfig = new CreateSignFlowByFileInput.NoticeConfig();
//            signerNoticeConfig.setNoticeTypes("1,2,5"); // 短信、邮件和微信通知
//            signerNoticeConfig.setExamineNotice(false);
//            signer.setNoticeConfig(signerNoticeConfig);
//
//            signers.add(signer);
//            input.setSigners(signers);
//
//            // 调用接口
//            CreateSignFlowByFileOutput result = eSignService.createSignFlowByFile(input);
//
//            System.out.println("=== 带附件的基于文件发起签署测试结果 ===");
//            System.out.println("签署流程ID: " + result.getData().getSignFlowId());
//
//        } catch (Exception e) {
//            System.err.println("带附件的基于文件发起签署测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
    
//    @Test
//    public void testCreateSignFlowByFileWithMultipleSigners() {
//        try {
//            // 创建多签署人的签署流程测试
//            CreateSignFlowByFileInput input = new CreateSignFlowByFileInput();
//
//            // 设置文档信息
//            List<CreateSignFlowByFileInput.DocInfo> docs = new ArrayList<>();
//            CreateSignFlowByFileInput.DocInfo doc = new CreateSignFlowByFileInput.DocInfo();
//            doc.setFileId("multi_signer_file_id_123456");
//            doc.setFileName("多方签署合同.pdf");
//            doc.setNeededPwd(false);
//            doc.setOrder(1);
//            docs.add(doc);
//            input.setDocs(docs);
//
//            // 设置签署流程配置
//            CreateSignFlowByFileInput.SignFlowConfig config = new CreateSignFlowByFileInput.SignFlowConfig();
//            config.setSignFlowTitle("多方签署合同流程");
//            config.setAutoStart(true);
//            config.setAutoFinish(true);
//            config.setIdentityVerify(true);
//            config.setSignFlowExpireTime(System.currentTimeMillis() + 7L * 24 * 60 * 60 * 1000); // 7天后过期
//            input.setSignFlowConfig(config);
//
//            // 设置多个签署人信息
//            List<CreateSignFlowByFileInput.SignerInfo> signers = new ArrayList<>();
//
//            // 第一个签署人
//            CreateSignFlowByFileInput.SignerInfo signer1 = new CreateSignFlowByFileInput.SignerInfo();
//            signer1.setSignerType(0); // 个人签署方
//
//            CreateSignFlowByFileInput.PsnSignerInfo psnSignerInfo1 = new CreateSignFlowByFileInput.PsnSignerInfo();
//            psnSignerInfo1.setPsnAccount("<EMAIL>");
//
//            CreateSignFlowByFileInput.PsnSignerInfo.PsnInfo psnInfo1 = new CreateSignFlowByFileInput.PsnSignerInfo.PsnInfo();
//            psnInfo1.setPsnName("签署人一");
//            psnInfo1.setPsnIDCardNum("110101199001011111");
//            psnInfo1.setPsnIDCardType("CRED_PSN_CH_IDCARD");
//            psnInfo1.setPsnMobile("***********");
//            psnSignerInfo1.setPsnInfo(psnInfo1);
//            signer1.setPsnSignerInfo(psnSignerInfo1);
//
//            // 第一个签署人的签署区
//            List<CreateSignFlowByFileInput.SignFieldInfo> signFields1 = new ArrayList<>();
//            CreateSignFlowByFileInput.SignFieldInfo signField1 = new CreateSignFlowByFileInput.SignFieldInfo();
//            signField1.setFileId("multi_signer_file_id_123456");
//            signField1.setSignFieldType(0);
//            signField1.setMustSign(true);
//
//            CreateSignFlowByFileInput.NormalSignFieldConfig fieldConfig1 = new CreateSignFlowByFileInput.NormalSignFieldConfig();
//            fieldConfig1.setFreeMode(true);
//            fieldConfig1.setAutoSign(false);
//            fieldConfig1.setMovableSignField(true);
//            signField1.setNormalSignFieldConfig(fieldConfig1);
//
//            signFields1.add(signField1);
//            signer1.setSignFields(signFields1);
//
//            // 第二个签署人
//            CreateSignFlowByFileInput.SignerInfo signer2 = new CreateSignFlowByFileInput.SignerInfo();
//            signer2.setSignerType(0); // 个人签署方
//
//            CreateSignFlowByFileInput.PsnSignerInfo psnSignerInfo2 = new CreateSignFlowByFileInput.PsnSignerInfo();
//            psnSignerInfo2.setPsnAccount("<EMAIL>");
//
//            CreateSignFlowByFileInput.PsnSignerInfo.PsnInfo psnInfo2 = new CreateSignFlowByFileInput.PsnSignerInfo.PsnInfo();
//            psnInfo2.setPsnName("签署人二");
//            psnInfo2.setPsnIDCardNum("110101199002022222");
//            psnInfo2.setPsnIDCardType("CRED_PSN_CH_IDCARD");
//            psnInfo2.setPsnMobile("***********");
//            psnSignerInfo2.setPsnInfo(psnInfo2);
//            signer2.setPsnSignerInfo(psnSignerInfo2);
//
//            // 第二个签署人的签署区
//            List<CreateSignFlowByFileInput.SignFieldInfo> signFields2 = new ArrayList<>();
//            CreateSignFlowByFileInput.SignFieldInfo signField2 = new CreateSignFlowByFileInput.SignFieldInfo();
//            signField2.setFileId("multi_signer_file_id_123456");
//            signField2.setSignFieldType(0);
//            signField2.setMustSign(true);
//
//            CreateSignFlowByFileInput.NormalSignFieldConfig fieldConfig2 = new CreateSignFlowByFileInput.NormalSignFieldConfig();
//            fieldConfig2.setFreeMode(true);
//            fieldConfig2.setAutoSign(false);
//            fieldConfig2.setMovableSignField(true);
//            signField2.setNormalSignFieldConfig(fieldConfig2);
//
//            signFields2.add(signField2);
//            signer2.setSignFields(signFields2);
//
//            signers.add(signer1);
//            signers.add(signer2);
//            input.setSigners(signers);
//
//            // 调用接口
//            CreateSignFlowByFileOutput result = eSignService.createSignFlowByFile(input);
//
//            System.out.println("=== 多方签署合同测试结果 ===");
//            System.out.println("签署流程ID: " + result.getSignFlowId());
//            System.out.println("状态: " + result.getSignFlowStatus());
//            System.out.println("创建时间: " + result.getSignFlowCreateTime());
//            System.out.println("截止时间: " + result.getSignFlowExpireTime());
//
//        } catch (Exception e) {
//            System.err.println("多方签署合同测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
}